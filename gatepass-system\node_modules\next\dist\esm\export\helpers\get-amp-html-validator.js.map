{"version": 3, "sources": ["../../../src/export/helpers/get-amp-html-validator.ts"], "sourcesContent": ["import AmpHtmlValidator, {\n  type Validator,\n} from 'next/dist/compiled/amphtml-validator'\n\nconst instancePromises = new Map<string | undefined, Promise<Validator>>()\n\n/**\n * `AmpHtmlValidator.getInstance` is buggy when multiple calls to getInstance occur in parallel.\n * This wrapper is a workaround for that.\n *\n * `AmpHtmlValidator.getInstance(validatorPath)` attempts to re-use the instances across multiple calls\n * by stashing existing instances in `instanceByValidatorJs`.\n * However, when creating a fresh instance, it is added to `instanceByValidatorJs` before `instance.init()` is finished:\n * https://github.com/ampproject/amphtml/blob/0c8eaba73ca8f5c462a642fa91901a29e6304f6e/validator/js/nodejs/index.js#L309-L313\n *\n * which means that, if a concurrent call to `AmpHtmlValidator.getInstance(validatorPath)` happens before the original `init()` is done,\n * then we'll get back an uninitialized or partially initialized instance:\n * https://github.com/ampproject/amphtml/blob/0c8eaba73ca8f5c462a642fa91901a29e6304f6e/validator/js/nodejs/index.js#L292-L294\n *\n * And, since `instance.init()` is the part that handles loading the webassembly code, this race results in\n * the dreaded \"AssertionError: Assertion failed: WebAssembly is uninitialized\" error.\n * As a workaround, we properly dedupe instance creation on our own.\n * */\nexport function getAmpValidatorInstance(\n  validatorPath: string | undefined\n): Promise<Validator> {\n  let promise = instancePromises.get(validatorPath)\n  if (!promise) {\n    // NOTE: if `validatorPath` is undefined, `AmpHtmlValidator` will load the code from its default URL\n    promise = AmpHtmlValidator.getInstance(validatorPath)\n    instancePromises.set(validatorPath, promise)\n  }\n  return promise\n}\n\nexport function getBundledAmpValidatorFilepath() {\n  return require.resolve(\n    'next/dist/compiled/amphtml-validator/validator_wasm.js'\n  )\n}\n"], "names": ["AmpHtmlValidator", "instancePromises", "Map", "getAmpValidatorInstance", "validatorPath", "promise", "get", "getInstance", "set", "getBundledAmpValidatorFilepath", "require", "resolve"], "mappings": "AAAA,OAAOA,sBAEA,uCAAsC;AAE7C,MAAMC,mBAAmB,IAAIC;AAE7B;;;;;;;;;;;;;;;;GAgBG,GACH,OAAO,SAASC,wBACdC,aAAiC;IAEjC,IAAIC,UAAUJ,iBAAiBK,GAAG,CAACF;IACnC,IAAI,CAACC,SAAS;QACZ,oGAAoG;QACpGA,UAAUL,iBAAiBO,WAAW,CAACH;QACvCH,iBAAiBO,GAAG,CAACJ,eAAeC;IACtC;IACA,OAAOA;AACT;AAEA,OAAO,SAASI;IACd,OAAOC,QAAQC,OAAO,CACpB;AAEJ", "ignoreList": [0]}