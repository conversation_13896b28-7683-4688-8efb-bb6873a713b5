import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

function verifyToken(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('No token provided');
  }

  const token = authHeader.substring(7);
  return jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const decoded = verifyToken(request);
    const resolvedParams = await params;
    const gatepassId = resolvedParams.id;

    // Get gatepass details
    const gatepass = await prisma.gatepass.findUnique({
      where: { id: gatepassId },
      include: {
        student: {
          include: {
            college: true,
            department: true,
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!gatepass) {
      return NextResponse.json(
        { error: 'Gatepass not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(gatepass);
  } catch (error) {
    console.error('Error fetching gatepass:', error);
    if (error instanceof Error && error.message === 'No token provided') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
