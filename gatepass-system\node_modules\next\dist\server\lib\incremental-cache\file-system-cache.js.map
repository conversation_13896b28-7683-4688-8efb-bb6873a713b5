{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/file-system-cache.ts"], "sourcesContent": ["import type { RouteMetadata } from '../../../export/routes/types'\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, CacheHandlerContext, CacheHandlerValue } from '.'\nimport type { CacheFs } from '../../../shared/lib/utils'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchValue,\n  type IncrementalCacheValue,\n  type SetIncrementalFetchCacheContext,\n  type SetIncrementalResponseCacheContext,\n} from '../../response-cache'\n\nimport type { LRUCache } from '../lru-cache'\nimport path from '../../../shared/lib/isomorphic/path'\nimport {\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_DATA_SUFFIX,\n  NEXT_META_SUFFIX,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SUFFIX,\n} from '../../../lib/constants'\nimport { isStale, tagsManifest } from './tags-manifest.external'\nimport { MultiFileWriter } from '../../../lib/multi-file-writer'\nimport { getMemoryCache } from './memory-cache.external'\n\ntype FileSystemCacheContext = Omit<\n  CacheHandlerContext,\n  'fs' | 'serverDistDir'\n> & {\n  fs: CacheFs\n  serverDistDir: string\n}\n\nexport default class FileSystemCache implements CacheHandler {\n  private fs: FileSystemCacheContext['fs']\n  private flushToDisk?: FileSystemCacheContext['flushToDisk']\n  private serverDistDir: FileSystemCacheContext['serverDistDir']\n  private revalidatedTags: string[]\n  private static debug: boolean = !!process.env.NEXT_PRIVATE_DEBUG_CACHE\n  private static memoryCache: LRUCache<CacheHandlerValue> | undefined\n\n  constructor(ctx: FileSystemCacheContext) {\n    this.fs = ctx.fs\n    this.flushToDisk = ctx.flushToDisk\n    this.serverDistDir = ctx.serverDistDir\n    this.revalidatedTags = ctx.revalidatedTags\n\n    if (ctx.maxMemoryCacheSize) {\n      if (!FileSystemCache.memoryCache) {\n        if (FileSystemCache.debug) {\n          console.log('using memory store for fetch cache')\n        }\n\n        FileSystemCache.memoryCache = getMemoryCache(ctx.maxMemoryCacheSize)\n      } else if (FileSystemCache.debug) {\n        console.log('memory store already initialized')\n      }\n    } else if (FileSystemCache.debug) {\n      console.log('not using memory store for fetch cache')\n    }\n  }\n\n  public resetRequestCache(): void {}\n\n  public async revalidateTag(\n    ...args: Parameters<CacheHandler['revalidateTag']>\n  ) {\n    let [tags] = args\n    tags = typeof tags === 'string' ? [tags] : tags\n\n    if (FileSystemCache.debug) {\n      console.log('revalidateTag', tags)\n    }\n\n    if (tags.length === 0) {\n      return\n    }\n\n    for (const tag of tags) {\n      if (!tagsManifest.has(tag)) {\n        tagsManifest.set(tag, Date.now())\n      }\n    }\n  }\n\n  public async get(...args: Parameters<CacheHandler['get']>) {\n    const [key, ctx] = args\n    const { kind } = ctx\n\n    let data = FileSystemCache.memoryCache?.get(key)\n\n    if (FileSystemCache.debug) {\n      if (kind === IncrementalCacheKind.FETCH) {\n        console.log('get', key, ctx.tags, kind, !!data)\n      } else {\n        console.log('get', key, kind, !!data)\n      }\n    }\n\n    // let's check the disk for seed data\n    if (!data && process.env.NEXT_RUNTIME !== 'edge') {\n      if (kind === IncrementalCacheKind.APP_ROUTE) {\n        try {\n          const filePath = this.getFilePath(\n            `${key}.body`,\n            IncrementalCacheKind.APP_ROUTE\n          )\n          const fileData = await this.fs.readFile(filePath)\n          const { mtime } = await this.fs.stat(filePath)\n\n          const meta = JSON.parse(\n            await this.fs.readFile(\n              filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n              'utf8'\n            )\n          )\n\n          const cacheEntry: CacheHandlerValue = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.APP_ROUTE,\n              body: fileData,\n              headers: meta.headers,\n              status: meta.status,\n            },\n          }\n          return cacheEntry\n        } catch {\n          return null\n        }\n      }\n\n      try {\n        const filePath = this.getFilePath(\n          kind === IncrementalCacheKind.FETCH ? key : `${key}.html`,\n          kind\n        )\n\n        const fileData = await this.fs.readFile(filePath, 'utf8')\n        const { mtime } = await this.fs.stat(filePath)\n\n        if (kind === IncrementalCacheKind.FETCH) {\n          const { tags, fetchIdx, fetchUrl } = ctx\n\n          if (!this.flushToDisk) return null\n\n          const lastModified = mtime.getTime()\n          const parsedData: CachedFetchValue = JSON.parse(fileData)\n          data = {\n            lastModified,\n            value: parsedData,\n          }\n\n          if (data.value?.kind === CachedRouteKind.FETCH) {\n            const storedTags = data.value?.tags\n\n            // update stored tags if a new one is being added\n            // TODO: remove this when we can send the tags\n            // via header on GET same as SET\n            if (!tags?.every((tag) => storedTags?.includes(tag))) {\n              if (FileSystemCache.debug) {\n                console.log('tags vs storedTags mismatch', tags, storedTags)\n              }\n              await this.set(key, data.value, {\n                fetchCache: true,\n                tags,\n                fetchIdx,\n                fetchUrl,\n              })\n            }\n          }\n        } else if (kind === IncrementalCacheKind.APP_PAGE) {\n          // We try to load the metadata file, but if it fails, we don't\n          // error. We also don't load it if this is a fallback.\n          let meta: RouteMetadata | undefined\n          try {\n            meta = JSON.parse(\n              await this.fs.readFile(\n                filePath.replace(/\\.html$/, NEXT_META_SUFFIX),\n                'utf8'\n              )\n            )\n          } catch {}\n\n          let maybeSegmentData: Map<string, Buffer> | undefined\n          if (meta?.segmentPaths) {\n            // Collect all the segment data for this page.\n            // TODO: To optimize file system reads, we should consider creating\n            // separate cache entries for each segment, rather than storing them\n            // all on the page's entry. Though the behavior is\n            // identical regardless.\n            const segmentData: Map<string, Buffer> = new Map()\n            maybeSegmentData = segmentData\n            const segmentsDir = key + RSC_SEGMENTS_DIR_SUFFIX\n            await Promise.all(\n              meta.segmentPaths.map(async (segmentPath: string) => {\n                const segmentDataFilePath = this.getFilePath(\n                  segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX,\n                  IncrementalCacheKind.APP_PAGE\n                )\n                try {\n                  segmentData.set(\n                    segmentPath,\n                    await this.fs.readFile(segmentDataFilePath)\n                  )\n                } catch {\n                  // This shouldn't happen, but if for some reason we fail to\n                  // load a segment from the filesystem, treat it the same as if\n                  // the segment is dynamic and does not have a prefetch.\n                }\n              })\n            )\n          }\n\n          let rscData: Buffer | undefined\n          if (!ctx.isFallback) {\n            rscData = await this.fs.readFile(\n              this.getFilePath(\n                `${key}${ctx.isRoutePPREnabled ? RSC_PREFETCH_SUFFIX : RSC_SUFFIX}`,\n                IncrementalCacheKind.APP_PAGE\n              )\n            )\n          }\n\n          data = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.APP_PAGE,\n              html: fileData,\n              rscData,\n              postponed: meta?.postponed,\n              headers: meta?.headers,\n              status: meta?.status,\n              segmentData: maybeSegmentData,\n            },\n          }\n        } else if (kind === IncrementalCacheKind.PAGES) {\n          let meta: RouteMetadata | undefined\n          let pageData: string | object = {}\n\n          if (!ctx.isFallback) {\n            pageData = JSON.parse(\n              await this.fs.readFile(\n                this.getFilePath(\n                  `${key}${NEXT_DATA_SUFFIX}`,\n                  IncrementalCacheKind.PAGES\n                ),\n                'utf8'\n              )\n            )\n          }\n\n          data = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.PAGES,\n              html: fileData,\n              pageData,\n              headers: meta?.headers,\n              status: meta?.status,\n            },\n          }\n        } else {\n          throw new Error(\n            `Invariant: Unexpected route kind ${kind} in file system cache.`\n          )\n        }\n\n        if (data) {\n          FileSystemCache.memoryCache?.set(key, data)\n        }\n      } catch {\n        return null\n      }\n    }\n\n    if (\n      data?.value?.kind === CachedRouteKind.APP_PAGE ||\n      data?.value?.kind === CachedRouteKind.PAGES\n    ) {\n      let cacheTags: undefined | string[]\n      const tagsHeader = data.value.headers?.[NEXT_CACHE_TAGS_HEADER]\n\n      if (typeof tagsHeader === 'string') {\n        cacheTags = tagsHeader.split(',')\n      }\n\n      if (cacheTags?.length) {\n        // we trigger a blocking validation if an ISR page\n        // had a tag revalidated, if we want to be a background\n        // revalidation instead we return data.lastModified = -1\n        if (isStale(cacheTags, data?.lastModified || Date.now())) {\n          return null\n        }\n      }\n    } else if (data?.value?.kind === CachedRouteKind.FETCH) {\n      const combinedTags =\n        ctx.kind === IncrementalCacheKind.FETCH\n          ? [...(ctx.tags || []), ...(ctx.softTags || [])]\n          : []\n\n      const wasRevalidated = combinedTags.some((tag) => {\n        if (this.revalidatedTags.includes(tag)) {\n          return true\n        }\n\n        return isStale([tag], data?.lastModified || Date.now())\n      })\n      // When revalidate tag is called we don't return\n      // stale data so it's updated right away\n      if (wasRevalidated) {\n        data = undefined\n      }\n    }\n\n    return data ?? null\n  }\n\n  public async set(\n    key: string,\n    data: IncrementalCacheValue | null,\n    ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ) {\n    FileSystemCache.memoryCache?.set(key, {\n      value: data,\n      lastModified: Date.now(),\n    })\n\n    if (FileSystemCache.debug) {\n      console.log('set', key)\n    }\n\n    if (!this.flushToDisk || !data) return\n\n    // Create a new writer that will prepare to write all the files to disk\n    // after their containing directory is created.\n    const writer = new MultiFileWriter(this.fs)\n\n    if (data.kind === CachedRouteKind.APP_ROUTE) {\n      const filePath = this.getFilePath(\n        `${key}.body`,\n        IncrementalCacheKind.APP_ROUTE\n      )\n\n      writer.append(filePath, data.body)\n\n      const meta: RouteMetadata = {\n        headers: data.headers,\n        status: data.status,\n        postponed: undefined,\n        segmentPaths: undefined,\n      }\n\n      writer.append(\n        filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n        JSON.stringify(meta, null, 2)\n      )\n    } else if (\n      data.kind === CachedRouteKind.PAGES ||\n      data.kind === CachedRouteKind.APP_PAGE\n    ) {\n      const isAppPath = data.kind === CachedRouteKind.APP_PAGE\n      const htmlPath = this.getFilePath(\n        `${key}.html`,\n        isAppPath ? IncrementalCacheKind.APP_PAGE : IncrementalCacheKind.PAGES\n      )\n\n      writer.append(htmlPath, data.html)\n\n      // Fallbacks don't generate a data file.\n      if (!ctx.fetchCache && !ctx.isFallback) {\n        writer.append(\n          this.getFilePath(\n            `${key}${\n              isAppPath\n                ? ctx.isRoutePPREnabled\n                  ? RSC_PREFETCH_SUFFIX\n                  : RSC_SUFFIX\n                : NEXT_DATA_SUFFIX\n            }`,\n            isAppPath\n              ? IncrementalCacheKind.APP_PAGE\n              : IncrementalCacheKind.PAGES\n          ),\n          isAppPath ? data.rscData! : JSON.stringify(data.pageData)\n        )\n      }\n\n      if (data?.kind === CachedRouteKind.APP_PAGE) {\n        let segmentPaths: string[] | undefined\n        if (data.segmentData) {\n          segmentPaths = []\n          const segmentsDir = htmlPath.replace(\n            /\\.html$/,\n            RSC_SEGMENTS_DIR_SUFFIX\n          )\n\n          for (const [segmentPath, buffer] of data.segmentData) {\n            segmentPaths.push(segmentPath)\n            const segmentDataFilePath =\n              segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX\n            writer.append(segmentDataFilePath, buffer)\n          }\n        }\n\n        const meta: RouteMetadata = {\n          headers: data.headers,\n          status: data.status,\n          postponed: data.postponed,\n          segmentPaths,\n        }\n\n        writer.append(\n          htmlPath.replace(/\\.html$/, NEXT_META_SUFFIX),\n          JSON.stringify(meta)\n        )\n      }\n    } else if (data.kind === CachedRouteKind.FETCH) {\n      const filePath = this.getFilePath(key, IncrementalCacheKind.FETCH)\n      writer.append(\n        filePath,\n        JSON.stringify({\n          ...data,\n          tags: ctx.fetchCache ? ctx.tags : [],\n        })\n      )\n    }\n\n    // Wait for all FS operations to complete.\n    await writer.wait()\n  }\n\n  private getFilePath(pathname: string, kind: IncrementalCacheKind): string {\n    switch (kind) {\n      case IncrementalCacheKind.FETCH:\n        // we store in .next/cache/fetch-cache so it can be persisted\n        // across deploys\n        return path.join(\n          this.serverDistDir,\n          '..',\n          'cache',\n          'fetch-cache',\n          pathname\n        )\n      case IncrementalCacheKind.PAGES:\n        return path.join(this.serverDistDir, 'pages', pathname)\n      case IncrementalCacheKind.IMAGE:\n      case IncrementalCacheKind.APP_PAGE:\n      case IncrementalCacheKind.APP_ROUTE:\n        return path.join(this.serverDistDir, 'app', pathname)\n      default:\n        throw new Error(`Unexpected file path kind: ${kind}`)\n    }\n  }\n}\n"], "names": ["FileSystemCache", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "revalidatedTags", "maxMemoryCacheSize", "memoryCache", "console", "log", "getMemoryCache", "resetRequestCache", "revalidateTag", "args", "tags", "length", "tag", "tagsManifest", "has", "set", "Date", "now", "get", "data", "key", "kind", "IncrementalCacheKind", "FETCH", "NEXT_RUNTIME", "APP_ROUTE", "filePath", "getFilePath", "fileData", "readFile", "mtime", "stat", "meta", "JSON", "parse", "replace", "NEXT_META_SUFFIX", "cacheEntry", "lastModified", "getTime", "value", "CachedRouteKind", "body", "headers", "status", "fetchIdx", "fetchUrl", "parsedData", "storedTags", "every", "includes", "fetchCache", "APP_PAGE", "maybeSegmentData", "segmentPaths", "segmentData", "Map", "segmentsDir", "RSC_SEGMENTS_DIR_SUFFIX", "Promise", "all", "map", "segmentPath", "segmentDataFilePath", "RSC_SEGMENT_SUFFIX", "rscData", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "html", "postponed", "PAGES", "pageData", "NEXT_DATA_SUFFIX", "Error", "cacheTags", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_CACHE_TAGS_HEADER", "split", "isStale", "combinedTags", "softTags", "wasRevalidated", "some", "undefined", "writer", "MultiFileWriter", "append", "stringify", "isAppPath", "htmlPath", "buffer", "push", "wait", "pathname", "path", "join", "IMAGE"], "mappings": ";;;;+BAmCA;;;eAAqBA;;;+BAzBd;6DAGU;2BASV;sCAC+B;iCACN;qCACD;;;;;;AAUhB,MAAMA;qBAKJC,QAAiB,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;IAGtEC,YAAYC,GAA2B,CAAE;QACvC,IAAI,CAACC,EAAE,GAAGD,IAAIC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,IAAIE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,IAAIG,aAAa;QACtC,IAAI,CAACC,eAAe,GAAGJ,IAAII,eAAe;QAE1C,IAAIJ,IAAIK,kBAAkB,EAAE;YAC1B,IAAI,CAACX,gBAAgBY,WAAW,EAAE;gBAChC,IAAIZ,gBAAgBC,KAAK,EAAE;oBACzBY,QAAQC,GAAG,CAAC;gBACd;gBAEAd,gBAAgBY,WAAW,GAAGG,IAAAA,mCAAc,EAACT,IAAIK,kBAAkB;YACrE,OAAO,IAAIX,gBAAgBC,KAAK,EAAE;gBAChCY,QAAQC,GAAG,CAAC;YACd;QACF,OAAO,IAAId,gBAAgBC,KAAK,EAAE;YAChCY,QAAQC,GAAG,CAAC;QACd;IACF;IAEOE,oBAA0B,CAAC;IAElC,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAE3C,IAAInB,gBAAgBC,KAAK,EAAE;YACzBY,QAAQC,GAAG,CAAC,iBAAiBK;QAC/B;QAEA,IAAIA,KAAKC,MAAM,KAAK,GAAG;YACrB;QACF;QAEA,KAAK,MAAMC,OAAOF,KAAM;YACtB,IAAI,CAACG,kCAAY,CAACC,GAAG,CAACF,MAAM;gBAC1BC,kCAAY,CAACE,GAAG,CAACH,KAAKI,KAAKC,GAAG;YAChC;QACF;IACF;IAEA,MAAaC,IAAI,GAAGT,IAAqC,EAAE;YAI9ClB,8BA4LT4B,aACAA,cAiBSA;QAjNX,MAAM,CAACC,KAAKvB,IAAI,GAAGY;QACnB,MAAM,EAAEY,IAAI,EAAE,GAAGxB;QAEjB,IAAIsB,QAAO5B,+BAAAA,gBAAgBY,WAAW,qBAA3BZ,6BAA6B2B,GAAG,CAACE;QAE5C,IAAI7B,gBAAgBC,KAAK,EAAE;YACzB,IAAI6B,SAASC,mCAAoB,CAACC,KAAK,EAAE;gBACvCnB,QAAQC,GAAG,CAAC,OAAOe,KAAKvB,IAAIa,IAAI,EAAEW,MAAM,CAAC,CAACF;YAC5C,OAAO;gBACLf,QAAQC,GAAG,CAAC,OAAOe,KAAKC,MAAM,CAAC,CAACF;YAClC;QACF;QAEA,qCAAqC;QACrC,IAAI,CAACA,QAAQ1B,QAAQC,GAAG,CAAC8B,YAAY,KAAK,QAAQ;YAChD,IAAIH,SAASC,mCAAoB,CAACG,SAAS,EAAE;gBAC3C,IAAI;oBACF,MAAMC,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGP,IAAI,KAAK,CAAC,EACbE,mCAAoB,CAACG,SAAS;oBAEhC,MAAMG,WAAW,MAAM,IAAI,CAAC9B,EAAE,CAAC+B,QAAQ,CAACH;oBACxC,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAChC,EAAE,CAACiC,IAAI,CAACL;oBAErC,MAAMM,OAAOC,KAAKC,KAAK,CACrB,MAAM,IAAI,CAACpC,EAAE,CAAC+B,QAAQ,CACpBH,SAASS,OAAO,CAAC,WAAWC,2BAAgB,GAC5C;oBAIJ,MAAMC,aAAgC;wBACpCC,cAAcR,MAAMS,OAAO;wBAC3BC,OAAO;4BACLnB,MAAMoB,8BAAe,CAAChB,SAAS;4BAC/BiB,MAAMd;4BACNe,SAASX,KAAKW,OAAO;4BACrBC,QAAQZ,KAAKY,MAAM;wBACrB;oBACF;oBACA,OAAOP;gBACT,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI;gBACF,MAAMX,WAAW,IAAI,CAACC,WAAW,CAC/BN,SAASC,mCAAoB,CAACC,KAAK,GAAGH,MAAM,GAAGA,IAAI,KAAK,CAAC,EACzDC;gBAGF,MAAMO,WAAW,MAAM,IAAI,CAAC9B,EAAE,CAAC+B,QAAQ,CAACH,UAAU;gBAClD,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAChC,EAAE,CAACiC,IAAI,CAACL;gBAErC,IAAIL,SAASC,mCAAoB,CAACC,KAAK,EAAE;wBAYnCJ;oBAXJ,MAAM,EAAET,IAAI,EAAEmC,QAAQ,EAAEC,QAAQ,EAAE,GAAGjD;oBAErC,IAAI,CAAC,IAAI,CAACE,WAAW,EAAE,OAAO;oBAE9B,MAAMuC,eAAeR,MAAMS,OAAO;oBAClC,MAAMQ,aAA+Bd,KAAKC,KAAK,CAACN;oBAChDT,OAAO;wBACLmB;wBACAE,OAAOO;oBACT;oBAEA,IAAI5B,EAAAA,eAAAA,KAAKqB,KAAK,qBAAVrB,aAAYE,IAAI,MAAKoB,8BAAe,CAAClB,KAAK,EAAE;4BAC3BJ;wBAAnB,MAAM6B,cAAa7B,eAAAA,KAAKqB,KAAK,qBAAVrB,aAAYT,IAAI;wBAEnC,iDAAiD;wBACjD,8CAA8C;wBAC9C,gCAAgC;wBAChC,IAAI,EAACA,wBAAAA,KAAMuC,KAAK,CAAC,CAACrC,MAAQoC,8BAAAA,WAAYE,QAAQ,CAACtC,QAAO;4BACpD,IAAIrB,gBAAgBC,KAAK,EAAE;gCACzBY,QAAQC,GAAG,CAAC,+BAA+BK,MAAMsC;4BACnD;4BACA,MAAM,IAAI,CAACjC,GAAG,CAACK,KAAKD,KAAKqB,KAAK,EAAE;gCAC9BW,YAAY;gCACZzC;gCACAmC;gCACAC;4BACF;wBACF;oBACF;gBACF,OAAO,IAAIzB,SAASC,mCAAoB,CAAC8B,QAAQ,EAAE;oBACjD,8DAA8D;oBAC9D,sDAAsD;oBACtD,IAAIpB;oBACJ,IAAI;wBACFA,OAAOC,KAAKC,KAAK,CACf,MAAM,IAAI,CAACpC,EAAE,CAAC+B,QAAQ,CACpBH,SAASS,OAAO,CAAC,WAAWC,2BAAgB,GAC5C;oBAGN,EAAE,OAAM,CAAC;oBAET,IAAIiB;oBACJ,IAAIrB,wBAAAA,KAAMsB,YAAY,EAAE;wBACtB,8CAA8C;wBAC9C,mEAAmE;wBACnE,oEAAoE;wBACpE,kDAAkD;wBAClD,wBAAwB;wBACxB,MAAMC,cAAmC,IAAIC;wBAC7CH,mBAAmBE;wBACnB,MAAME,cAAcrC,MAAMsC,kCAAuB;wBACjD,MAAMC,QAAQC,GAAG,CACf5B,KAAKsB,YAAY,CAACO,GAAG,CAAC,OAAOC;4BAC3B,MAAMC,sBAAsB,IAAI,CAACpC,WAAW,CAC1C8B,cAAcK,cAAcE,6BAAkB,EAC9C1C,mCAAoB,CAAC8B,QAAQ;4BAE/B,IAAI;gCACFG,YAAYxC,GAAG,CACb+C,aACA,MAAM,IAAI,CAAChE,EAAE,CAAC+B,QAAQ,CAACkC;4BAE3B,EAAE,OAAM;4BACN,2DAA2D;4BAC3D,8DAA8D;4BAC9D,uDAAuD;4BACzD;wBACF;oBAEJ;oBAEA,IAAIE;oBACJ,IAAI,CAACpE,IAAIqE,UAAU,EAAE;wBACnBD,UAAU,MAAM,IAAI,CAACnE,EAAE,CAAC+B,QAAQ,CAC9B,IAAI,CAACF,WAAW,CACd,GAAGP,MAAMvB,IAAIsE,iBAAiB,GAAGC,8BAAmB,GAAGC,qBAAU,EAAE,EACnE/C,mCAAoB,CAAC8B,QAAQ;oBAGnC;oBAEAjC,OAAO;wBACLmB,cAAcR,MAAMS,OAAO;wBAC3BC,OAAO;4BACLnB,MAAMoB,8BAAe,CAACW,QAAQ;4BAC9BkB,MAAM1C;4BACNqC;4BACAM,SAAS,EAAEvC,wBAAAA,KAAMuC,SAAS;4BAC1B5B,OAAO,EAAEX,wBAAAA,KAAMW,OAAO;4BACtBC,MAAM,EAAEZ,wBAAAA,KAAMY,MAAM;4BACpBW,aAAaF;wBACf;oBACF;gBACF,OAAO,IAAIhC,SAASC,mCAAoB,CAACkD,KAAK,EAAE;oBAC9C,IAAIxC;oBACJ,IAAIyC,WAA4B,CAAC;oBAEjC,IAAI,CAAC5E,IAAIqE,UAAU,EAAE;wBACnBO,WAAWxC,KAAKC,KAAK,CACnB,MAAM,IAAI,CAACpC,EAAE,CAAC+B,QAAQ,CACpB,IAAI,CAACF,WAAW,CACd,GAAGP,MAAMsD,2BAAgB,EAAE,EAC3BpD,mCAAoB,CAACkD,KAAK,GAE5B;oBAGN;oBAEArD,OAAO;wBACLmB,cAAcR,MAAMS,OAAO;wBAC3BC,OAAO;4BACLnB,MAAMoB,8BAAe,CAAC+B,KAAK;4BAC3BF,MAAM1C;4BACN6C;4BACA9B,OAAO,EAAEX,wBAAAA,KAAMW,OAAO;4BACtBC,MAAM,EAAEZ,wBAAAA,KAAMY,MAAM;wBACtB;oBACF;gBACF,OAAO;oBACL,MAAM,qBAEL,CAFK,IAAI+B,MACR,CAAC,iCAAiC,EAAEtD,KAAK,sBAAsB,CAAC,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIF,MAAM;wBACR5B;qBAAAA,gCAAAA,gBAAgBY,WAAW,qBAA3BZ,8BAA6BwB,GAAG,CAACK,KAAKD;gBACxC;YACF,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QAEA,IACEA,CAAAA,yBAAAA,cAAAA,KAAMqB,KAAK,qBAAXrB,YAAaE,IAAI,MAAKoB,8BAAe,CAACW,QAAQ,IAC9CjC,CAAAA,yBAAAA,eAAAA,KAAMqB,KAAK,qBAAXrB,aAAaE,IAAI,MAAKoB,8BAAe,CAAC+B,KAAK,EAC3C;gBAEmBrD;YADnB,IAAIyD;YACJ,MAAMC,cAAa1D,sBAAAA,KAAKqB,KAAK,CAACG,OAAO,qBAAlBxB,mBAAoB,CAAC2D,iCAAsB,CAAC;YAE/D,IAAI,OAAOD,eAAe,UAAU;gBAClCD,YAAYC,WAAWE,KAAK,CAAC;YAC/B;YAEA,IAAIH,6BAAAA,UAAWjE,MAAM,EAAE;gBACrB,kDAAkD;gBAClD,uDAAuD;gBACvD,wDAAwD;gBACxD,IAAIqE,IAAAA,6BAAO,EAACJ,WAAWzD,CAAAA,wBAAAA,KAAMmB,YAAY,KAAItB,KAAKC,GAAG,KAAK;oBACxD,OAAO;gBACT;YACF;QACF,OAAO,IAAIE,CAAAA,yBAAAA,eAAAA,KAAMqB,KAAK,qBAAXrB,aAAaE,IAAI,MAAKoB,8BAAe,CAAClB,KAAK,EAAE;YACtD,MAAM0D,eACJpF,IAAIwB,IAAI,KAAKC,mCAAoB,CAACC,KAAK,GACnC;mBAAK1B,IAAIa,IAAI,IAAI,EAAE;mBAAOb,IAAIqF,QAAQ,IAAI,EAAE;aAAE,GAC9C,EAAE;YAER,MAAMC,iBAAiBF,aAAaG,IAAI,CAAC,CAACxE;gBACxC,IAAI,IAAI,CAACX,eAAe,CAACiD,QAAQ,CAACtC,MAAM;oBACtC,OAAO;gBACT;gBAEA,OAAOoE,IAAAA,6BAAO,EAAC;oBAACpE;iBAAI,EAAEO,CAAAA,wBAAAA,KAAMmB,YAAY,KAAItB,KAAKC,GAAG;YACtD;YACA,gDAAgD;YAChD,wCAAwC;YACxC,IAAIkE,gBAAgB;gBAClBhE,OAAOkE;YACT;QACF;QAEA,OAAOlE,QAAQ;IACjB;IAEA,MAAaJ,IACXK,GAAW,EACXD,IAAkC,EAClCtB,GAAyE,EACzE;YACAN;SAAAA,+BAAAA,gBAAgBY,WAAW,qBAA3BZ,6BAA6BwB,GAAG,CAACK,KAAK;YACpCoB,OAAOrB;YACPmB,cAActB,KAAKC,GAAG;QACxB;QAEA,IAAI1B,gBAAgBC,KAAK,EAAE;YACzBY,QAAQC,GAAG,CAAC,OAAOe;QACrB;QAEA,IAAI,CAAC,IAAI,CAACrB,WAAW,IAAI,CAACoB,MAAM;QAEhC,uEAAuE;QACvE,+CAA+C;QAC/C,MAAMmE,SAAS,IAAIC,gCAAe,CAAC,IAAI,CAACzF,EAAE;QAE1C,IAAIqB,KAAKE,IAAI,KAAKoB,8BAAe,CAAChB,SAAS,EAAE;YAC3C,MAAMC,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGP,IAAI,KAAK,CAAC,EACbE,mCAAoB,CAACG,SAAS;YAGhC6D,OAAOE,MAAM,CAAC9D,UAAUP,KAAKuB,IAAI;YAEjC,MAAMV,OAAsB;gBAC1BW,SAASxB,KAAKwB,OAAO;gBACrBC,QAAQzB,KAAKyB,MAAM;gBACnB2B,WAAWc;gBACX/B,cAAc+B;YAChB;YAEAC,OAAOE,MAAM,CACX9D,SAASS,OAAO,CAAC,WAAWC,2BAAgB,GAC5CH,KAAKwD,SAAS,CAACzD,MAAM,MAAM;QAE/B,OAAO,IACLb,KAAKE,IAAI,KAAKoB,8BAAe,CAAC+B,KAAK,IACnCrD,KAAKE,IAAI,KAAKoB,8BAAe,CAACW,QAAQ,EACtC;YACA,MAAMsC,YAAYvE,KAAKE,IAAI,KAAKoB,8BAAe,CAACW,QAAQ;YACxD,MAAMuC,WAAW,IAAI,CAAChE,WAAW,CAC/B,GAAGP,IAAI,KAAK,CAAC,EACbsE,YAAYpE,mCAAoB,CAAC8B,QAAQ,GAAG9B,mCAAoB,CAACkD,KAAK;YAGxEc,OAAOE,MAAM,CAACG,UAAUxE,KAAKmD,IAAI;YAEjC,wCAAwC;YACxC,IAAI,CAACzE,IAAIsD,UAAU,IAAI,CAACtD,IAAIqE,UAAU,EAAE;gBACtCoB,OAAOE,MAAM,CACX,IAAI,CAAC7D,WAAW,CACd,GAAGP,MACDsE,YACI7F,IAAIsE,iBAAiB,GACnBC,8BAAmB,GACnBC,qBAAU,GACZK,2BAAgB,EACpB,EACFgB,YACIpE,mCAAoB,CAAC8B,QAAQ,GAC7B9B,mCAAoB,CAACkD,KAAK,GAEhCkB,YAAYvE,KAAK8C,OAAO,GAAIhC,KAAKwD,SAAS,CAACtE,KAAKsD,QAAQ;YAE5D;YAEA,IAAItD,CAAAA,wBAAAA,KAAME,IAAI,MAAKoB,8BAAe,CAACW,QAAQ,EAAE;gBAC3C,IAAIE;gBACJ,IAAInC,KAAKoC,WAAW,EAAE;oBACpBD,eAAe,EAAE;oBACjB,MAAMG,cAAckC,SAASxD,OAAO,CAClC,WACAuB,kCAAuB;oBAGzB,KAAK,MAAM,CAACI,aAAa8B,OAAO,IAAIzE,KAAKoC,WAAW,CAAE;wBACpDD,aAAauC,IAAI,CAAC/B;wBAClB,MAAMC,sBACJN,cAAcK,cAAcE,6BAAkB;wBAChDsB,OAAOE,MAAM,CAACzB,qBAAqB6B;oBACrC;gBACF;gBAEA,MAAM5D,OAAsB;oBAC1BW,SAASxB,KAAKwB,OAAO;oBACrBC,QAAQzB,KAAKyB,MAAM;oBACnB2B,WAAWpD,KAAKoD,SAAS;oBACzBjB;gBACF;gBAEAgC,OAAOE,MAAM,CACXG,SAASxD,OAAO,CAAC,WAAWC,2BAAgB,GAC5CH,KAAKwD,SAAS,CAACzD;YAEnB;QACF,OAAO,IAAIb,KAAKE,IAAI,KAAKoB,8BAAe,CAAClB,KAAK,EAAE;YAC9C,MAAMG,WAAW,IAAI,CAACC,WAAW,CAACP,KAAKE,mCAAoB,CAACC,KAAK;YACjE+D,OAAOE,MAAM,CACX9D,UACAO,KAAKwD,SAAS,CAAC;gBACb,GAAGtE,IAAI;gBACPT,MAAMb,IAAIsD,UAAU,GAAGtD,IAAIa,IAAI,GAAG,EAAE;YACtC;QAEJ;QAEA,0CAA0C;QAC1C,MAAM4E,OAAOQ,IAAI;IACnB;IAEQnE,YAAYoE,QAAgB,EAAE1E,IAA0B,EAAU;QACxE,OAAQA;YACN,KAAKC,mCAAoB,CAACC,KAAK;gBAC7B,6DAA6D;gBAC7D,iBAAiB;gBACjB,OAAOyE,aAAI,CAACC,IAAI,CACd,IAAI,CAACjG,aAAa,EAClB,MACA,SACA,eACA+F;YAEJ,KAAKzE,mCAAoB,CAACkD,KAAK;gBAC7B,OAAOwB,aAAI,CAACC,IAAI,CAAC,IAAI,CAACjG,aAAa,EAAE,SAAS+F;YAChD,KAAKzE,mCAAoB,CAAC4E,KAAK;YAC/B,KAAK5E,mCAAoB,CAAC8B,QAAQ;YAClC,KAAK9B,mCAAoB,CAACG,SAAS;gBACjC,OAAOuE,aAAI,CAACC,IAAI,CAAC,IAAI,CAACjG,aAAa,EAAE,OAAO+F;YAC9C;gBACE,MAAM,qBAA+C,CAA/C,IAAIpB,MAAM,CAAC,2BAA2B,EAAEtD,MAAM,GAA9C,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;QACxD;IACF;AACF", "ignoreList": [0]}