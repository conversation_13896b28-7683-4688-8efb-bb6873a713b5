{"version": 3, "sources": ["../../src/lib/find-root.ts"], "sourcesContent": ["import { dirname } from 'path'\nimport findUp from 'next/dist/compiled/find-up'\nimport * as Log from '../build/output/log'\n\nexport function findRootLockFile(cwd: string) {\n  return findUp.sync(\n    [\n      'pnpm-lock.yaml',\n      'package-lock.json',\n      'yarn.lock',\n      'bun.lock',\n      'bun.lockb',\n    ],\n    {\n      cwd,\n    }\n  )\n}\n\nexport function findRootDir(cwd: string) {\n  const lockFile = findRootLockFile(cwd)\n  if (!lockFile) return undefined\n\n  const lockFiles = [lockFile]\n  while (true) {\n    const nextDir = dirname(dirname(lockFiles[lockFiles.length - 1]))\n    const newLockFile = findRootLockFile(nextDir)\n\n    if (newLockFile) {\n      lockFiles.push(newLockFile)\n    } else {\n      break\n    }\n  }\n\n  // Only warn if not in a build worker to avoid duplicate warnings\n  if (typeof process.send !== 'function' && lockFiles.length > 1) {\n    Log.warnOnce(\n      `Warning: Found multiple lockfiles. Selecting ${lockFiles[lockFiles.length - 1]}.\\n   Consider removing the lockfiles at:${lockFiles\n        .slice(0, -1)\n        .map((str) => '\\n   * ' + str)\n        .join('')}\\n`\n    )\n  }\n\n  return dirname(lockFile)\n}\n"], "names": ["findRootDir", "findRootLockFile", "cwd", "findUp", "sync", "lockFile", "undefined", "lockFiles", "nextDir", "dirname", "length", "newLockFile", "push", "process", "send", "Log", "warnOnce", "slice", "map", "str", "join"], "mappings": ";;;;;;;;;;;;;;;IAmBgBA,WAAW;eAAXA;;IAfAC,gBAAgB;eAAhBA;;;sBAJQ;+DACL;6DACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,SAASA,iBAAiBC,GAAW;IAC1C,OAAOC,eAAM,CAACC,IAAI,CAChB;QACE;QACA;QACA;QACA;QACA;KACD,EACD;QACEF;IACF;AAEJ;AAEO,SAASF,YAAYE,GAAW;IACrC,MAAMG,WAAWJ,iBAAiBC;IAClC,IAAI,CAACG,UAAU,OAAOC;IAEtB,MAAMC,YAAY;QAACF;KAAS;IAC5B,MAAO,KAAM;QACX,MAAMG,UAAUC,IAAAA,aAAO,EAACA,IAAAA,aAAO,EAACF,SAAS,CAACA,UAAUG,MAAM,GAAG,EAAE;QAC/D,MAAMC,cAAcV,iBAAiBO;QAErC,IAAIG,aAAa;YACfJ,UAAUK,IAAI,CAACD;QACjB,OAAO;YACL;QACF;IACF;IAEA,iEAAiE;IACjE,IAAI,OAAOE,QAAQC,IAAI,KAAK,cAAcP,UAAUG,MAAM,GAAG,GAAG;QAC9DK,KAAIC,QAAQ,CACV,CAAC,6CAA6C,EAAET,SAAS,CAACA,UAAUG,MAAM,GAAG,EAAE,CAAC,yCAAyC,EAAEH,UACxHU,KAAK,CAAC,GAAG,CAAC,GACVC,GAAG,CAAC,CAACC,MAAQ,YAAYA,KACzBC,IAAI,CAAC,IAAI,EAAE,CAAC;IAEnB;IAEA,OAAOX,IAAAA,aAAO,EAACJ;AACjB", "ignoreList": [0]}