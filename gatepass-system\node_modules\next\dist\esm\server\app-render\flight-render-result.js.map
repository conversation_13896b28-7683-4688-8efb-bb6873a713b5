{"version": 3, "sources": ["../../../src/server/app-render/flight-render-result.ts"], "sourcesContent": ["import { RSC_CONTENT_TYPE_HEADER } from '../../client/components/app-router-headers'\nimport RenderResult, { type RenderResultMetadata } from '../render-result'\n\n/**\n * Flight Response is always set to RSC_CONTENT_TYPE_HEADER to ensure it does not get interpreted as HTML.\n */\nexport class FlightRenderResult extends RenderResult {\n  constructor(\n    response: string | ReadableStream<Uint8Array>,\n    metadata: RenderResultMetadata = {}\n  ) {\n    super(response, { contentType: RSC_CONTENT_TYPE_HEADER, metadata })\n  }\n}\n"], "names": ["RSC_CONTENT_TYPE_HEADER", "RenderResult", "FlightRenderResult", "constructor", "response", "metadata", "contentType"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,6CAA4C;AACpF,OAAOC,kBAAiD,mBAAkB;AAE1E;;CAEC,GACD,OAAO,MAAMC,2BAA2BD;IACtCE,YACEC,QAA6C,EAC7CC,WAAiC,CAAC,CAAC,CACnC;QACA,KAAK,CAACD,UAAU;YAAEE,aAAaN;YAAyBK;QAAS;IACnE;AACF", "ignoreList": [0]}