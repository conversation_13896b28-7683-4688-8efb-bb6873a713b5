"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var download_exports = {};
__export(download_exports, {
  download: () => import_chunk_R5W6ONNL.download,
  getBinaryName: () => import_chunk_R5W6ONNL.getBinaryName,
  getVersion: () => import_chunk_R5W6ONNL.getVersion,
  maybeCopyToTmp: () => import_chunk_R5W6ONNL.maybeCopyToTmp,
  plusX: () => import_chunk_R5W6ONNL.plusX,
  vercelPkgPathRegex: () => import_chunk_R5W6ONNL.vercelPkgPathRegex
});
module.exports = __toCommonJS(download_exports);
var import_chunk_R5W6ONNL = require("./chunk-R5W6ONNL.js");
var import_chunk_MWVY55RY = require("./chunk-MWVY55RY.js");
var import_chunk_MX3HXAU2 = require("./chunk-MX3HXAU2.js");
var import_chunk_4GLRZ2GS = require("./chunk-4GLRZ2GS.js");
var import_chunk_NA32AGOU = require("./chunk-NA32AGOU.js");
var import_chunk_RXM4EBGR = require("./chunk-RXM4EBGR.js");
var import_chunk_SVP4SRAT = require("./chunk-SVP4SRAT.js");
var import_chunk_YJOPKU47 = require("./chunk-YJOPKU47.js");
var import_chunk_PXQVM7NP = require("./chunk-PXQVM7NP.js");
var import_chunk_X37PZICB = require("./chunk-X37PZICB.js");
var import_chunk_CWGQAQ3T = require("./chunk-CWGQAQ3T.js");
var import_chunk_VAPNG6TS = require("./chunk-VAPNG6TS.js");
var import_chunk_QGM4M3NI = require("./chunk-QGM4M3NI.js");
