{"version": 3, "sources": ["../../../../src/server/web/sandbox/fetch-inline-assets.ts"], "sourcesContent": ["import type { EdgeFunctionDefinition } from '../../../build/webpack/plugins/middleware-plugin'\nimport { createReadStream, promises as fs } from 'fs'\nimport { requestToBodyStream } from '../../body-streams'\nimport { resolve } from 'path'\n\n/**\n * Short-circuits the `fetch` function\n * to return a stream for a given asset, if a user used `new URL(\"file\", import.meta.url)`.\n * This allows to embed assets in Edge Runtime.\n */\nexport async function fetchInlineAsset(options: {\n  input: RequestInfo | URL\n  distDir: string\n  assets: EdgeFunctionDefinition['assets']\n  context: { Response: typeof Response; ReadableStream: typeof ReadableStream }\n}): Promise<Response | undefined> {\n  const inputString = String(options.input)\n  if (!inputString.startsWith('blob:')) {\n    return\n  }\n\n  const name = inputString.replace('blob:', '')\n  const asset = options.assets\n    ? options.assets.find((x) => x.name === name)\n    : {\n        name,\n        filePath: name,\n      }\n  if (!asset) {\n    return\n  }\n\n  const filePath = resolve(options.distDir, asset.filePath)\n  const fileIsReadable = await fs.access(filePath).then(\n    () => true,\n    () => false\n  )\n\n  if (fileIsReadable) {\n    const readStream = createReadStream(filePath)\n    return new options.context.Response(\n      requestToBodyStream(options.context, Uint8Array, readStream)\n    )\n  }\n}\n"], "names": ["createReadStream", "promises", "fs", "requestToBodyStream", "resolve", "fetchInlineAsset", "options", "inputString", "String", "input", "startsWith", "name", "replace", "asset", "assets", "find", "x", "filePath", "distDir", "fileIsReadable", "access", "then", "readStream", "context", "Response", "Uint8Array"], "mappings": "AACA,SAASA,gBAAgB,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AACrD,SAASC,mBAAmB,QAAQ,qBAAoB;AACxD,SAASC,OAAO,QAAQ,OAAM;AAE9B;;;;CAIC,GACD,OAAO,eAAeC,iBAAiBC,OAKtC;IACC,MAAMC,cAAcC,OAAOF,QAAQG,KAAK;IACxC,IAAI,CAACF,YAAYG,UAAU,CAAC,UAAU;QACpC;IACF;IAEA,MAAMC,OAAOJ,YAAYK,OAAO,CAAC,SAAS;IAC1C,MAAMC,QAAQP,QAAQQ,MAAM,GACxBR,QAAQQ,MAAM,CAACC,IAAI,CAAC,CAACC,IAAMA,EAAEL,IAAI,KAAKA,QACtC;QACEA;QACAM,UAAUN;IACZ;IACJ,IAAI,CAACE,OAAO;QACV;IACF;IAEA,MAAMI,WAAWb,QAAQE,QAAQY,OAAO,EAAEL,MAAMI,QAAQ;IACxD,MAAME,iBAAiB,MAAMjB,GAAGkB,MAAM,CAACH,UAAUI,IAAI,CACnD,IAAM,MACN,IAAM;IAGR,IAAIF,gBAAgB;QAClB,MAAMG,aAAatB,iBAAiBiB;QACpC,OAAO,IAAIX,QAAQiB,OAAO,CAACC,QAAQ,CACjCrB,oBAAoBG,QAAQiB,OAAO,EAAEE,YAAYH;IAErD;AACF", "ignoreList": [0]}