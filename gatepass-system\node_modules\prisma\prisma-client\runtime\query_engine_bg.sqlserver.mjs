var y=()=>{};y.prototype=y;let r;function N(e){r=e}let s=0,m=null;function h(){return(m===null||m.byteLength===0)&&(m=new Uint8Array(r.memory.buffer)),m}const j=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let T=new j("utf-8");const k=typeof T.encodeInto=="function"?function(e,t){return T.encodeInto(e,t)}:function(e,t){const n=T.encode(e);return t.set(n),{read:e.length,written:n.length}};function f(e,t,n){if(n===void 0){const u=T.encode(e),a=t(u.length,1)>>>0;return h().subarray(a,a+u.length).set(u),s=u.length,a}let o=e.length,_=t(o,1)>>>0;const i=h();let c=0;for(;c<o;c++){const u=e.charCodeAt(c);if(u>127)break;i[_+c]=u}if(c!==o){c!==0&&(e=e.slice(c)),_=n(_,o,o=c+e.length*3,1)>>>0;const u=h().subarray(_+c,_+o),a=k(e,u);c+=a.written,_=n(_,o,c,1)>>>0}return s=c,_}let w=null;function g(){return(w===null||w.buffer.detached===!0||w.buffer.detached===void 0&&w.buffer!==r.memory.buffer)&&(w=new DataView(r.memory.buffer)),w}function p(e){const t=r.__externref_table_alloc();return r.__wbindgen_export_4.set(t,e),t}function b(e,t){try{return e.apply(this,t)}catch(n){const o=p(n);r.__wbindgen_exn_store(o)}}const D=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let A=new D("utf-8",{ignoreBOM:!0,fatal:!0});A.decode();function q(e,t){return e=e>>>0,A.decode(h().subarray(e,e+t))}function l(e){return e==null}const I=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(e=>{r.__wbindgen_export_5.get(e.dtor)(e.a,e.b)});function R(e,t,n,o){const _={a:e,b:t,cnt:1,dtor:n},i=(...c)=>{_.cnt++;const u=_.a;_.a=0;try{return o(u,_.b,...c)}finally{--_.cnt===0?(r.__wbindgen_export_5.get(_.dtor)(u,_.b),I.unregister(_)):_.a=u}};return i.original=_,I.register(i,_,_),i}function S(e){const t=typeof e;if(t=="number"||t=="boolean"||e==null)return`${e}`;if(t=="string")return`"${e}"`;if(t=="symbol"){const _=e.description;return _==null?"Symbol":`Symbol(${_})`}if(t=="function"){const _=e.name;return typeof _=="string"&&_.length>0?`Function(${_})`:"Function"}if(Array.isArray(e)){const _=e.length;let i="[";_>0&&(i+=S(e[0]));for(let c=1;c<_;c++)i+=", "+S(e[c]);return i+="]",i}const n=/\[object ([^\]]+)\]/.exec(toString.call(e));let o;if(n&&n.length>1)o=n[1];else return toString.call(e);if(o=="Object")try{return"Object("+JSON.stringify(e)+")"}catch{return"Object"}return e instanceof Error?`${e.name}: ${e.message}
${e.stack}`:o}function E(e){const t=r.__wbindgen_export_4.get(e);return r.__externref_table_dealloc(e),t}function $(){return r.getBuildTimeInfo()}function C(e){var t=l(e)?0:f(e,r.__wbindgen_malloc,r.__wbindgen_realloc),n=s;const o=r.debug_panic(t,n);if(o[1])throw E(o[0])}function B(e,t,n){r.closure580_externref_shim(e,t,n)}function U(e,t,n,o){r.closure130_externref_shim(e,t,n,o)}const O=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(e=>r.__wbg_queryengine_free(e>>>0,1));class V{__destroy_into_raw(){const t=this.__wbg_ptr;return this.__wbg_ptr=0,O.unregister(this),t}free(){const t=this.__destroy_into_raw();r.__wbg_queryengine_free(t,0)}constructor(t,n,o){const _=r.queryengine_new(t,n,o);if(_[2])throw E(_[1]);return this.__wbg_ptr=_[0]>>>0,O.register(this,this.__wbg_ptr,this),this}connect(t,n){const o=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),_=s,i=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),c=s;return r.queryengine_connect(this.__wbg_ptr,o,_,i,c)}disconnect(t,n){const o=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),_=s,i=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),c=s;return r.queryengine_disconnect(this.__wbg_ptr,o,_,i,c)}query(t,n,o,_){const i=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),c=s,u=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),a=s;var d=l(o)?0:f(o,r.__wbindgen_malloc,r.__wbindgen_realloc),x=s;const F=f(_,r.__wbindgen_malloc,r.__wbindgen_realloc),M=s;return r.queryengine_query(this.__wbg_ptr,i,c,u,a,d,x,F,M)}startTransaction(t,n,o){const _=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),i=s,c=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),u=s,a=f(o,r.__wbindgen_malloc,r.__wbindgen_realloc),d=s;return r.queryengine_startTransaction(this.__wbg_ptr,_,i,c,u,a,d)}commitTransaction(t,n,o){const _=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),i=s,c=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),u=s,a=f(o,r.__wbindgen_malloc,r.__wbindgen_realloc),d=s;return r.queryengine_commitTransaction(this.__wbg_ptr,_,i,c,u,a,d)}rollbackTransaction(t,n,o){const _=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),i=s,c=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),u=s,a=f(o,r.__wbindgen_malloc,r.__wbindgen_realloc),d=s;return r.queryengine_rollbackTransaction(this.__wbg_ptr,_,i,c,u,a,d)}metrics(t){const n=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),o=s;return r.queryengine_metrics(this.__wbg_ptr,n,o)}trace(t){const n=f(t,r.__wbindgen_malloc,r.__wbindgen_realloc),o=s;return r.queryengine_trace(this.__wbg_ptr,n,o)}}function z(e,t){const n=String(t),o=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),_=s;g().setInt32(e+4*1,_,!0),g().setInt32(e+4*0,o,!0)}function W(e){return e.buffer}function P(){return b(function(e,t){return e.call(t)},arguments)}function G(){return b(function(e,t,n){return e.call(t,n)},arguments)}function Q(e){return e.crypto}function H(e){return e.done}function J(e){return Object.entries(e)}function K(){return b(function(e,t){e.getRandomValues(t)},arguments)}function X(e){return e.getTime()}function Y(){return b(function(e,t){return Reflect.get(e,t)},arguments)}function Z(e,t){return e[t>>>0]}function ee(){return b(function(e,t){return e[t]},arguments)}function te(e,t){return e[t]}function ne(){return b(function(e,t){return Reflect.has(e,t)},arguments)}function re(e){let t;try{t=e instanceof ArrayBuffer}catch{t=!1}return t}function oe(e){let t;try{t=e instanceof Map}catch{t=!1}return t}function _e(e){let t;try{t=e instanceof Promise}catch{t=!1}return t}function ce(e){let t;try{t=e instanceof Uint8Array}catch{t=!1}return t}function ie(e){return Array.isArray(e)}function ue(e){return Number.isSafeInteger(e)}function se(){return Symbol.iterator}function fe(e){return Object.keys(e)}function ae(e){return e.length}function be(e){return e.length}function le(e){return e.msCrypto}function ge(){return new Date}function de(e,t){try{var n={a:e,b:t},o=(i,c)=>{const u=n.a;n.a=0;try{return U(u,n.b,i,c)}finally{n.a=u}};return new Promise(o)}finally{n.a=n.b=0}}function we(){return new Object}function pe(){return new Map}function xe(){return new Array}function ye(e){return new Uint8Array(e)}function me(e,t){return new y(q(e,t))}function he(e,t,n){return new Uint8Array(e,t>>>0,n>>>0)}function Te(e){return new Uint8Array(e>>>0)}function qe(e){return e.next}function Se(){return b(function(e){return e.next()},arguments)}function Ae(e){return e.node}function Ie(e){return e.now()}function Ee(){return Date.now()}function Oe(){return b(function(){return Date.now()},arguments)}function Fe(e){return e.process}function Me(e,t){return e.push(t)}function je(e){return e.queueMicrotask}function ke(e){queueMicrotask(e)}function De(){return b(function(e,t){e.randomFillSync(t)},arguments)}function Re(){return b(function(){return module.require},arguments)}function Be(e){return Promise.resolve(e)}function Ue(e,t){return setTimeout(e,t>>>0)}function Le(e,t,n){e[t>>>0]=n}function ve(e,t,n){e[t]=n}function Ne(e,t,n){e.set(t,n>>>0)}function $e(e,t,n){return e.set(t,n)}function Ce(){return b(function(e,t,n){return Reflect.set(e,t,n)},arguments)}function Ve(){const e=typeof global>"u"?null:global;return l(e)?0:p(e)}function ze(){const e=typeof globalThis>"u"?null:globalThis;return l(e)?0:p(e)}function We(){const e=typeof self>"u"?null:self;return l(e)?0:p(e)}function Pe(){const e=typeof window>"u"?null:window;return l(e)?0:p(e)}function Ge(e,t,n){return e.subarray(t>>>0,n>>>0)}function Qe(e,t){return e.then(t)}function He(e,t,n){return e.then(t,n)}function Je(e){return e.valueOf()}function Ke(e){return e.value}function Xe(e){return e.versions}function Ye(e){return+e}function Ze(e){return e}function et(e){return BigInt.asUintN(64,e)}function tt(e,t){const n=t,o=typeof n=="bigint"?n:void 0;g().setBigInt64(e+8*1,l(o)?BigInt(0):o,!0),g().setInt32(e+4*0,!l(o),!0)}function nt(e){const t=e;return typeof t=="boolean"?t?1:0:2}function rt(e){const t=e.original;return t.cnt--==1?(t.a=0,!0):!1}function ot(e,t,n){return R(e,t,581,B)}function _t(e,t){const n=S(t),o=f(n,r.__wbindgen_malloc,r.__wbindgen_realloc),_=s;g().setInt32(e+4*1,_,!0),g().setInt32(e+4*0,o,!0)}function ct(e,t){return new Error(q(e,t))}function it(e,t){return e in t}function ut(){const e=r.__wbindgen_export_4,t=e.grow(4);e.set(0,void 0),e.set(t+0,void 0),e.set(t+1,null),e.set(t+2,!0),e.set(t+3,!1)}function st(e){return typeof e=="bigint"}function ft(e){return typeof e=="function"}function at(e){const t=e;return typeof t=="object"&&t!==null}function bt(e){return typeof e=="string"}function lt(e){return e===void 0}function gt(e,t){return e===t}function dt(e,t){return e==t}function wt(){return r.memory}function pt(e,t){const n=t,o=typeof n=="number"?n:void 0;g().setFloat64(e+8*1,l(o)?0:o,!0),g().setInt32(e+4*0,!l(o),!0)}function xt(e){return e}function yt(e,t){const n=t,o=typeof n=="string"?n:void 0;var _=l(o)?0:f(o,r.__wbindgen_malloc,r.__wbindgen_realloc),i=s;g().setInt32(e+4*1,i,!0),g().setInt32(e+4*0,_,!0)}function mt(e,t){return q(e,t)}function ht(e,t){throw new Error(q(e,t))}export{V as QueryEngine,z as __wbg_String_8f0eb39a4a4c2f66,W as __wbg_buffer_609cc3eee51ed158,P as __wbg_call_672a4d21634d4a24,G as __wbg_call_7cccdd69e0791ae2,Q as __wbg_crypto_805be4ce92f1e370,H as __wbg_done_769e5ede4b31c67b,J as __wbg_entries_3265d4158b33e5dc,K as __wbg_getRandomValues_f6a868620c8bab49,X as __wbg_getTime_46267b1c24877e30,Y as __wbg_get_67b2ba62fc30de12,Z as __wbg_get_b9b93047fe3cf45b,ee as __wbg_get_ece95cf6585650d9,te as __wbg_getwithrefkey_1dc361bd10053bfe,ne as __wbg_has_a5ea9117f258a0ec,re as __wbg_instanceof_ArrayBuffer_e14585432e3737fc,oe as __wbg_instanceof_Map_f3469ce2244d2430,_e as __wbg_instanceof_Promise_935168b8f4b49db3,ce as __wbg_instanceof_Uint8Array_17156bcf118086a9,ie as __wbg_isArray_a1eab7e0d067391b,ue as __wbg_isSafeInteger_343e2beeeece1bb0,se as __wbg_iterator_9a24c88df860dc65,fe as __wbg_keys_5c77a08ddc2fb8a6,ae as __wbg_length_a446193dc22c12f8,be as __wbg_length_e2d2a49132c1b256,le as __wbg_msCrypto_2ac4d17c4748234a,ge as __wbg_new0_f788a2397c7ca929,de as __wbg_new_23a2665fac83c611,we as __wbg_new_405e22f390576ce2,pe as __wbg_new_5e0be73521bc8c17,xe as __wbg_new_78feb108b6472713,ye as __wbg_new_a12002a7f91c75be,me as __wbg_newnoargs_105ed471475aaf50,he as __wbg_newwithbyteoffsetandlength_d97e637ebe145a9a,Te as __wbg_newwithlength_a381634e90c276d4,qe as __wbg_next_25feadfc0913fea9,Se as __wbg_next_6574e1a8a62d1055,Ae as __wbg_node_ecc8306b9857f33d,Ie as __wbg_now_7fd00a794a07d388,Ee as __wbg_now_807e54c39636c349,Oe as __wbg_now_b3f7572f6ef3d3a9,Fe as __wbg_process_5cff2739921be718,Me as __wbg_push_737cfc8c1432c2c6,je as __wbg_queueMicrotask_5a8a9131f3f0b37b,ke as __wbg_queueMicrotask_6d79674585219521,De as __wbg_randomFillSync_d3c85af7e31cf1f8,Re as __wbg_require_0c566c6f2eef6c79,Be as __wbg_resolve_4851785c9c5f573d,Ue as __wbg_setTimeout_5d6a1d4fc51ea450,Le as __wbg_set_37837023f3d740e8,ve as __wbg_set_3f1d0b984ed272ed,Ne as __wbg_set_65595bdd868b3009,$e as __wbg_set_8fc6bf8a5b1071d1,Ce as __wbg_set_bb8cecf6a62b9f46,N as __wbg_set_wasm,Ve as __wbg_static_accessor_GLOBAL_88a902d13a557d07,ze as __wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0,We as __wbg_static_accessor_SELF_37c5d418e4bf5819,Pe as __wbg_static_accessor_WINDOW_5de37043a91a9c40,Ge as __wbg_subarray_aa9065fa9dc5df96,Qe as __wbg_then_44b73946d2fb3e7d,He as __wbg_then_48b406749878a531,Je as __wbg_valueOf_7392193dd78c6b97,Ke as __wbg_value_cd1ffa7b1ab794f1,Xe as __wbg_versions_a8e5a362e1f16442,Ye as __wbindgen_as_number,Ze as __wbindgen_bigint_from_i64,et as __wbindgen_bigint_from_u64,tt as __wbindgen_bigint_get_as_i64,nt as __wbindgen_boolean_get,rt as __wbindgen_cb_drop,ot as __wbindgen_closure_wrapper7516,_t as __wbindgen_debug_string,ct as __wbindgen_error_new,it as __wbindgen_in,ut as __wbindgen_init_externref_table,st as __wbindgen_is_bigint,ft as __wbindgen_is_function,at as __wbindgen_is_object,bt as __wbindgen_is_string,lt as __wbindgen_is_undefined,gt as __wbindgen_jsval_eq,dt as __wbindgen_jsval_loose_eq,wt as __wbindgen_memory,pt as __wbindgen_number_get,xt as __wbindgen_number_new,yt as __wbindgen_string_get,mt as __wbindgen_string_new,ht as __wbindgen_throw,C as debug_panic,$ as getBuildTimeInfo};
