{"version": 3, "sources": ["../../../../src/next-devtools/userspace/app/terminal-logging-config.ts"], "sourcesContent": ["export function getTerminalLoggingConfig():\n  | false\n  | boolean\n  | {\n      depthLimit?: number\n      edgeLimit?: number\n      showSourceLocation?: boolean\n    } {\n  try {\n    return JSON.parse(\n      process.env.__NEXT_BROWSER_DEBUG_INFO_IN_TERMINAL || 'false'\n    )\n  } catch {\n    return false\n  }\n}\n\nexport function getIsTerminalLoggingEnabled(): boolean {\n  const config = getTerminalLoggingConfig()\n  return Boolean(config)\n}\n"], "names": ["getTerminalLoggingConfig", "JSON", "parse", "process", "env", "__NEXT_BROWSER_DEBUG_INFO_IN_TERMINAL", "getIsTerminalLoggingEnabled", "config", "Boolean"], "mappings": "AAAA,OAAO,SAASA;IAQd,IAAI;QACF,OAAOC,KAAKC,KAAK,CACfC,QAAQC,GAAG,CAACC,qCAAqC,IAAI;IAEzD,EAAE,UAAM;QACN,OAAO;IACT;AACF;AAEA,OAAO,SAASC;IACd,MAAMC,SAASP;IACf,OAAOQ,QAAQD;AACjB", "ignoreList": [0]}