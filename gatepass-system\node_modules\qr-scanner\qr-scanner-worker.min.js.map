{"version": 3, "file": "qr-scanner-worker.min.js", "sources": ["node_modules/jsqr-es6/src/BitMatrix.ts", "node_modules/jsqr-es6/src/binarizer/index.ts", "node_modules/jsqr-es6/src/decoder/decodeData/BitStream.ts", "node_modules/jsqr-es6/src/decoder/decodeData/index.ts", "node_modules/jsqr-es6/src/decoder/reedsolomon/GenericGFPoly.ts", "node_modules/jsqr-es6/src/decoder/reedsolomon/GenericGF.ts", "node_modules/jsqr-es6/src/decoder/reedsolomon/index.ts", "node_modules/jsqr-es6/src/decoder/version.ts", "node_modules/jsqr-es6/src/decoder/decoder.ts", "node_modules/jsqr-es6/src/extractor/index.ts", "node_modules/jsqr-es6/src/locator/index.ts", "node_modules/jsqr-es6/src/index.ts", "src/worker.ts"], "sourcesContent": ["export class BitMatrix {\n  public static createEmpty(width: number, height: number) {\n    return new BitMatrix(new Uint8ClampedArray(width * height), width);\n  }\n\n  public width: number;\n  public height: number;\n  private data: Uint8ClampedArray;\n\n  constructor(data: Uint8ClampedArray, width: number) {\n    this.width = width;\n    this.height = data.length / width;\n    this.data = data;\n  }\n\n  public get(x: number, y: number): boolean {\n    if (x < 0 || x >= this.width || y < 0 || y >= this.height) {\n      return false;\n    }\n    return !!this.data[y * this.width + x];\n  }\n\n  public set(x: number, y: number, v: boolean) {\n    this.data[y * this.width + x] = v ? 1 : 0;\n  }\n\n  public setRegion(left: number, top: number, width: number, height: number, v: boolean) {\n    for (let y = top; y < top + height; y++) {\n      for (let x = left; x < left + width; x++) {\n        this.set(x, y, !!v);\n      }\n    }\n  }\n}\n", "import {BitMatrix} from \"../BitMatrix\";\nimport {GreyscaleWeights} from \"../index\";\n\nconst REGION_SIZE = 8;\nconst MIN_DYNAMIC_RANGE = 24;\n\nfunction numBetween(value: number, min: number, max: number): number {\n  return value < min ? min : value > max ? max : value;\n}\n\n// Like BitMatrix but accepts arbitry Uint8 values\nclass Matrix {\n  private data: Uint8ClampedArray;\n  private width: number;\n  constructor(width: number, height: number, buffer?: Uint8ClampedArray) {\n    this.width = width;\n    const bufferSize = width * height;\n    if (buffer && buffer.length !== bufferSize) {\n      throw new Error(\"Wrong buffer size\");\n    }\n    this.data = buffer || new Uint8ClampedArray(bufferSize);\n  }\n  public get(x: number, y: number) {\n    return this.data[y * this.width + x];\n  }\n  public set(x: number, y: number, value: number) {\n    this.data[y * this.width + x] = value;\n  }\n}\n\nexport function binarize(data: Uint8ClampedArray, width: number, height: number, returnInverted: boolean,\n                         greyscaleWeights: GreyscaleWeights, canOverwriteImage: boolean) {\n  const pixelCount = width * height;\n  if (data.length !== pixelCount * 4) {\n    throw new Error(\"Malformed data passed to binarizer.\");\n  }\n  // assign the greyscale and binary image within the rgba buffer as the rgba image will not be needed after conversion\n  let bufferOffset = 0;\n  // Convert image to greyscale\n  let greyscaleBuffer: Uint8ClampedArray;\n  if (canOverwriteImage) {\n    greyscaleBuffer = new Uint8ClampedArray(data.buffer, bufferOffset, pixelCount);\n    bufferOffset += pixelCount;\n  }\n  const greyscalePixels = new Matrix(width, height, greyscaleBuffer);\n  if (greyscaleWeights.useIntegerApproximation) {\n    for (let y = 0; y < height; y++) {\n      for (let x = 0; x < width; x++) {\n        const pixelPosition = (y * width + x) * 4;\n        const r = data[pixelPosition];\n        const g = data[pixelPosition + 1];\n        const b = data[pixelPosition + 2];\n        greyscalePixels.set(x, y,\n          // tslint:disable-next-line no-bitwise\n          (greyscaleWeights.red * r + greyscaleWeights.green * g + greyscaleWeights.blue * b + 128) >> 8);\n      }\n    }\n  } else {\n    for (let y = 0; y < height; y++) {\n      for (let x = 0; x < width; x++) {\n        const pixelPosition = (y * width + x) * 4;\n        const r = data[pixelPosition];\n        const g = data[pixelPosition + 1];\n        const b = data[pixelPosition + 2];\n        greyscalePixels.set(x, y,\n          greyscaleWeights.red * r + greyscaleWeights.green * g + greyscaleWeights.blue * b);\n      }\n    }\n  }\n  const horizontalRegionCount = Math.ceil(width / REGION_SIZE);\n  const verticalRegionCount = Math.ceil(height / REGION_SIZE);\n  const blackPointsCount = horizontalRegionCount * verticalRegionCount;\n\n  let blackPointsBuffer: Uint8ClampedArray;\n  if (canOverwriteImage) {\n    blackPointsBuffer = new Uint8ClampedArray(data.buffer, bufferOffset, blackPointsCount);\n    bufferOffset += blackPointsCount;\n  }\n  const blackPoints = new Matrix(horizontalRegionCount, verticalRegionCount, blackPointsBuffer);\n  for (let verticalRegion = 0; verticalRegion < verticalRegionCount; verticalRegion++) {\n    for (let hortizontalRegion = 0; hortizontalRegion < horizontalRegionCount; hortizontalRegion++) {\n      let min = Infinity;\n      let max = 0;\n      for (let y = 0; y < REGION_SIZE; y++) {\n        for (let x = 0; x < REGION_SIZE; x++) {\n          const pixelLumosity =\n            greyscalePixels.get(hortizontalRegion * REGION_SIZE + x, verticalRegion * REGION_SIZE + y);\n          min = Math.min(min, pixelLumosity);\n          max = Math.max(max, pixelLumosity);\n        }\n      }\n      // We could also compute the real average of all pixels but following the assumption that the qr code consists\n      // of bright and dark pixels and essentially not much in between, by (min + max)/2 we make the cut really between\n      // those two classes. If using the average over all pixel in a block of mostly bright pixels and few dark pixels,\n      // the avg would tend to the bright side and darker bright pixels could be interpreted as dark.\n      let average = (min + max) / 2;\n      // Small bias towards black by moving the threshold up. We do this, as in the finder patterns white holes tend\n      // to appear which makes them undetectable.\n      const blackBias = 1.11;\n      average = Math.min(255, average * blackBias);\n      if (max - min <= MIN_DYNAMIC_RANGE) {\n        // If variation within the block is low, assume this is a block with only light or only\n        // dark pixels. In that case we do not want to use the average, as it would divide this\n        // low contrast area into black and white pixels, essentially creating data out of noise.\n        //\n        // Default the blackpoint for these blocks to be half the min - effectively white them out\n        average = min / 2;\n\n        if (verticalRegion > 0 && hortizontalRegion > 0) {\n          // Correct the \"white background\" assumption for blocks that have neighbors by comparing\n          // the pixels in this block to the previously calculated black points. This is based on\n          // the fact that dark barcode symbology is always surrounded by some amount of light\n          // background for which reasonable black point estimates were made. The bp estimated at\n          // the boundaries is used for the interior.\n\n          // The (min < bp) is arbitrary but works better than other heuristics that were tried.\n          const averageNeighborBlackPoint = (\n            blackPoints.get(hortizontalRegion, verticalRegion - 1) +\n            (2 * blackPoints.get(hortizontalRegion - 1, verticalRegion)) +\n            blackPoints.get(hortizontalRegion - 1, verticalRegion - 1)\n          ) / 4;\n          if (min < averageNeighborBlackPoint) {\n            average = averageNeighborBlackPoint; // no need to apply black bias as already applied to neighbors\n          }\n        }\n      }\n      blackPoints.set(hortizontalRegion, verticalRegion, average);\n    }\n  }\n\n  let binarized: BitMatrix;\n  if (canOverwriteImage) {\n    const binarizedBuffer = new Uint8ClampedArray(data.buffer, bufferOffset, pixelCount);\n    bufferOffset += pixelCount;\n    binarized = new BitMatrix(binarizedBuffer, width);\n  } else {\n    binarized = BitMatrix.createEmpty(width, height);\n  }\n\n  let inverted: BitMatrix = null;\n  if (returnInverted) {\n    if (canOverwriteImage) {\n      const invertedBuffer = new Uint8ClampedArray(data.buffer, bufferOffset, pixelCount);\n      inverted = new BitMatrix(invertedBuffer, width);\n    } else {\n      inverted = BitMatrix.createEmpty(width, height);\n    }\n  }\n\n  for (let verticalRegion = 0; verticalRegion < verticalRegionCount; verticalRegion++) {\n    for (let hortizontalRegion = 0; hortizontalRegion < horizontalRegionCount; hortizontalRegion++) {\n      const left = numBetween(hortizontalRegion, 2, horizontalRegionCount - 3);\n      const top = numBetween(verticalRegion, 2, verticalRegionCount - 3);\n      let sum = 0;\n      for (let xRegion = -2; xRegion <= 2; xRegion++) {\n        for (let yRegion = -2; yRegion <= 2; yRegion++) {\n          sum += blackPoints.get(left + xRegion, top + yRegion);\n        }\n      }\n      const threshold = sum / 25;\n      for (let xRegion = 0; xRegion < REGION_SIZE; xRegion++) {\n        for (let yRegion = 0; yRegion < REGION_SIZE; yRegion++) {\n          const x = hortizontalRegion * REGION_SIZE + xRegion;\n          const y = verticalRegion * REGION_SIZE + yRegion;\n          const lum = greyscalePixels.get(x, y);\n          binarized.set(x, y, lum <= threshold);\n          if (returnInverted) {\n            inverted.set(x, y, !(lum <= threshold));\n          }\n        }\n      }\n    }\n  }\n  if (returnInverted) {\n    return { binarized, inverted };\n  }\n  return { binarized };\n}\n", "// tslint:disable:no-bitwise\n\nexport class BitStream {\n  private bytes: Uint8ClampedArray;\n  private byteOffset: number = 0;\n  private bitOffset: number = 0;\n\n  constructor(bytes: Uint8ClampedArray) {\n    this.bytes = bytes;\n  }\n\n  public readBits(numBits: number): number {\n    if (numBits < 1 || numBits > 32 || numBits > this.available()) {\n      throw new Error(\"Cannot read \" + numBits.toString() + \" bits\");\n    }\n\n    let result = 0;\n    // First, read remainder from current byte\n    if (this.bitOffset > 0) {\n      const bitsLeft = 8 - this.bitOffset;\n      const toRead = numBits < bitsLeft ? numBits : bitsLeft;\n      const bitsToNotRead = bitsLeft - toRead;\n      const mask = (0xFF >> (8 - toRead)) << bitsToNotRead;\n      result = (this.bytes[this.byteOffset] & mask) >> bitsToNotRead;\n      numBits -= toRead;\n      this.bitOffset += toRead;\n      if (this.bitOffset === 8) {\n        this.bitOffset = 0;\n        this.byteOffset++;\n      }\n    }\n\n    // Next read whole bytes\n    if (numBits > 0) {\n      while (numBits >= 8) {\n        result = (result << 8) | (this.bytes[this.byteOffset] & 0xFF);\n        this.byteOffset++;\n        numBits -= 8;\n      }\n\n      // Finally read a partial byte\n      if (numBits > 0) {\n        const bitsToNotRead = 8 - numBits;\n        const mask = (0xFF >> bitsToNotRead) << bitsToNotRead;\n        result = (result << numBits) | ((this.bytes[this.byteOffset] & mask) >> bitsToNotRead);\n        this.bitOffset += numBits;\n      }\n    }\n    return result;\n  }\n\n  public available(): number {\n    return 8 * (this.bytes.length - this.byteOffset) - this.bitOffset;\n  }\n}\n", "// tslint:disable:no-bitwise\nimport { BitStream } from \"./BitStream\";\n\nexport interface Chunk {\n  type: Mode;\n  text: string;\n}\n\nexport interface ByteChunk {\n  type: Mode.Byte | Mode.Kanji;\n  bytes: number[];\n}\n\nexport interface ECIChunk {\n  type: Mode.ECI;\n  assignmentNumber: number;\n}\n\nexport interface StructuredAppend {\n  type: Mode.StructuredAppend;\n  currentSequence: number;\n  totalSequence: number;\n  parity: number;\n}\n\nexport type Chunks = Array<Chunk | ByteChunk | ECIChunk | StructuredAppend>;\n\nexport interface DecodedQR {\n  text: string;\n  bytes: number[];\n  chunks: Chunks;\n  version: number;\n}\n\nexport enum Mode {\n  Numeric = \"numeric\",\n  Alphanumeric = \"alphanumeric\",\n  Byte = \"byte\",\n  Kanji = \"kanji\",\n  ECI = \"eci\",\n  StructuredAppend = \"structuredappend\",\n}\n\nenum ModeByte {\n  Terminator = 0x0,\n  Numeric = 0x1,\n  Alphanumeric = 0x2,\n  Byte = 0x4,\n  Kanji = 0x8,\n  ECI = 0x7,\n  StructuredAppend = 0x3,\n  // FNC1FirstPosition = 0x5,\n  // FNC1SecondPosition = 0x9,\n}\n\nfunction decodeNumeric(stream: BitStream, size: number) {\n  const bytes: number[] = [];\n  let text = \"\";\n\n  const characterCountSize = [10, 12, 14][size];\n  let length = stream.readBits(characterCountSize);\n  // Read digits in groups of 3\n  while (length >= 3) {\n    const num = stream.readBits(10);\n    if (num >= 1000) {\n      throw new Error(\"Invalid numeric value above 999\");\n    }\n\n    const a = Math.floor(num / 100);\n    const b = Math.floor(num / 10) % 10;\n    const c = num % 10;\n\n    bytes.push(48 + a, 48 + b, 48 + c);\n    text += a.toString() + b.toString() + c.toString();\n    length -= 3;\n  }\n\n  // If the number of digits aren't a multiple of 3, the remaining digits are special cased.\n  if (length === 2) {\n    const num = stream.readBits(7);\n    if (num >= 100) {\n      throw new Error(\"Invalid numeric value above 99\");\n    }\n\n    const a = Math.floor(num / 10);\n    const b = num % 10;\n\n    bytes.push(48 + a, 48 + b);\n    text += a.toString() + b.toString();\n  } else if (length === 1) {\n    const num = stream.readBits(4);\n    if (num >= 10) {\n      throw new Error(\"Invalid numeric value above 9\");\n    }\n\n    bytes.push(48 + num);\n    text += num.toString();\n  }\n\n  return { bytes, text };\n}\n\nconst AlphanumericCharacterCodes = [\n  \"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\",\n  \"9\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\",\n  \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\",\n  \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\",\n  \" \", \"$\", \"%\", \"*\", \"+\", \"-\", \".\", \"/\", \":\",\n];\n\nfunction decodeAlphanumeric(stream: BitStream, size: number) {\n  const bytes: number[] = [];\n  let text = \"\";\n\n  const characterCountSize = [9, 11, 13][size];\n  let length = stream.readBits(characterCountSize);\n  while (length >= 2) {\n    const v = stream.readBits(11);\n\n    const a = Math.floor(v / 45);\n    const b = v % 45;\n\n    bytes.push(AlphanumericCharacterCodes[a].charCodeAt(0), AlphanumericCharacterCodes[b].charCodeAt(0));\n    text += AlphanumericCharacterCodes[a] + AlphanumericCharacterCodes[b];\n    length -= 2;\n  }\n\n  if (length === 1) {\n    const a = stream.readBits(6);\n    bytes.push(AlphanumericCharacterCodes[a].charCodeAt(0));\n    text += AlphanumericCharacterCodes[a];\n  }\n\n  return { bytes, text };\n}\n\nfunction decodeByte(stream: BitStream, size: number) {\n  const bytes: number[] = [];\n  let text = \"\";\n\n  const characterCountSize = [8, 16, 16][size];\n  const length = stream.readBits(characterCountSize);\n  for (let i = 0; i < length; i++) {\n    const b = stream.readBits(8);\n    bytes.push(b);\n  }\n  try {\n    text += decodeURIComponent(bytes.map(b => `%${(\"0\" + b.toString(16)).substr(-2)}`).join(\"\"));\n  } catch {\n    // failed to decode\n  }\n\n  return { bytes, text };\n}\n\nfunction decodeKanji(stream: BitStream, size: number) {\n  const bytes: number[] = [];\n\n  const characterCountSize = [8, 10, 12][size];\n  const length = stream.readBits(characterCountSize);\n  for (let i = 0; i < length; i++) {\n    const k = stream.readBits(13);\n\n    let c = (Math.floor(k / 0xC0) << 8) | (k % 0xC0);\n    if (c < 0x1F00) {\n      c += 0x8140;\n    } else {\n      c += 0xC140;\n    }\n\n    bytes.push(c >> 8, c & 0xFF);\n  }\n\n  const text = new TextDecoder(\"shift-jis\").decode(Uint8Array.from(bytes));\n  return { bytes, text };\n}\n\nexport function decode(data: Uint8ClampedArray, version: number): DecodedQR {\n  const stream = new BitStream(data);\n\n  // There are 3 'sizes' based on the version. 1-9 is small (0), 10-26 is medium (1) and 27-40 is large (2).\n  const size = version <= 9 ? 0 : version <= 26 ? 1 : 2;\n\n  const result: DecodedQR = {\n    text: \"\",\n    bytes: [],\n    chunks: [],\n    version,\n  };\n\n  while (stream.available() >= 4) {\n    const mode = stream.readBits(4);\n    if (mode === ModeByte.Terminator) {\n      return result;\n    } else if (mode === ModeByte.ECI) {\n      if (stream.readBits(1) === 0) {\n        result.chunks.push({\n          type: Mode.ECI,\n          assignmentNumber: stream.readBits(7),\n        });\n      } else if (stream.readBits(1) === 0) {\n        result.chunks.push({\n          type: Mode.ECI,\n          assignmentNumber: stream.readBits(14),\n        });\n      } else if (stream.readBits(1) === 0) {\n        result.chunks.push({\n          type: Mode.ECI,\n          assignmentNumber: stream.readBits(21),\n        });\n      } else {\n        // ECI data seems corrupted\n        result.chunks.push({\n          type: Mode.ECI,\n          assignmentNumber: -1,\n        });\n      }\n    } else if (mode === ModeByte.Numeric) {\n      const numericResult = decodeNumeric(stream, size);\n      result.text += numericResult.text;\n      result.bytes.push(...numericResult.bytes);\n      result.chunks.push({\n        type: Mode.Numeric,\n        text: numericResult.text,\n      });\n    } else if (mode === ModeByte.Alphanumeric) {\n      const alphanumericResult = decodeAlphanumeric(stream, size);\n      result.text += alphanumericResult.text;\n      result.bytes.push(...alphanumericResult.bytes);\n      result.chunks.push({\n        type: Mode.Alphanumeric,\n        text: alphanumericResult.text,\n      });\n    } else if (mode === ModeByte.Byte) {\n      const byteResult = decodeByte(stream, size);\n      result.text += byteResult.text;\n      result.bytes.push(...byteResult.bytes);\n      result.chunks.push({\n        type: Mode.Byte,\n        bytes: byteResult.bytes,\n        text: byteResult.text,\n      });\n    } else if (mode === ModeByte.Kanji) {\n      const kanjiResult = decodeKanji(stream, size);\n      result.text += kanjiResult.text;\n      result.bytes.push(...kanjiResult.bytes);\n      result.chunks.push({\n        type: Mode.Kanji,\n        bytes: kanjiResult.bytes,\n        text: kanjiResult.text,\n      });\n    } else if (mode === ModeByte.StructuredAppend) {\n      result.chunks.push({\n        type: Mode.StructuredAppend,\n        currentSequence: stream.readBits(4),\n        totalSequence: stream.readBits(4),\n        parity: stream.readBits(8),\n      });\n    }\n  }\n\n  // If there is no data left, or the remaining bits are all 0, then that counts as a termination marker\n  if (stream.available() === 0 || stream.readBits(stream.available()) === 0) {\n    return result;\n  }\n}\n", "import GenericGF, { addOrSubtractGF } from \"./GenericGF\";\n\nexport default class GenericGFPoly {\n  private field: GenericGF;\n  private coefficients: Uint8ClampedArray;\n\n  constructor(field: GenericGF, coefficients: Uint8ClampedArray) {\n    if (coefficients.length === 0) {\n      throw new Error(\"No coefficients.\");\n    }\n    this.field = field;\n    const coefficientsLength = coefficients.length;\n    if (coefficientsLength > 1 && coefficients[0] === 0) {\n      // Leading term must be non-zero for anything except the constant polynomial \"0\"\n      let firstNonZero = 1;\n      while (firstNonZero < coefficientsLength && coefficients[firstNonZero] === 0) {\n        firstNonZero++;\n      }\n      if (firstNonZero === coefficientsLength) {\n        this.coefficients = field.zero.coefficients;\n      } else {\n        this.coefficients = new Uint8ClampedArray(coefficientsLength - firstNonZero);\n        for (let i = 0; i < this.coefficients.length; i++) {\n          this.coefficients[i] = coefficients[firstNonZero + i];\n        }\n      }\n    } else {\n      this.coefficients = coefficients;\n    }\n  }\n\n  public degree() {\n    return this.coefficients.length - 1;\n  }\n\n  public isZero() {\n    return this.coefficients[0] === 0;\n  }\n\n  public getCoefficient(degree: number) {\n    return this.coefficients[this.coefficients.length - 1 - degree];\n  }\n\n  public addOrSubtract(other: GenericGFPoly) {\n    if (this.isZero()) {\n      return other;\n    }\n    if (other.isZero()) {\n      return this;\n    }\n\n    let smallerCoefficients = this.coefficients;\n    let largerCoefficients = other.coefficients;\n    if (smallerCoefficients.length > largerCoefficients.length) {\n      [smallerCoefficients, largerCoefficients] = [largerCoefficients, smallerCoefficients];\n    }\n    const sumDiff = new Uint8ClampedArray(largerCoefficients.length);\n    const lengthDiff = largerCoefficients.length - smallerCoefficients.length;\n    for (let i = 0; i < lengthDiff; i++) {\n      sumDiff[i] = largerCoefficients[i];\n    }\n\n    for (let i = lengthDiff; i < largerCoefficients.length; i++) {\n      sumDiff[i] = addOrSubtractGF(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);\n    }\n\n    return new GenericGFPoly(this.field, sumDiff);\n  }\n\n  public multiply(scalar: number) {\n    if (scalar === 0) {\n      return this.field.zero;\n    }\n    if (scalar === 1) {\n      return this;\n    }\n    const size = this.coefficients.length;\n    const product = new Uint8ClampedArray(size);\n    for (let i = 0; i < size; i++) {\n      product[i] = this.field.multiply(this.coefficients[i], scalar);\n    }\n\n    return new GenericGFPoly(this.field, product);\n  }\n\n  public multiplyPoly(other: GenericGFPoly): GenericGFPoly {\n    if (this.isZero() || other.isZero()) {\n      return this.field.zero;\n    }\n    const aCoefficients = this.coefficients;\n    const aLength = aCoefficients.length;\n    const bCoefficients = other.coefficients;\n    const bLength = bCoefficients.length;\n    const product = new Uint8ClampedArray(aLength + bLength - 1);\n    for (let i = 0; i < aLength; i++) {\n      const aCoeff = aCoefficients[i];\n      for (let j = 0; j < bLength; j++) {\n        product[i + j] = addOrSubtractGF(product[i + j],\n          this.field.multiply(aCoeff, bCoefficients[j]));\n      }\n    }\n    return new GenericGFPoly(this.field, product);\n  }\n\n  public multiplyByMonomial(degree: number, coefficient: number) {\n    if (degree < 0) {\n      throw new Error(\"Invalid degree less than 0\");\n    }\n    if (coefficient === 0) {\n      return this.field.zero;\n    }\n    const size = this.coefficients.length;\n    const product = new Uint8ClampedArray(size + degree);\n    for (let i = 0; i < size; i++) {\n      product[i] = this.field.multiply(this.coefficients[i], coefficient);\n    }\n    return new GenericGFPoly(this.field, product);\n  }\n\n  public evaluateAt(a: number) {\n    let result = 0;\n    if (a === 0) {\n      // Just return the x^0 coefficient\n      return this.getCoefficient(0);\n    }\n    const size = this.coefficients.length;\n    if (a === 1) {\n      // Just the sum of the coefficients\n      this.coefficients.forEach((coefficient) => {\n        result = addOrSubtractGF(result, coefficient);\n      });\n      return result;\n    }\n    result = this.coefficients[0];\n    for (let i = 1; i < size; i++) {\n      result = addOrSubtractGF(this.field.multiply(a, result), this.coefficients[i]);\n    }\n    return result;\n  }\n}\n", "import GenericGFPoly from \"./GenericGFPoly\";\n\nexport function addOrSubtractGF(a: number, b: number) {\n  return a ^ b; // tslint:disable-line:no-bitwise\n}\n\nexport default class GenericGF {\n  public primitive: number;\n  public size: number;\n  public generatorBase: number;\n  public zero: GenericGFPoly;\n  public one: GenericGFPoly;\n\n  private expTable: number[];\n  private logTable: number[];\n\n  constructor(primitive: number, size: number, genBase: number) {\n    this.primitive = primitive;\n    this.size = size;\n    this.generatorBase = genBase;\n    this.expTable = new Array(this.size);\n    this.logTable = new Array(this.size);\n\n    let x = 1;\n    for (let i = 0; i < this.size; i++) {\n      this.expTable[i] = x;\n      x = x * 2;\n      if (x >= this.size) {\n        x = (x ^ this.primitive) & (this.size - 1); // tslint:disable-line:no-bitwise\n      }\n    }\n\n    for (let i = 0; i < this.size - 1; i++) {\n      this.logTable[this.expTable[i]] = i;\n    }\n    this.zero = new GenericGFPoly(this, Uint8ClampedArray.from([0]));\n    this.one = new GenericGFPoly(this, Uint8ClampedArray.from([1]));\n  }\n\n  public multiply(a: number, b: number) {\n    if (a === 0 || b === 0) {\n      return 0;\n    }\n    return this.expTable[(this.logTable[a] + this.logTable[b]) % (this.size - 1)];\n  }\n\n  public inverse(a: number) {\n    if (a === 0) {\n      throw new Error(\"Can't invert 0\");\n    }\n    return this.expTable[this.size - this.logTable[a] - 1];\n  }\n\n  public buildMonomial(degree: number, coefficient: number): GenericGFPoly {\n    if (degree < 0) {\n      throw new Error(\"Invalid monomial degree less than 0\");\n    }\n    if (coefficient === 0) {\n      return this.zero;\n    }\n    const coefficients = new Uint8ClampedArray(degree + 1);\n    coefficients[0] = coefficient;\n    return new GenericGFPoly(this, coefficients);\n  }\n\n  public log(a: number) {\n    if (a === 0) {\n      throw new Error(\"Can't take log(0)\");\n    }\n    return this.logTable[a];\n  }\n\n  public exp(a: number) {\n    return this.expTable[a];\n  }\n}\n", "import GenericGF, { addOrSubtractGF } from \"./GenericGF\";\nimport GenericGFPoly from \"./GenericGFPoly\";\n\nfunction runEuclideanAlgorithm(field: GenericGF, a: GenericGFPoly, b: GenericGFPoly, R: number): GenericGFPoly[] {\n  // Assume a's degree is >= b's\n  if (a.degree() < b.degree()) {\n    [a, b] = [b, a];\n  }\n\n  let rLast = a;\n  let r = b;\n  let tLast = field.zero;\n  let t = field.one;\n\n  // Run Euclidean algorithm until r's degree is less than R/2\n  while (r.degree() >= R / 2) {\n    const rLastLast = rLast;\n    const tLastLast = tLast;\n    rLast = r;\n    tLast = t;\n\n    // Divide rLastLast by rLast, with quotient in q and remainder in r\n    if (rLast.isZero()) {\n      // Euclidean algorithm already terminated?\n      return null;\n    }\n    r = rLastLast;\n    let q = field.zero;\n    const denominatorLeadingTerm = rLast.getCoefficient(rLast.degree());\n    const dltInverse = field.inverse(denominatorLeadingTerm);\n    while (r.degree() >= rLast.degree() && !r.isZero()) {\n      const degreeDiff = r.degree() - rLast.degree();\n      const scale = field.multiply(r.getCoefficient(r.degree()), dltInverse);\n      q = q.addOrSubtract(field.buildMonomial(degreeDiff, scale));\n      r = r.addOrSubtract(rLast.multiplyByMonomial(degreeDiff, scale));\n    }\n\n    t = q.multiplyPoly(tLast).addOrSubtract(tLastLast);\n\n    if (r.degree() >= rLast.degree()) {\n      return null;\n    }\n  }\n\n  const sigmaTildeAtZero = t.getCoefficient(0);\n  if (sigmaTildeAtZero === 0) {\n    return null;\n  }\n\n  const inverse = field.inverse(sigmaTildeAtZero);\n  return [t.multiply(inverse), r.multiply(inverse)];\n}\n\nfunction findErrorLocations(field: GenericGF, errorLocator: GenericGFPoly): number[] {\n  // This is a direct application of Chien's search\n  const numErrors = errorLocator.degree();\n  if (numErrors === 1) {\n    return [errorLocator.getCoefficient(1)];\n  }\n  const result: number[] = new Array(numErrors);\n  let errorCount = 0;\n  for (let i = 1; i < field.size && errorCount < numErrors; i++) {\n    if (errorLocator.evaluateAt(i) === 0) {\n      result[errorCount] = field.inverse(i);\n      errorCount++;\n    }\n  }\n  if (errorCount !== numErrors) {\n    return null;\n  }\n  return result;\n}\n\nfunction findErrorMagnitudes(field: GenericGF, errorEvaluator: GenericGFPoly, errorLocations: number[]): number[] {\n  // This is directly applying Forney's Formula\n  const s = errorLocations.length;\n  const result: number[] = new Array(s);\n  for (let i = 0; i < s; i++) {\n    const xiInverse = field.inverse(errorLocations[i]);\n    let denominator = 1;\n    for (let j = 0; j < s; j++) {\n      if (i !== j) {\n        denominator = field.multiply(denominator, addOrSubtractGF(1, field.multiply(errorLocations[j], xiInverse)));\n      }\n    }\n    result[i] = field.multiply(errorEvaluator.evaluateAt(xiInverse), field.inverse(denominator));\n    if (field.generatorBase !== 0) {\n      result[i] = field.multiply(result[i], xiInverse);\n    }\n  }\n  return result;\n}\n\nexport function decode(bytes: number[], twoS: number) {\n  const outputBytes = new Uint8ClampedArray(bytes.length);\n  outputBytes.set(bytes);\n\n  const field = new GenericGF(0x011D, 256, 0); // x^8 + x^4 + x^3 + x^2 + 1\n  const poly = new GenericGFPoly(field, outputBytes);\n\n  const syndromeCoefficients = new Uint8ClampedArray(twoS);\n  let error = false;\n  for (let s = 0; s < twoS; s++) {\n    const evaluation = poly.evaluateAt(field.exp(s + field.generatorBase));\n    syndromeCoefficients[syndromeCoefficients.length - 1 - s] = evaluation;\n    if (evaluation !== 0) {\n      error = true;\n    }\n  }\n  if (!error) {\n    return outputBytes;\n  }\n\n  const syndrome = new GenericGFPoly(field, syndromeCoefficients);\n\n  const sigmaOmega = runEuclideanAlgorithm(field, field.buildMonomial(twoS, 1), syndrome, twoS);\n  if (sigmaOmega === null) {\n    return null;\n  }\n\n  const errorLocations = findErrorLocations(field, sigmaOmega[0]);\n  if (errorLocations == null) {\n    return null;\n  }\n\n  const errorMagnitudes = findErrorMagnitudes(field, sigmaOmega[1], errorLocations);\n  for (let i = 0; i < errorLocations.length; i++) {\n    const position = outputBytes.length - 1 - field.log(errorLocations[i]);\n    if (position < 0) {\n      return null;\n    }\n    outputBytes[position] = addOrSubtractGF(outputBytes[position], errorMagnitudes[i]);\n  }\n\n  return outputBytes;\n}\n", "export interface Version {\n  infoBits: number;\n  versionNumber: number;\n  alignmentPatternCenters: number[];\n  errorCorrectionLevels: Array<{\n    ecCodewordsPerBlock: number;\n    ecBlocks: Array<{\n      numBlocks: number;\n      dataCodewordsPerBlock: number;\n    }>\n  }>;\n}\n\nexport const VERSIONS: Version[] = [\n  {\n    infoBits: null,\n    versionNumber: 1,\n    alignmentPatternCenters: [],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 7,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 19 }],\n      },\n      {\n        ecCodewordsPerBlock: 10,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 16 }],\n      },\n      {\n        ecCodewordsPerBlock: 13,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 13 }],\n      },\n      {\n        ecCodewordsPerBlock: 17,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 9 }],\n      },\n    ],\n  },\n  {\n    infoBits: null,\n    versionNumber: 2,\n    alignmentPatternCenters: [6, 18],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 10,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 34 }],\n      },\n      {\n        ecCodewordsPerBlock: 16,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 28 }],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 22 }],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 16 }],\n      },\n    ],\n  },\n  {\n    infoBits: null,\n    versionNumber: 3,\n    alignmentPatternCenters: [6, 22],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 15,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 55 }],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 44 }],\n      },\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 17 }],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 13 }],\n      },\n    ],\n  },\n  {\n    infoBits: null,\n    versionNumber: 4,\n    alignmentPatternCenters: [6, 26],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 20,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 80 }],\n      },\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 32 }],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 24 }],\n      },\n      {\n        ecCodewordsPerBlock: 16,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 9 }],\n      },\n    ],\n  },\n  {\n    infoBits: null,\n    versionNumber: 5,\n    alignmentPatternCenters: [6, 30],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 108 }],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 43 }],\n      },\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 15 },\n          { numBlocks: 2, dataCodewordsPerBlock: 16 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 11 },\n          { numBlocks: 2, dataCodewordsPerBlock: 12 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: null,\n    versionNumber: 6,\n    alignmentPatternCenters: [6, 34],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 68 }],\n      },\n      {\n        ecCodewordsPerBlock: 16,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 27 }],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 19 }],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 15 }],\n      },\n    ],\n  },\n  {\n    infoBits: 0x07C94,\n    versionNumber: 7,\n    alignmentPatternCenters: [6, 22, 38],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 20,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 78 }],\n      },\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 31 }],\n      },\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 14 },\n          { numBlocks: 4, dataCodewordsPerBlock: 15 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 13 },\n          { numBlocks: 1, dataCodewordsPerBlock: 14 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x085BC,\n    versionNumber: 8,\n    alignmentPatternCenters: [6, 24, 42],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 97 }],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 38 },\n          { numBlocks: 2, dataCodewordsPerBlock: 39 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 18 },\n          { numBlocks: 2, dataCodewordsPerBlock: 19 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 14 },\n          { numBlocks: 2, dataCodewordsPerBlock: 15 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x09A99,\n    versionNumber: 9,\n    alignmentPatternCenters: [6, 26, 46],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 116 }],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 36 },\n          { numBlocks: 2, dataCodewordsPerBlock: 37 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 20,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 16 },\n          { numBlocks: 4, dataCodewordsPerBlock: 17 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 12 },\n          { numBlocks: 4, dataCodewordsPerBlock: 13 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0A4D3,\n    versionNumber: 10,\n    alignmentPatternCenters: [6, 28, 50],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 68 },\n          { numBlocks: 2, dataCodewordsPerBlock: 69 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 43 },\n          { numBlocks: 1, dataCodewordsPerBlock: 44 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 19 },\n          { numBlocks: 2, dataCodewordsPerBlock: 20 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 15 },\n          { numBlocks: 2, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0BBF6,\n    versionNumber: 11,\n    alignmentPatternCenters: [6, 30, 54],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 20,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 81 }],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 1, dataCodewordsPerBlock: 50 },\n          { numBlocks: 4, dataCodewordsPerBlock: 51 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 22 },\n          { numBlocks: 4, dataCodewordsPerBlock: 23 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 12 },\n          { numBlocks: 8, dataCodewordsPerBlock: 13 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0C762,\n    versionNumber: 12,\n    alignmentPatternCenters: [6, 32, 58],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 92 },\n          { numBlocks: 2, dataCodewordsPerBlock: 93 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 36 },\n          { numBlocks: 2, dataCodewordsPerBlock: 37 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 20 },\n          { numBlocks: 6, dataCodewordsPerBlock: 21 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 7, dataCodewordsPerBlock: 14 },\n          { numBlocks: 4, dataCodewordsPerBlock: 15 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0D847,\n    versionNumber: 13,\n    alignmentPatternCenters: [6, 34, 62],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 107 }],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 37 },\n          { numBlocks: 1, dataCodewordsPerBlock: 38 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 20 },\n          { numBlocks: 4, dataCodewordsPerBlock: 21 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 12, dataCodewordsPerBlock: 11 },\n          { numBlocks: 4, dataCodewordsPerBlock: 12 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0E60D,\n    versionNumber: 14,\n    alignmentPatternCenters: [6, 26, 46, 66],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 115 },\n          { numBlocks: 1, dataCodewordsPerBlock: 116 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 40 },\n          { numBlocks: 5, dataCodewordsPerBlock: 41 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 20,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 16 },\n          { numBlocks: 5, dataCodewordsPerBlock: 17 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 12 },\n          { numBlocks: 5, dataCodewordsPerBlock: 13 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0F928,\n    versionNumber: 15,\n    alignmentPatternCenters: [6, 26, 48, 70],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 87 },\n          { numBlocks: 1, dataCodewordsPerBlock: 88 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 41 },\n          { numBlocks: 5, dataCodewordsPerBlock: 42 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 24 },\n          { numBlocks: 7, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 12 },\n          { numBlocks: 7, dataCodewordsPerBlock: 13 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x10B78,\n    versionNumber: 16,\n    alignmentPatternCenters: [6, 26, 50, 74],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 98 },\n          { numBlocks: 1, dataCodewordsPerBlock: 99 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 7, dataCodewordsPerBlock: 45 },\n          { numBlocks: 3, dataCodewordsPerBlock: 46 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 15, dataCodewordsPerBlock: 19 },\n          { numBlocks: 2, dataCodewordsPerBlock: 20 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 15 },\n          { numBlocks: 13, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1145D,\n    versionNumber: 17,\n    alignmentPatternCenters: [6, 30, 54, 78],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 1, dataCodewordsPerBlock: 107 },\n          { numBlocks: 5, dataCodewordsPerBlock: 108 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 10, dataCodewordsPerBlock: 46 },\n          { numBlocks: 1, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 1, dataCodewordsPerBlock: 22 },\n          { numBlocks: 15, dataCodewordsPerBlock: 23 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 14 },\n          { numBlocks: 17, dataCodewordsPerBlock: 15 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x12A17,\n    versionNumber: 18,\n    alignmentPatternCenters: [6, 30, 56, 82],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 120 },\n          { numBlocks: 1, dataCodewordsPerBlock: 121 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 9, dataCodewordsPerBlock: 43 },\n          { numBlocks: 4, dataCodewordsPerBlock: 44 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 17, dataCodewordsPerBlock: 22 },\n          { numBlocks: 1, dataCodewordsPerBlock: 23 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 14 },\n          { numBlocks: 19, dataCodewordsPerBlock: 15 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x13532,\n    versionNumber: 19,\n    alignmentPatternCenters: [6, 30, 58, 86],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 113 },\n          { numBlocks: 4, dataCodewordsPerBlock: 114 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 44 },\n          { numBlocks: 11, dataCodewordsPerBlock: 45 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 17, dataCodewordsPerBlock: 21 },\n          { numBlocks: 4, dataCodewordsPerBlock: 22 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 9, dataCodewordsPerBlock: 13 },\n          { numBlocks: 16, dataCodewordsPerBlock: 14 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x149A6,\n    versionNumber: 20,\n    alignmentPatternCenters: [6, 34, 62, 90],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 107 },\n          { numBlocks: 5, dataCodewordsPerBlock: 108 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 41 },\n          { numBlocks: 13, dataCodewordsPerBlock: 42 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 15, dataCodewordsPerBlock: 24 },\n          { numBlocks: 5, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 15, dataCodewordsPerBlock: 15 },\n          { numBlocks: 10, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x15683,\n    versionNumber: 21,\n    alignmentPatternCenters: [6, 28, 50, 72, 94],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 116 },\n          { numBlocks: 4, dataCodewordsPerBlock: 117 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [{ numBlocks: 17, dataCodewordsPerBlock: 42 }],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 17, dataCodewordsPerBlock: 22 },\n          { numBlocks: 6, dataCodewordsPerBlock: 23 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 16 },\n          { numBlocks: 6, dataCodewordsPerBlock: 17 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x168C9,\n    versionNumber: 22,\n    alignmentPatternCenters: [6, 26, 50, 74, 98],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 111 },\n          { numBlocks: 7, dataCodewordsPerBlock: 112 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [{ numBlocks: 17, dataCodewordsPerBlock: 46 }],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 7, dataCodewordsPerBlock: 24 },\n          { numBlocks: 16, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [{ numBlocks: 34, dataCodewordsPerBlock: 13 }],\n      },\n    ],\n  },\n  {\n    infoBits: 0x177EC,\n    versionNumber: 23,\n    alignmentPatternCenters: [6, 30, 54, 74, 102],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 121 },\n          { numBlocks: 5, dataCodewordsPerBlock: 122 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 47 },\n          { numBlocks: 14, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 24 },\n          { numBlocks: 14, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 16, dataCodewordsPerBlock: 15 },\n          { numBlocks: 14, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x18EC4,\n    versionNumber: 24,\n    alignmentPatternCenters: [6, 28, 54, 80, 106],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 117 },\n          { numBlocks: 4, dataCodewordsPerBlock: 118 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 45 },\n          { numBlocks: 14, dataCodewordsPerBlock: 46 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 24 },\n          { numBlocks: 16, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 30, dataCodewordsPerBlock: 16 },\n          { numBlocks: 2, dataCodewordsPerBlock: 17 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x191E1,\n    versionNumber: 25,\n    alignmentPatternCenters: [6, 32, 58, 84, 110],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 106 },\n          { numBlocks: 4, dataCodewordsPerBlock: 107 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 47 },\n          { numBlocks: 13, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 7, dataCodewordsPerBlock: 24 },\n          { numBlocks: 22, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 22, dataCodewordsPerBlock: 15 },\n          { numBlocks: 13, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1AFAB,\n    versionNumber: 26,\n    alignmentPatternCenters: [6, 30, 58, 86, 114],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 10, dataCodewordsPerBlock: 114 },\n          { numBlocks: 2, dataCodewordsPerBlock: 115 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 46 },\n          { numBlocks: 4, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 28, dataCodewordsPerBlock: 22 },\n          { numBlocks: 6, dataCodewordsPerBlock: 23 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 33, dataCodewordsPerBlock: 16 },\n          { numBlocks: 4, dataCodewordsPerBlock: 17 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1B08E,\n    versionNumber: 27,\n    alignmentPatternCenters: [6, 34, 62, 90, 118],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 122 },\n          { numBlocks: 4, dataCodewordsPerBlock: 123 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 22, dataCodewordsPerBlock: 45 },\n          { numBlocks: 3, dataCodewordsPerBlock: 46 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 23 },\n          { numBlocks: 26, dataCodewordsPerBlock: 24 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 12, dataCodewordsPerBlock: 15 },\n          { numBlocks: 28, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1CC1A,\n    versionNumber: 28,\n    alignmentPatternCenters: [6, 26, 50, 74, 98, 122],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 117 },\n          { numBlocks: 10, dataCodewordsPerBlock: 118 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 45 },\n          { numBlocks: 23, dataCodewordsPerBlock: 46 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 24 },\n          { numBlocks: 31, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 15 },\n          { numBlocks: 31, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1D33F,\n    versionNumber: 29,\n    alignmentPatternCenters: [6, 30, 54, 78, 102, 126],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 7, dataCodewordsPerBlock: 116 },\n          { numBlocks: 7, dataCodewordsPerBlock: 117 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 21, dataCodewordsPerBlock: 45 },\n          { numBlocks: 7, dataCodewordsPerBlock: 46 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 1, dataCodewordsPerBlock: 23 },\n          { numBlocks: 37, dataCodewordsPerBlock: 24 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 15 },\n          { numBlocks: 26, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1ED75,\n    versionNumber: 30,\n    alignmentPatternCenters: [6, 26, 52, 78, 104, 130],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 115 },\n          { numBlocks: 10, dataCodewordsPerBlock: 116 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 47 },\n          { numBlocks: 10, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 15, dataCodewordsPerBlock: 24 },\n          { numBlocks: 25, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 23, dataCodewordsPerBlock: 15 },\n          { numBlocks: 25, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1F250,\n    versionNumber: 31,\n    alignmentPatternCenters: [6, 30, 56, 82, 108, 134],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 13, dataCodewordsPerBlock: 115 },\n          { numBlocks: 3, dataCodewordsPerBlock: 116 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 46 },\n          { numBlocks: 29, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 42, dataCodewordsPerBlock: 24 },\n          { numBlocks: 1, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 23, dataCodewordsPerBlock: 15 },\n          { numBlocks: 28, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x209D5,\n    versionNumber: 32,\n    alignmentPatternCenters: [6, 34, 60, 86, 112, 138],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [{ numBlocks: 17, dataCodewordsPerBlock: 115 }],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 10, dataCodewordsPerBlock: 46 },\n          { numBlocks: 23, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 10, dataCodewordsPerBlock: 24 },\n          { numBlocks: 35, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 15 },\n          { numBlocks: 35, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x216F0,\n    versionNumber: 33,\n    alignmentPatternCenters: [6, 30, 58, 86, 114, 142],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 17, dataCodewordsPerBlock: 115 },\n          { numBlocks: 1, dataCodewordsPerBlock: 116 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 14, dataCodewordsPerBlock: 46 },\n          { numBlocks: 21, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 29, dataCodewordsPerBlock: 24 },\n          { numBlocks: 19, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 15 },\n          { numBlocks: 46, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x228BA,\n    versionNumber: 34,\n    alignmentPatternCenters: [6, 34, 62, 90, 118, 146],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 13, dataCodewordsPerBlock: 115 },\n          { numBlocks: 6, dataCodewordsPerBlock: 116 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 14, dataCodewordsPerBlock: 46 },\n          { numBlocks: 23, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 44, dataCodewordsPerBlock: 24 },\n          { numBlocks: 7, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 59, dataCodewordsPerBlock: 16 },\n          { numBlocks: 1, dataCodewordsPerBlock: 17 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x2379F,\n    versionNumber: 35,\n    alignmentPatternCenters: [6, 30, 54, 78, 102, 126, 150],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 12, dataCodewordsPerBlock: 121 },\n          { numBlocks: 7, dataCodewordsPerBlock: 122 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 12, dataCodewordsPerBlock: 47 },\n          { numBlocks: 26, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 39, dataCodewordsPerBlock: 24 },\n          { numBlocks: 14, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 22, dataCodewordsPerBlock: 15 },\n          { numBlocks: 41, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x24B0B,\n    versionNumber: 36,\n    alignmentPatternCenters: [ 6, 24, 50, 76, 102, 128, 154 ],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 121 },\n          { numBlocks: 14, dataCodewordsPerBlock: 122 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 47 },\n          { numBlocks: 34, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 46, dataCodewordsPerBlock: 24 },\n          { numBlocks: 10, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 15 },\n          { numBlocks: 64, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x2542E,\n    versionNumber: 37,\n    alignmentPatternCenters: [ 6, 28, 54, 80, 106, 132, 158 ],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 17, dataCodewordsPerBlock: 122 },\n          { numBlocks: 4, dataCodewordsPerBlock: 123 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 29, dataCodewordsPerBlock: 46 },\n          { numBlocks: 14, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 49, dataCodewordsPerBlock: 24 },\n          { numBlocks: 10, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 24, dataCodewordsPerBlock: 15 },\n          { numBlocks: 46, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x26A64,\n    versionNumber: 38,\n    alignmentPatternCenters: [ 6, 32, 58, 84, 110, 136, 162 ],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 122 },\n          { numBlocks: 18, dataCodewordsPerBlock: 123 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 13, dataCodewordsPerBlock: 46 },\n          { numBlocks: 32, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 48, dataCodewordsPerBlock: 24 },\n          { numBlocks: 14, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 42, dataCodewordsPerBlock: 15 },\n          { numBlocks: 32, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x27541,\n    versionNumber: 39,\n    alignmentPatternCenters: [ 6, 26, 54, 82, 110, 138, 166 ],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 20, dataCodewordsPerBlock: 117 },\n          { numBlocks: 4, dataCodewordsPerBlock: 118 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 40, dataCodewordsPerBlock: 47 },\n          { numBlocks: 7, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 43, dataCodewordsPerBlock: 24 },\n          { numBlocks: 22, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 10, dataCodewordsPerBlock: 15 },\n          { numBlocks: 67, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x28C69,\n    versionNumber: 40,\n    alignmentPatternCenters: [ 6, 30, 58, 86, 114, 142, 170 ],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 118 },\n          { numBlocks: 6, dataCodewordsPerBlock: 119 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 18, dataCodewordsPerBlock: 47 },\n          { numBlocks: 31, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 34, dataCodewordsPerBlock: 24 },\n          { numBlocks: 34, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 20, dataCodewordsPerBlock: 15 },\n          { numBlocks: 61, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n];\n", "import { BitMatrix } from \"../BitMatrix\";\nimport { Point } from \"../Point\";\nimport { decode as decodeData, DecodedQR } from \"./decodeData\";\nimport { decode as rsDecode } from \"./reedsolomon\";\nimport { Version, VERSIONS } from \"./version\";\n\n// tslint:disable:no-bitwise\nfunction numBitsDiffering(x: number, y: number) {\n  let z = x ^ y;\n  let bitCount = 0;\n  while (z) {\n    bitCount++;\n    z &= z - 1;\n  }\n  return bitCount;\n}\n\nfunction pushBit(bit: any, byte: number) {\n  return (byte << 1) | bit;\n}\n// tslint:enable:no-bitwise\n\nconst FORMAT_INFO_TABLE = [\n  { bits: 0x5412, formatInfo: { errorCorrectionLevel: 1, dataMask: 0 } },\n  { bits: 0x5125, formatInfo: { errorCorrectionLevel: 1, dataMask: 1 } },\n  { bits: 0x5E7C, formatInfo: { errorCorrectionLevel: 1, dataMask: 2 } },\n  { bits: 0x5B4B, formatInfo: { errorCorrectionLevel: 1, dataMask: 3 } },\n  { bits: 0x45F9, formatInfo: { errorCorrectionLevel: 1, dataMask: 4 } },\n  { bits: 0x40CE, formatInfo: { errorCorrectionLevel: 1, dataMask: 5 } },\n  { bits: 0x4F97, formatInfo: { errorCorrectionLevel: 1, dataMask: 6 } },\n  { bits: 0x4AA0, formatInfo: { errorCorrectionLevel: 1, dataMask: 7 } },\n  { bits: 0x77C4, formatInfo: { errorCorrectionLevel: 0, dataMask: 0 } },\n  { bits: 0x72F3, formatInfo: { errorCorrectionLevel: 0, dataMask: 1 } },\n  { bits: 0x7DAA, formatInfo: { errorCorrectionLevel: 0, dataMask: 2 } },\n  { bits: 0x789D, formatInfo: { errorCorrectionLevel: 0, dataMask: 3 } },\n  { bits: 0x662F, formatInfo: { errorCorrectionLevel: 0, dataMask: 4 } },\n  { bits: 0x6318, formatInfo: { errorCorrectionLevel: 0, dataMask: 5 } },\n  { bits: 0x6C41, formatInfo: { errorCorrectionLevel: 0, dataMask: 6 } },\n  { bits: 0x6976, formatInfo: { errorCorrectionLevel: 0, dataMask: 7 } },\n  { bits: 0x1689, formatInfo: { errorCorrectionLevel: 3, dataMask: 0 } },\n  { bits: 0x13BE, formatInfo: { errorCorrectionLevel: 3, dataMask: 1 } },\n  { bits: 0x1CE7, formatInfo: { errorCorrectionLevel: 3, dataMask: 2 } },\n  { bits: 0x19D0, formatInfo: { errorCorrectionLevel: 3, dataMask: 3 } },\n  { bits: 0x0762, formatInfo: { errorCorrectionLevel: 3, dataMask: 4 } },\n  { bits: 0x0255, formatInfo: { errorCorrectionLevel: 3, dataMask: 5 } },\n  { bits: 0x0D0C, formatInfo: { errorCorrectionLevel: 3, dataMask: 6 } },\n  { bits: 0x083B, formatInfo: { errorCorrectionLevel: 3, dataMask: 7 } },\n  { bits: 0x355F, formatInfo: { errorCorrectionLevel: 2, dataMask: 0 } },\n  { bits: 0x3068, formatInfo: { errorCorrectionLevel: 2, dataMask: 1 } },\n  { bits: 0x3F31, formatInfo: { errorCorrectionLevel: 2, dataMask: 2 } },\n  { bits: 0x3A06, formatInfo: { errorCorrectionLevel: 2, dataMask: 3 } },\n  { bits: 0x24B4, formatInfo: { errorCorrectionLevel: 2, dataMask: 4 } },\n  { bits: 0x2183, formatInfo: { errorCorrectionLevel: 2, dataMask: 5 } },\n  { bits: 0x2EDA, formatInfo: { errorCorrectionLevel: 2, dataMask: 6 } },\n  { bits: 0x2BED, formatInfo: { errorCorrectionLevel: 2, dataMask: 7 } },\n];\n\nconst DATA_MASKS = [\n  (p: Point) => ((p.y + p.x) % 2) === 0,\n  (p: Point) => (p.y % 2) === 0,\n  (p: Point) => p.x % 3 === 0,\n  (p: Point) => (p.y + p.x) % 3 === 0,\n  (p: Point) => (Math.floor(p.y / 2) + Math.floor(p.x / 3)) % 2 === 0,\n  (p: Point) => ((p.x * p.y) % 2) + ((p.x * p.y) % 3) === 0,\n  (p: Point) => ((((p.y * p.x) % 2) + (p.y * p.x) % 3) % 2) === 0,\n  (p: Point) => ((((p.y + p.x) % 2) + (p.y * p.x) % 3) % 2) === 0,\n];\n\ninterface FormatInformation {\n  errorCorrectionLevel: number;\n  dataMask: number;\n}\n\nfunction buildFunctionPatternMask(version: Version): BitMatrix {\n  const dimension = 17 + 4 * version.versionNumber;\n  const matrix = BitMatrix.createEmpty(dimension, dimension);\n\n  matrix.setRegion(0, 0, 9, 9, true); // Top left finder pattern + separator + format\n  matrix.setRegion(dimension - 8, 0, 8, 9, true); // Top right finder pattern + separator + format\n  matrix.setRegion(0, dimension - 8, 9, 8, true); // Bottom left finder pattern + separator + format\n\n  // Alignment patterns\n  for (const x of version.alignmentPatternCenters) {\n    for (const y of version.alignmentPatternCenters) {\n      if (!(x === 6 && y === 6 || x === 6 && y === dimension - 7 || x === dimension - 7 && y === 6)) {\n        matrix.setRegion(x - 2, y - 2, 5, 5, true);\n      }\n    }\n  }\n\n  matrix.setRegion(6, 9, 1, dimension - 17, true); // Vertical timing pattern\n  matrix.setRegion(9, 6, dimension - 17, 1, true); // Horizontal timing pattern\n\n  if (version.versionNumber > 6) {\n    matrix.setRegion(dimension - 11, 0, 3, 6, true); // Version info, top right\n    matrix.setRegion(0, dimension - 11, 6, 3, true); // Version info, bottom left\n  }\n\n  return matrix;\n}\n\nfunction readCodewords(matrix: BitMatrix, version: Version, formatInfo: FormatInformation) {\n  const dataMask = DATA_MASKS[formatInfo.dataMask];\n  const dimension = matrix.height;\n\n  const functionPatternMask = buildFunctionPatternMask(version);\n\n  const codewords: number[] = [];\n  let currentByte = 0;\n  let bitsRead = 0;\n\n  // Read columns in pairs, from right to left\n  let readingUp = true;\n  for (let columnIndex = dimension - 1; columnIndex > 0; columnIndex -= 2) {\n    if (columnIndex === 6) { // Skip whole column with vertical alignment pattern;\n      columnIndex--;\n    }\n    for (let i = 0; i < dimension; i++) {\n      const y = readingUp ? dimension - 1 - i : i;\n      for (let columnOffset = 0; columnOffset < 2; columnOffset++) {\n        const x = columnIndex - columnOffset;\n        if (!functionPatternMask.get(x, y)) {\n          bitsRead++;\n          let bit = matrix.get(x, y);\n          if (dataMask({y, x})) {\n            bit = !bit;\n          }\n          currentByte = pushBit(bit, currentByte);\n          if (bitsRead === 8) { // Whole bytes\n            codewords.push(currentByte);\n            bitsRead = 0;\n            currentByte = 0;\n          }\n        }\n      }\n    }\n    readingUp = !readingUp;\n  }\n  return codewords;\n}\n\nfunction readVersion(matrix: BitMatrix): Version {\n  const dimension = matrix.height;\n\n  const provisionalVersion = Math.floor((dimension - 17) / 4);\n  if (provisionalVersion <= 6) { // 6 and under dont have version info in the QR code\n    return VERSIONS[provisionalVersion - 1];\n  }\n\n  let topRightVersionBits = 0;\n  for (let y = 5; y >= 0; y--) {\n    for (let x = dimension - 9; x >= dimension - 11; x--) {\n      topRightVersionBits = pushBit(matrix.get(x, y), topRightVersionBits);\n    }\n  }\n\n  let bottomLeftVersionBits = 0;\n  for (let x = 5; x >= 0; x--) {\n    for (let y = dimension - 9; y >= dimension - 11; y--) {\n      bottomLeftVersionBits = pushBit(matrix.get(x, y), bottomLeftVersionBits);\n    }\n  }\n\n  let bestDifference = Infinity;\n  let bestVersion: Version;\n  for (const version of VERSIONS) {\n    if (version.infoBits === topRightVersionBits || version.infoBits === bottomLeftVersionBits) {\n      return version;\n    }\n\n    let difference = numBitsDiffering(topRightVersionBits, version.infoBits);\n    if (difference < bestDifference) {\n      bestVersion = version;\n      bestDifference = difference;\n    }\n\n    difference = numBitsDiffering(bottomLeftVersionBits, version.infoBits);\n    if (difference < bestDifference) {\n      bestVersion = version;\n      bestDifference = difference;\n    }\n  }\n  // We can tolerate up to 3 bits of error since no two version info codewords will\n  // differ in less than 8 bits.\n  if (bestDifference <= 3) {\n    return bestVersion;\n  }\n}\n\nfunction readFormatInformation(matrix: BitMatrix) {\n  let topLeftFormatInfoBits = 0;\n  for (let x = 0; x <= 8; x++) {\n    if (x !== 6) { // Skip timing pattern bit\n      topLeftFormatInfoBits = pushBit(matrix.get(x, 8), topLeftFormatInfoBits);\n    }\n  }\n  for (let y = 7; y >= 0; y--) {\n    if (y !== 6) { // Skip timing pattern bit\n      topLeftFormatInfoBits = pushBit(matrix.get(8, y), topLeftFormatInfoBits);\n    }\n  }\n\n  const dimension = matrix.height;\n  let topRightBottomRightFormatInfoBits = 0;\n  for (let y = dimension - 1; y >= dimension - 7; y--) { // bottom left\n    topRightBottomRightFormatInfoBits = pushBit(matrix.get(8, y), topRightBottomRightFormatInfoBits);\n  }\n  for (let x = dimension - 8; x < dimension; x++) { // top right\n    topRightBottomRightFormatInfoBits = pushBit(matrix.get(x, 8), topRightBottomRightFormatInfoBits);\n  }\n\n  let bestDifference = Infinity;\n  let bestFormatInfo = null;\n  for (const {bits, formatInfo} of FORMAT_INFO_TABLE) {\n    if (bits === topLeftFormatInfoBits || bits === topRightBottomRightFormatInfoBits) {\n      return formatInfo;\n    }\n    let difference = numBitsDiffering(topLeftFormatInfoBits, bits);\n    if (difference < bestDifference) {\n      bestFormatInfo = formatInfo;\n      bestDifference = difference;\n    }\n    if (topLeftFormatInfoBits !== topRightBottomRightFormatInfoBits) { // also try the other option\n      difference = numBitsDiffering(topRightBottomRightFormatInfoBits, bits);\n      if (difference < bestDifference) {\n        bestFormatInfo = formatInfo;\n        bestDifference = difference;\n      }\n    }\n  }\n  // Hamming distance of the 32 masked codes is 7, by construction, so <= 3 bits differing means we found a match\n  if (bestDifference <= 3) {\n    return bestFormatInfo;\n  }\n  return null;\n}\n\nfunction getDataBlocks(codewords: number[], version: Version, ecLevel: number) {\n  const ecInfo = version.errorCorrectionLevels[ecLevel];\n  const dataBlocks: Array<{\n    numDataCodewords: number;\n    codewords: number[];\n  }> = [];\n\n  let totalCodewords = 0;\n  ecInfo.ecBlocks.forEach(block => {\n    for (let i = 0; i < block.numBlocks; i++) {\n      dataBlocks.push({ numDataCodewords: block.dataCodewordsPerBlock, codewords: [] });\n      totalCodewords += block.dataCodewordsPerBlock + ecInfo.ecCodewordsPerBlock;\n    }\n  });\n\n  // In some cases the QR code will be malformed enough that we pull off more or less than we should.\n  // If we pull off less there's nothing we can do.\n  // If we pull off more we can safely truncate\n  if (codewords.length < totalCodewords) {\n    return null;\n  }\n  codewords = codewords.slice(0, totalCodewords);\n\n  const shortBlockSize = ecInfo.ecBlocks[0].dataCodewordsPerBlock;\n  // Pull codewords to fill the blocks up to the minimum size\n  for (let i = 0; i < shortBlockSize; i++) {\n    for (const dataBlock of dataBlocks) {\n      dataBlock.codewords.push(codewords.shift());\n    }\n  }\n\n  // If there are any large blocks, pull codewords to fill the last element of those\n  if (ecInfo.ecBlocks.length > 1) {\n    const smallBlockCount = ecInfo.ecBlocks[0].numBlocks;\n    const largeBlockCount = ecInfo.ecBlocks[1].numBlocks;\n    for (let i = 0; i < largeBlockCount; i++) {\n      dataBlocks[smallBlockCount + i].codewords.push(codewords.shift());\n    }\n  }\n\n  // Add the rest of the codewords to the blocks. These are the error correction codewords.\n  while (codewords.length > 0) {\n    for (const dataBlock of dataBlocks) {\n      dataBlock.codewords.push(codewords.shift());\n    }\n  }\n\n  return dataBlocks;\n}\n\nfunction decodeMatrix(matrix: BitMatrix) {\n  const version = readVersion(matrix);\n  if (!version) {\n    return null;\n  }\n\n  const formatInfo = readFormatInformation(matrix);\n  if (!formatInfo) {\n    return null;\n  }\n\n  const codewords = readCodewords(matrix, version, formatInfo);\n  const dataBlocks = getDataBlocks(codewords, version, formatInfo.errorCorrectionLevel);\n  if (!dataBlocks) {\n    return null;\n  }\n\n  // Count total number of data bytes\n  const totalBytes = dataBlocks.reduce((a, b) => a + b.numDataCodewords, 0);\n  const resultBytes = new Uint8ClampedArray(totalBytes);\n\n  let resultIndex = 0;\n  for (const dataBlock of dataBlocks) {\n    const correctedBytes = rsDecode(dataBlock.codewords, dataBlock.codewords.length - dataBlock.numDataCodewords);\n    if (!correctedBytes) {\n      return null;\n    }\n    for (let i = 0; i < dataBlock.numDataCodewords; i++) {\n      resultBytes[resultIndex++] = correctedBytes[i];\n    }\n  }\n\n  try {\n    return decodeData(resultBytes, version.versionNumber);\n  } catch {\n    return null;\n  }\n}\n\nexport function decode(matrix: BitMatrix): DecodedQR {\n  if (matrix == null) {\n    return null;\n  }\n  const result = decodeMatrix(matrix);\n  if (result) {\n    return result;\n  }\n  // Decoding didn't work, try mirroring the QR across the topLeft -> bottomRight line.\n  for (let x = 0; x < matrix.width; x++) {\n    for (let y = x + 1; y < matrix.height; y++) {\n      if (matrix.get(x, y) !== matrix.get(y, x)) {\n        matrix.set(x, y, !matrix.get(x, y));\n        matrix.set(y, x, !matrix.get(y, x));\n      }\n    }\n  }\n  return decodeMatrix(matrix);\n}\n", "import {BitMatrix} from \"../BitMatrix\";\nimport {Point, QRLocation} from \"../locator\";\n\ninterface PerspectiveTransform {\n  a11: number;\n  a21: number;\n  a31: number;\n  a12: number;\n  a22: number;\n  a32: number;\n  a13: number;\n  a23: number;\n  a33: number;\n}\n\nfunction squareToQuadrilateral(p1: Point, p2: Point, p3: Point, p4: Point): PerspectiveTransform {\n  const dx3 = p1.x - p2.x + p3.x - p4.x;\n  const dy3 = p1.y - p2.y + p3.y - p4.y;\n  if (dx3 === 0 && dy3 === 0) { // Affine\n    return {\n      a11: p2.x - p1.x,\n      a12: p2.y - p1.y,\n      a13: 0,\n      a21: p3.x - p2.x,\n      a22: p3.y - p2.y,\n      a23: 0,\n      a31: p1.x,\n      a32: p1.y,\n      a33: 1,\n    };\n  } else {\n    const dx1 = p2.x - p3.x;\n    const dx2 = p4.x - p3.x;\n    const dy1 = p2.y - p3.y;\n    const dy2 = p4.y - p3.y;\n    const denominator = dx1 * dy2 - dx2 * dy1;\n    const a13 = (dx3 * dy2 - dx2 * dy3) / denominator;\n    const a23 = (dx1 * dy3 - dx3 * dy1) / denominator;\n    return {\n      a11: p2.x - p1.x + a13 * p2.x,\n      a12: p2.y - p1.y + a13 * p2.y,\n      a13,\n      a21: p4.x - p1.x + a23 * p4.x,\n      a22: p4.y - p1.y + a23 * p4.y,\n      a23,\n      a31: p1.x,\n      a32: p1.y,\n      a33: 1,\n    };\n  }\n}\n\nfunction quadrilateralToSquare(p1: Point, p2: Point, p3: Point, p4: Point): PerspectiveTransform {\n  // Here, the adjoint serves as the inverse:\n  const sToQ = squareToQuadrilateral(p1, p2, p3, p4);\n  return {\n    a11: sToQ.a22 * sToQ.a33 - sToQ.a23 * sToQ.a32,\n    a12: sToQ.a13 * sToQ.a32 - sToQ.a12 * sToQ.a33,\n    a13: sToQ.a12 * sToQ.a23 - sToQ.a13 * sToQ.a22,\n    a21: sToQ.a23 * sToQ.a31 - sToQ.a21 * sToQ.a33,\n    a22: sToQ.a11 * sToQ.a33 - sToQ.a13 * sToQ.a31,\n    a23: sToQ.a13 * sToQ.a21 - sToQ.a11 * sToQ.a23,\n    a31: sToQ.a21 * sToQ.a32 - sToQ.a22 * sToQ.a31,\n    a32: sToQ.a12 * sToQ.a31 - sToQ.a11 * sToQ.a32,\n    a33: sToQ.a11 * sToQ.a22 - sToQ.a12 * sToQ.a21,\n  };\n}\n\nfunction times(a: PerspectiveTransform, b: PerspectiveTransform): PerspectiveTransform {\n  return {\n    a11: a.a11 * b.a11 + a.a21 * b.a12 + a.a31 * b.a13,\n    a12: a.a12 * b.a11 + a.a22 * b.a12 + a.a32 * b.a13,\n    a13: a.a13 * b.a11 + a.a23 * b.a12 + a.a33 * b.a13,\n    a21: a.a11 * b.a21 + a.a21 * b.a22 + a.a31 * b.a23,\n    a22: a.a12 * b.a21 + a.a22 * b.a22 + a.a32 * b.a23,\n    a23: a.a13 * b.a21 + a.a23 * b.a22 + a.a33 * b.a23,\n    a31: a.a11 * b.a31 + a.a21 * b.a32 + a.a31 * b.a33,\n    a32: a.a12 * b.a31 + a.a22 * b.a32 + a.a32 * b.a33,\n    a33: a.a13 * b.a31 + a.a23 * b.a32 + a.a33 * b.a33,\n  };\n}\n\nexport function extract(image: BitMatrix, location: QRLocation) {\n  const qToS = quadrilateralToSquare(\n    {x: 3.5, y: 3.5},\n    {x: location.dimension - 3.5, y: 3.5},\n    {x: location.dimension - 6.5, y: location.dimension - 6.5},\n    {x: 3.5, y: location.dimension - 3.5},\n  );\n  const sToQ = squareToQuadrilateral(location.topLeft, location.topRight, location.alignmentPattern, location.bottomLeft);\n  const transform = times(sToQ, qToS);\n\n  const matrix = BitMatrix.createEmpty(location.dimension, location.dimension);\n  const mappingFunction = (x: number, y: number) => {\n    const denominator = transform.a13 * x + transform.a23 * y + transform.a33;\n    return {\n      x: (transform.a11 * x + transform.a21 * y + transform.a31) / denominator,\n      y: (transform.a12 * x + transform.a22 * y + transform.a32) / denominator,\n    };\n  };\n\n  for (let y = 0; y < location.dimension; y++) {\n    for (let x = 0; x < location.dimension; x++) {\n      const xValue = x + 0.5;\n      const yValue = y + 0.5;\n      const sourcePixel = mappingFunction(xValue, yValue);\n      matrix.set(x, y, image.get(Math.floor(sourcePixel.x), Math.floor(sourcePixel.y)));\n    }\n  }\n\n  return {\n    matrix,\n    mappingFunction,\n  };\n}\n", "import { BitMatrix } from \"../BitMatrix\";\n\nconst MAX_FINDERPATTERNS_TO_SEARCH = 5;\nconst MIN_QUAD_RATIO = 0.5;\nconst MAX_QUAD_RATIO = 1.5;\n\nexport interface Point {\n  x: number;\n  y: number;\n}\n\nexport interface QRLocation {\n  topRight: Point;\n  bottomLeft: Point;\n  topLeft: Point;\n  alignmentPattern: Point;\n  dimension: number;\n}\n\nconst distance = (a: Point, b: Point) => Math.sqrt((b.x - a.x) ** 2 + (b.y - a.y) ** 2);\n\nfunction sum(values: number[]) {\n  return values.reduce((a, b) => a + b);\n}\n\n// Takes three finder patterns and organizes them into topLeft, topRight, etc\nfunction reorderFinderPatterns(pattern1: Point, pattern2: Point, pattern3: Point) {\n  // Find distances between pattern centers\n  const oneTwoDistance = distance(pattern1, pattern2);\n  const twoThreeDistance = distance(pattern2, pattern3);\n  const oneThreeDistance = distance(pattern1, pattern3);\n\n  let bottomLeft: Point;\n  let topLeft: Point;\n  let topRight: Point;\n\n  // Assume one closest to other two is B; A and C will just be guesses at first\n  if (twoThreeDistance >= oneTwoDistance && twoThreeDistance >= oneThreeDistance) {\n    [bottomLeft, topLeft, topRight] = [pattern2, pattern1, pattern3];\n  } else if (oneThreeDistance >= twoThreeDistance && oneThreeDistance >= oneTwoDistance) {\n    [bottomLeft, topLeft, topRight] = [pattern1, pattern2, pattern3];\n  } else {\n    [bottomLeft, topLeft, topRight] = [pattern1, pattern3, pattern2];\n  }\n\n  // Use cross product to figure out whether bottomLeft (A) and topRight (C) are correct or flipped in relation to topLeft (B)\n  // This asks whether BC x BA has a positive z component, which is the arrangement we want. If it's negative, then\n  // we've got it flipped around and should swap topRight and bottomLeft.\n  if (((topRight.x - topLeft.x) * (bottomLeft.y - topLeft.y)) - ((topRight.y - topLeft.y) * (bottomLeft.x - topLeft.x)) < 0) {\n    [bottomLeft, topRight] = [topRight, bottomLeft];\n  }\n\n  return { bottomLeft, topLeft, topRight };\n}\n\n// Computes the dimension (number of modules on a side) of the QR Code based on the position of the finder patterns\nfunction computeDimension(topLeft: Point, topRight: Point, bottomLeft: Point, matrix: BitMatrix) {\n  const moduleSize = (\n    sum(countBlackWhiteRun(topLeft, bottomLeft, matrix, 5)) / 7 + // Divide by 7 since the ratio is 1:1:3:1:1\n    sum(countBlackWhiteRun(topLeft, topRight, matrix, 5)) / 7 +\n    sum(countBlackWhiteRun(bottomLeft, topLeft, matrix, 5)) / 7 +\n    sum(countBlackWhiteRun(topRight, topLeft, matrix, 5)) / 7\n  ) / 4;\n\n  if (moduleSize < 1) {\n    throw new Error(\"Invalid module size\");\n  }\n\n  const topDimension = Math.round(distance(topLeft, topRight) / moduleSize);\n  const sideDimension = Math.round(distance(topLeft, bottomLeft) / moduleSize);\n  let dimension = Math.floor((topDimension + sideDimension) / 2) + 7;\n  switch (dimension % 4) {\n    case 0:\n      dimension++;\n      break;\n    case 2:\n      dimension--;\n      break;\n  }\n  return { dimension, moduleSize };\n}\n\n// Takes an origin point and an end point and counts the sizes of the black white run from the origin towards the end point.\n// Returns an array of elements, representing the pixel size of the black white run.\n// Uses a variant of http://en.wikipedia.org/wiki/Bresenham's_line_algorithm\nfunction countBlackWhiteRunTowardsPoint(origin: Point, end: Point, matrix: BitMatrix, length: number) {\n  const switchPoints: Point[] = [{x: Math.floor(origin.x), y: Math.floor(origin.y)}];\n  const steep = Math.abs(end.y - origin.y) > Math.abs(end.x - origin.x);\n\n  let fromX: number;\n  let fromY: number;\n  let toX: number;\n  let toY: number;\n  if (steep) {\n    fromX = Math.floor(origin.y);\n    fromY = Math.floor(origin.x);\n    toX = Math.floor(end.y);\n    toY = Math.floor(end.x);\n  } else {\n    fromX = Math.floor(origin.x);\n    fromY = Math.floor(origin.y);\n    toX = Math.floor(end.x);\n    toY = Math.floor(end.y);\n  }\n\n  const dx = Math.abs(toX - fromX);\n  const dy = Math.abs(toY - fromY);\n  let error = Math.floor(-dx / 2);\n  const xStep = fromX < toX ? 1 : -1;\n  const yStep = fromY < toY ? 1 : -1;\n\n  let currentPixel = true;\n  // Loop up until x == toX, but not beyond\n  for (let x = fromX, y = fromY; x !== toX + xStep; x += xStep) {\n    // Does current pixel mean we have moved white to black or vice versa?\n    // Scanning black in state 0,2 and white in state 1, so if we find the wrong\n    // color, advance to next state or end if we are in state 2 already\n    const realX = steep ? y : x;\n    const realY = steep ? x : y;\n    if (matrix.get(realX, realY) !== currentPixel) {\n      currentPixel = !currentPixel;\n      switchPoints.push({x: realX, y: realY});\n      if (switchPoints.length === length + 1) {\n        break;\n      }\n    }\n    error += dy;\n    if (error > 0) {\n      if (y === toY) {\n        break;\n      }\n      y += yStep;\n      error -= dx;\n    }\n  }\n  const distances: number[] = [];\n  for (let i = 0; i < length; i++) {\n    if (switchPoints[i] && switchPoints[i + 1]) {\n      distances.push(distance(switchPoints[i], switchPoints[i + 1]));\n    } else {\n      distances.push(0);\n    }\n  }\n  return distances;\n}\n\n// Takes an origin point and an end point and counts the sizes of the black white run in the origin point\n// along the line that intersects with the end point. Returns an array of elements, representing the pixel sizes\n// of the black white run. Takes a length which represents the number of switches from black to white to look for.\nfunction countBlackWhiteRun(origin: Point, end: Point, matrix: BitMatrix, length: number) {\n  const rise = end.y - origin.y;\n  const run = end.x - origin.x;\n\n  const towardsEnd = countBlackWhiteRunTowardsPoint(origin, end, matrix, Math.ceil(length / 2));\n  const awayFromEnd = countBlackWhiteRunTowardsPoint(origin, {x: origin.x - run, y: origin.y - rise}, matrix, Math.ceil(length / 2));\n\n  const middleValue = towardsEnd.shift() + awayFromEnd.shift() - 1; // Substract one so we don't double count a pixel\n  return awayFromEnd.concat(middleValue).concat(...towardsEnd);\n}\n\n// Takes in a black white run and an array of expected ratios. Returns the average size of the run as well as the \"error\" -\n// that is the amount the run diverges from the expected ratio\nfunction scoreBlackWhiteRun(sequence: number[], ratios: number[]) {\n  const averageSize = sum(sequence) / sum(ratios);\n  let error = 0;\n  ratios.forEach((ratio, i) => {\n    error += (sequence[i] - ratio * averageSize) ** 2;\n  });\n\n  return { averageSize, error };\n}\n\n// Takes an X,Y point and an array of sizes and scores the point against those ratios.\n// For example for a finder pattern takes the ratio list of 1:1:3:1:1 and checks horizontal, vertical and diagonal ratios\n// against that.\nfunction scorePattern(point: Point, ratios: number[], matrix: BitMatrix) {\n  try {\n    const horizontalRun = countBlackWhiteRun(point, {x: -1, y: point.y}, matrix, ratios.length);\n    const verticalRun = countBlackWhiteRun(point, {x: point.x, y: -1}, matrix, ratios.length);\n\n    const topLeftPoint = {\n      x: Math.max(0, point.x - point.y) - 1,\n      y: Math.max(0, point.y - point.x) - 1,\n    };\n    const topLeftBottomRightRun = countBlackWhiteRun(point, topLeftPoint, matrix, ratios.length);\n\n    const bottomLeftPoint = {\n      x: Math.min(matrix.width, point.x + point.y) + 1,\n      y: Math.min(matrix.height, point.y + point.x) + 1,\n    };\n    const bottomLeftTopRightRun = countBlackWhiteRun(point, bottomLeftPoint, matrix, ratios.length);\n\n    const horzError = scoreBlackWhiteRun(horizontalRun, ratios);\n    const vertError = scoreBlackWhiteRun(verticalRun, ratios);\n    const diagDownError = scoreBlackWhiteRun(topLeftBottomRightRun, ratios);\n    const diagUpError = scoreBlackWhiteRun(bottomLeftTopRightRun, ratios);\n\n    const ratioError = Math.sqrt(horzError.error * horzError.error +\n      vertError.error * vertError.error +\n      diagDownError.error * diagDownError.error +\n      diagUpError.error * diagUpError.error);\n\n    const avgSize = (horzError.averageSize + vertError.averageSize + diagDownError.averageSize + diagUpError.averageSize) / 4;\n\n    const sizeError = ((horzError.averageSize - avgSize) ** 2 +\n      (vertError.averageSize - avgSize) ** 2 +\n      (diagDownError.averageSize - avgSize) ** 2 +\n      (diagUpError.averageSize - avgSize) ** 2) / avgSize;\n    return ratioError + sizeError;\n  } catch {\n    return Infinity;\n  }\n}\n\nfunction recenterLocation(matrix: BitMatrix, p: Point): Point {\n  let leftX = Math.round(p.x);\n  while (matrix.get(leftX, Math.round(p.y))) {\n    leftX--;\n  }\n  let rightX = Math.round(p.x);\n  while (matrix.get(rightX, Math.round(p.y))) {\n    rightX++;\n  }\n  const x = (leftX + rightX) / 2;\n\n  let topY = Math.round(p.y);\n  while (matrix.get(Math.round(x), topY)) {\n    topY--;\n  }\n  let bottomY = Math.round(p.y);\n  while (matrix.get(Math.round(x), bottomY)) {\n    bottomY++;\n  }\n  const y = (topY + bottomY) / 2;\n\n  return { x, y };\n}\n\ninterface Quad {\n  top: {\n    startX: number;\n    endX: number;\n    y: number;\n  };\n  bottom: {\n    startX: number;\n    endX: number;\n    y: number;\n  };\n}\n\nexport function locate(matrix: BitMatrix): QRLocation[] {\n  const finderPatternQuads: Quad[] = [];\n  let activeFinderPatternQuads: Quad[] = [];\n  const alignmentPatternQuads: Quad[] = [];\n  let activeAlignmentPatternQuads: Quad[] = [];\n\n  for (let y = 0; y <= matrix.height; y++) {\n    let length = 0;\n    let lastBit = false;\n    let scans = [0, 0, 0, 0, 0];\n\n    for (let x = -1; x <= matrix.width; x++) {\n      const v = matrix.get(x, y);\n      if (v === lastBit) {\n        length++;\n      } else {\n        scans = [scans[1], scans[2], scans[3], scans[4], length];\n        length = 1;\n        lastBit = v;\n\n        // Do the last 5 color changes ~ match the expected ratio for a finder pattern? 1:1:3:1:1 of b:w:b:w:b\n        const averageFinderPatternBlocksize = sum(scans) / 7;\n        const validFinderPattern =\n          Math.abs(scans[0] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n          Math.abs(scans[1] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n          Math.abs(scans[2] - 3 * averageFinderPatternBlocksize) < 3 * averageFinderPatternBlocksize &&\n          Math.abs(scans[3] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n          Math.abs(scans[4] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n          !v; // And make sure the current pixel is white since finder patterns are bordered in white\n\n        // Do the last 3 color changes ~ match the expected ratio for an alignment pattern? 1:1:1 of w:b:w\n        const averageAlignmentPatternBlocksize = sum(scans.slice(-3)) / 3;\n        const validAlignmentPattern =\n          Math.abs(scans[2] - averageAlignmentPatternBlocksize) < averageAlignmentPatternBlocksize &&\n          Math.abs(scans[3] - averageAlignmentPatternBlocksize) < averageAlignmentPatternBlocksize &&\n          Math.abs(scans[4] - averageAlignmentPatternBlocksize) < averageAlignmentPatternBlocksize &&\n          v; // Is the current pixel black since alignment patterns are bordered in black\n\n        if (validFinderPattern) {\n          // Compute the start and end x values of the large center black square\n          const endX = x - scans[3] - scans[4];\n          const startX = endX - scans[2];\n\n          const line = { startX, endX, y };\n          // Is there a quad directly above the current spot? If so, extend it with the new line. Otherwise, create a new quad with\n          // that line as the starting point.\n          const matchingQuads = activeFinderPatternQuads.filter(q =>\n            (startX >= q.bottom.startX && startX <= q.bottom.endX) ||\n            (endX >= q.bottom.startX && startX <= q.bottom.endX) ||\n            (startX <= q.bottom.startX && endX >= q.bottom.endX && (\n              (scans[2] / (q.bottom.endX - q.bottom.startX)) < MAX_QUAD_RATIO &&\n              (scans[2] / (q.bottom.endX - q.bottom.startX)) > MIN_QUAD_RATIO\n            )),\n          );\n          if (matchingQuads.length > 0) {\n            matchingQuads[0].bottom = line;\n          } else {\n            activeFinderPatternQuads.push({ top: line, bottom: line });\n          }\n        }\n        if (validAlignmentPattern) {\n          // Compute the start and end x values of the center black square\n          const endX = x - scans[4];\n          const startX = endX - scans[3];\n\n          const line = { startX, y, endX };\n          // Is there a quad directly above the current spot? If so, extend it with the new line. Otherwise, create a new quad with\n          // that line as the starting point.\n          const matchingQuads = activeAlignmentPatternQuads.filter(q =>\n            (startX >= q.bottom.startX && startX <= q.bottom.endX) ||\n            (endX >= q.bottom.startX && startX <= q.bottom.endX) ||\n            (startX <= q.bottom.startX && endX >= q.bottom.endX && (\n              (scans[2] / (q.bottom.endX - q.bottom.startX)) < MAX_QUAD_RATIO &&\n              (scans[2] / (q.bottom.endX - q.bottom.startX)) > MIN_QUAD_RATIO\n            )),\n          );\n          if (matchingQuads.length > 0) {\n            matchingQuads[0].bottom = line;\n          } else {\n            activeAlignmentPatternQuads.push({ top: line, bottom: line });\n          }\n        }\n      }\n    }\n    finderPatternQuads.push(...activeFinderPatternQuads.filter(q => q.bottom.y !== y && q.bottom.y - q.top.y >= 2));\n    activeFinderPatternQuads = activeFinderPatternQuads.filter(q => q.bottom.y === y);\n\n    alignmentPatternQuads.push(...activeAlignmentPatternQuads.filter(q => q.bottom.y !== y));\n    activeAlignmentPatternQuads = activeAlignmentPatternQuads.filter(q => q.bottom.y === y);\n\n  }\n\n  finderPatternQuads.push(...activeFinderPatternQuads.filter(q => q.bottom.y - q.top.y >= 2));\n  alignmentPatternQuads.push(...activeAlignmentPatternQuads);\n\n  // Refactored from cozmo/jsQR to (hopefully) circumvent an issue in Safari 13+ on both Mac and iOS (also including\n  // iOS Chrome and other Safari iOS derivatives). Safari was very occasionally and apparently not deterministically\n  // throwing a \"RangeError: Array size is not a small enough positive integer.\" exception seemingly within the second\n  // .map of the original code (here the second for-loop). This second .map contained a nested .map call over the same\n  // array instance which was the chained result from previous calls to .map, .filter and .sort which potentially caused\n  // this bug in Safari?\n  // Also see https://github.com/cozmo/jsQR/issues/157 and https://bugs.webkit.org/show_bug.cgi?id=211619#c3\n  const scoredFinderPatternPositions: Array<Point & { size: number, score: number }> = [];\n  for (const quad of finderPatternQuads) {\n    if (quad.bottom.y - quad.top.y < 2) {\n      // All quads must be at least 2px tall since the center square is larger than a block\n      continue;\n    }\n\n    // calculate quad center\n    const x = (quad.top.startX + quad.top.endX + quad.bottom.startX + quad.bottom.endX) / 4;\n    const y = (quad.top.y + quad.bottom.y + 1) / 2;\n    if (!matrix.get(Math.round(x), Math.round(y))) {\n      continue;\n    }\n\n    const lengths = [quad.top.endX - quad.top.startX, quad.bottom.endX - quad.bottom.startX, quad.bottom.y - quad.top.y + 1];\n    const size = sum(lengths) / lengths.length;\n    // Initial scoring of finder pattern quads by looking at their ratios, not taking into account position\n    const score = scorePattern({x: Math.round(x), y: Math.round(y)}, [1, 1, 3, 1, 1], matrix);\n    scoredFinderPatternPositions.push({ score, x, y, size });\n  }\n  if (scoredFinderPatternPositions.length < 3) {\n    // A QR code has 3 finder patterns, therefore we need at least 3 candidates.\n    return null;\n  }\n  scoredFinderPatternPositions.sort((a, b) => a.score - b.score);\n\n  // Now take the top finder pattern options and try to find 2 other options with a similar size.\n  const finderPatternGroups: Array<{ points: [Point, Point, Point], score: number }> = [];\n  for (let i = 0; i < Math.min(scoredFinderPatternPositions.length, MAX_FINDERPATTERNS_TO_SEARCH); ++i) {\n    const point = scoredFinderPatternPositions[i];\n    const otherPoints: typeof scoredFinderPatternPositions = [];\n\n    for (const otherPoint of scoredFinderPatternPositions) {\n      if (otherPoint === point) {\n        continue;\n      }\n      otherPoints.push({\n        ...otherPoint,\n        score: otherPoint.score + ((otherPoint.size - point.size) ** 2) / point.size, // score similarity of sizes\n      });\n    }\n    otherPoints.sort((a, b) => a.score - b.score);\n\n    finderPatternGroups.push({\n      points: [point, otherPoints[0], otherPoints[1]], // note that otherPoints.length >= 2 as scoredFinderPatternPositions.length >= 3\n      score: point.score + otherPoints[0].score + otherPoints[1].score, // total combined score of the three points in the group\n    });\n  }\n  finderPatternGroups.sort((a, b) => a.score - b.score);\n  const bestFinderPatternGroup = finderPatternGroups[0];\n\n  const { topRight, topLeft, bottomLeft } = reorderFinderPatterns(...bestFinderPatternGroup.points);\n  const alignment = findAlignmentPattern(matrix, alignmentPatternQuads, topRight, topLeft, bottomLeft);\n  const result: QRLocation[] = [];\n  if (alignment) {\n    result.push({\n      alignmentPattern: { x: alignment.alignmentPattern.x, y: alignment.alignmentPattern.y },\n      bottomLeft: {x: bottomLeft.x, y: bottomLeft.y },\n      dimension: alignment.dimension,\n      topLeft: {x: topLeft.x, y: topLeft.y },\n      topRight: {x: topRight.x, y: topRight.y },\n    });\n  }\n\n  // We normally use the center of the quads as the location of the tracking points, which is optimal for most cases and will account\n  // for a skew in the image. However, In some cases, a slight skew might not be real and instead be caused by image compression\n  // errors and/or low resolution. For those cases, we'd be better off centering the point exactly in the middle of the black area. We\n  // compute and return the location data for the naively centered points as it is little additional work and allows for multiple\n  // attempts at decoding harder images.\n  const midTopRight = recenterLocation(matrix, topRight);\n  const midTopLeft = recenterLocation(matrix, topLeft);\n  const midBottomLeft = recenterLocation(matrix, bottomLeft);\n  const centeredAlignment = findAlignmentPattern(matrix, alignmentPatternQuads, midTopRight, midTopLeft, midBottomLeft);\n  if (centeredAlignment) {\n    result.push({\n      alignmentPattern: { x: centeredAlignment.alignmentPattern.x, y: centeredAlignment.alignmentPattern.y },\n      bottomLeft: { x: midBottomLeft.x, y: midBottomLeft. y },\n      topLeft: { x: midTopLeft.x, y: midTopLeft. y },\n      topRight: { x: midTopRight.x, y: midTopRight. y },\n      dimension: centeredAlignment.dimension,\n    });\n  }\n\n  if (result.length === 0) {\n    return null;\n  }\n\n  return result;\n}\n\nfunction findAlignmentPattern(matrix: BitMatrix, alignmentPatternQuads: Quad[], topRight: Point, topLeft: Point, bottomLeft: Point) {\n  // Now that we've found the three finder patterns we can determine the blockSize and the size of the QR code.\n  // We'll use these to help find the alignment pattern but also later when we do the extraction.\n  let dimension: number;\n  let moduleSize: number;\n  try {\n    ({ dimension, moduleSize } = computeDimension(topLeft, topRight, bottomLeft, matrix));\n  } catch (e) {\n    return null;\n  }\n\n  // Now find the alignment pattern\n  const bottomRightFinderPattern = { // Best guess at where a bottomRight finder pattern would be\n    x: topRight.x - topLeft.x + bottomLeft.x,\n    y: topRight.y - topLeft.y + bottomLeft.y,\n  };\n  const modulesBetweenFinderPatterns = ((distance(topLeft, bottomLeft) + distance(topLeft, topRight)) / 2 / moduleSize);\n  const correctionToTopLeft = 1 - (3 / modulesBetweenFinderPatterns);\n  const expectedAlignmentPattern = {\n    x: topLeft.x + correctionToTopLeft * (bottomRightFinderPattern.x - topLeft.x),\n    y: topLeft.y + correctionToTopLeft * (bottomRightFinderPattern.y - topLeft.y),\n  };\n\n  const alignmentPatterns = alignmentPatternQuads\n    .map(q => {\n      const x = (q.top.startX + q.top.endX + q.bottom.startX + q.bottom.endX) / 4;\n      const y = (q.top.y + q.bottom.y + 1) / 2;\n      if (!matrix.get(Math.floor(x), Math.floor(y))) {\n        return;\n      }\n\n      const sizeScore = scorePattern({x: Math.floor(x), y: Math.floor(y)}, [1, 1, 1], matrix);\n      const score = sizeScore + distance({x, y}, expectedAlignmentPattern);\n      return { x, y, score };\n    })\n    .filter(v => !!v)\n    .sort((a, b) => a.score - b.score);\n\n  // If there are less than 15 modules between finder patterns it's a version 1 QR code and as such has no alignmemnt pattern\n  // so we can only use our best guess.\n  const alignmentPattern = modulesBetweenFinderPatterns >= 15 && alignmentPatterns.length ? alignmentPatterns[0] : expectedAlignmentPattern;\n\n  return { alignmentPattern, dimension };\n}\n", "import {binarize} from \"./binarizer\";\nimport {BitMatrix} from \"./BitMatrix\";\nimport {Chunks} from \"./decoder/decodeData\";\nimport {decode} from \"./decoder/decoder\";\nimport { Version } from \"./decoder/version\";\nimport {extract} from \"./extractor\";\nimport {locate, Point} from \"./locator\";\n\nexport interface QRCode {\n  binaryData: number[];\n  data: string;\n  chunks: Chunks;\n  version: number;\n  location: {\n    topRightCorner: Point;\n    topLeftCorner: Point;\n    bottomRightCorner: Point;\n    bottomLeftCorner: Point;\n\n    topRightFinderPattern: Point;\n    topLeftFinderPattern: Point;\n    bottomLeftFinderPattern: Point;\n\n    bottomRightAlignmentPattern?: Point;\n  };\n  matrix: BitMatrix;\n}\n\nfunction scan(matrix: BitMatrix): QRCode | null {\n  const locations = locate(matrix);\n  if (!locations) {\n    return null;\n  }\n\n  for (const location of locations) {\n    const extracted = extract(matrix, location);\n    const decoded = decode(extracted.matrix);\n    if (decoded) {\n      return {\n        binaryData: decoded.bytes,\n        data: decoded.text,\n        chunks: decoded.chunks,\n        version: decoded.version,\n        location: {\n          topRightCorner: extracted.mappingFunction(location.dimension, 0),\n          topLeftCorner: extracted.mappingFunction(0, 0),\n          bottomRightCorner: extracted.mappingFunction(location.dimension, location.dimension),\n          bottomLeftCorner: extracted.mappingFunction(0, location.dimension),\n\n          topRightFinderPattern: location.topRight,\n          topLeftFinderPattern: location.topLeft,\n          bottomLeftFinderPattern: location.bottomLeft,\n\n          bottomRightAlignmentPattern: location.alignmentPattern,\n        },\n        matrix: extracted.matrix,\n      };\n    }\n  }\n  return null;\n}\n\nexport interface Options {\n  inversionAttempts?: \"dontInvert\" | \"onlyInvert\" | \"attemptBoth\" | \"invertFirst\";\n  greyScaleWeights?: GreyscaleWeights;\n  canOverwriteImage?: boolean;\n}\n\nexport interface GreyscaleWeights {\n  red: number;\n  green: number;\n  blue: number;\n  useIntegerApproximation?: boolean;\n}\n\nconst defaultOptions: Options = {\n  inversionAttempts: \"attemptBoth\",\n  greyScaleWeights: {\n    red: 0.2126,\n    green: 0.7152,\n    blue: 0.0722,\n    useIntegerApproximation: false,\n  },\n  canOverwriteImage: true,\n};\n\nfunction mergeObject(target: any, src: any) {\n  Object.keys(src).forEach(opt => { // Sad implementation of Object.assign since we target es5 not es6\n    target[opt] = src[opt];\n  });\n}\n\nfunction jsQR(data: Uint8ClampedArray, width: number, height: number, providedOptions: Options = {}): QRCode | null {\n  const options = Object.create(null);\n  mergeObject(options, defaultOptions);\n  mergeObject(options, providedOptions);\n\n  const tryInvertedFirst = options.inversionAttempts === \"onlyInvert\" || options.inversionAttempts === \"invertFirst\";\n  const shouldInvert = options.inversionAttempts === \"attemptBoth\" || tryInvertedFirst;\n  const {binarized, inverted} = binarize(data, width, height, shouldInvert, options.greyScaleWeights,\n      options.canOverwriteImage);\n  let result = scan(tryInvertedFirst ? inverted : binarized);\n  if (!result && (options.inversionAttempts === \"attemptBoth\" || options.inversionAttempts === \"invertFirst\")) {\n    result = scan(tryInvertedFirst ? binarized : inverted);\n  }\n  return result;\n}\n\n(jsQR as any).default = jsQR;\nexport default jsQR;\n", "// @ts-ignore jsqr-es6 does not provide types currently\nimport jsQR from '../node_modules/jsqr-es6/dist/jsQR.js';\n\ntype GrayscaleWeights = {\n    red: number,\n    green: number,\n    blue: number,\n    useIntegerApproximation: boolean,\n};\n\nlet inversionAttempts: 'dontInvert' | 'onlyInvert' | 'attemptBoth' = 'dontInvert';\nlet grayscaleWeights: GrayscaleWeights = {\n    // weights for quick luma integer approximation (https://en.wikipedia.org/wiki/YUV#Full_swing_for_BT.601)\n    red: 77,\n    green: 150,\n    blue: 29,\n    useIntegerApproximation: true,\n};\n\nself.onmessage = event => {\n    const id = event['data']['id'];\n    const type = event['data']['type'];\n    const data = event['data']['data'];\n\n    switch (type) {\n        case 'decode':\n            decode(data, id);\n            break;\n        case 'grayscaleWeights':\n            setGrayscaleWeights(data);\n            break;\n        case 'inversionMode':\n            setInversionMode(data);\n            break;\n        case 'close':\n            // close after earlier messages in the event loop finished processing\n            self.close();\n            break;\n    }\n};\n\nfunction decode(data: { data: Uint8ClampedArray, width: number, height: number }, requestId: number): void {\n    const rgbaData = data['data'];\n    const width = data['width'];\n    const height = data['height'];\n    const result = jsQR(rgbaData, width, height, {\n        inversionAttempts: inversionAttempts,\n        greyScaleWeights: grayscaleWeights,\n    });\n    if (!result) {\n        (self as unknown as Worker).postMessage({\n            id: requestId,\n            type: 'qrResult',\n            data: null,\n        });\n        return;\n    }\n\n    (self as unknown as Worker).postMessage({\n        id: requestId,\n        type: 'qrResult',\n        data: result.data,\n        // equivalent to cornerPoints of native BarcodeDetector\n        cornerPoints: [\n            result.location.topLeftCorner,\n            result.location.topRightCorner,\n            result.location.bottomRightCorner,\n            result.location.bottomLeftCorner,\n        ],\n    });\n}\n\nfunction setGrayscaleWeights(data: GrayscaleWeights) {\n    // update grayscaleWeights in a closure compiler compatible fashion\n    grayscaleWeights.red = data['red'];\n    grayscaleWeights.green = data['green'];\n    grayscaleWeights.blue = data['blue'];\n    grayscaleWeights.useIntegerApproximation = data['useIntegerApproximation'];\n}\n\nfunction setInversionMode(inversionMode: 'original' | 'invert' | 'both') {\n    switch (inversionMode) {\n        case 'original':\n            inversionAttempts = 'dontInvert';\n            break;\n        case 'invert':\n            inversionAttempts = 'onlyInvert';\n            break;\n        case 'both':\n            inversionAttempts = 'attemptBoth';\n            break;\n        default:\n            throw new Error('Invalid inversion mode');\n    }\n}\n"], "names": ["BitMatrix", "constructor", "data", "width", "height", "length", "createEmpty", "Uint8ClampedArray", "get", "x", "y", "set", "v", "setRegion", "left", "top", "Matrix", "buffer", "bufferSize", "Error", "value", "BitStream", "bytes", "readBits", "numBits", "available", "toString", "result", "bitOffset", "byteOffset", "bitsToNotRead", "toRead", "Mode", "ModeByte", "decodeByte", "stream", "size", "text", "i", "push", "b", "decodeURIComponent", "map", "substr", "join", "decode", "version", "chunks", "mode", "Terminator", "ECI", "type", "assignmentNumber", "Numeric", "num", "a", "c", "Alphanumeric", "AlphanumericCharacterCodes", "charCodeAt", "Byte", "byteResult", "Kanji", "Math", "floor", "k", "StructuredAppend", "currentSequence", "totalSequence", "parity", "GenericGFPoly", "field", "coefficients", "<PERSON><PERSON><PERSON><PERSON>", "firstNonZero", "zero", "degree", "isZero", "getCoefficient", "addOrSubtract", "other", "smallerCoefficients", "largerCoefficients", "lengthDiff", "sumDiff", "multiply", "scalar", "product", "multiplyPoly", "a<PERSON><PERSON><PERSON>", "j", "b<PERSON><PERSON><PERSON>", "addOrSubtractGF", "<PERSON><PERSON><PERSON><PERSON>", "bCoefficients", "multiplyByMonomial", "coefficient", "evaluateAt", "for<PERSON>ach", "GenericGF", "primitive", "genBase", "generatorBase", "expTable", "Array", "logTable", "from", "one", "inverse", "buildMonomial", "log", "exp", "runEuclideanAlgorithm", "R", "tLast", "t", "r", "rLast", "rLastLast", "q", "dltInverse", "degreeDiff", "scale", "tLastLast", "sigmaTildeAtZero", "twoS", "outputBytes", "error", "s", "syndromeCoefficients", "evaluation", "syndrome", "sigmaOmega", "numErrors", "errorLocator", "errorCount", "errorLocations", "denominator", "xiInverse", "errorEvaluator", "position", "infoBits", "versionNumber", "alignmentPatternCenters", "errorCorrectionLevels", "ecCodewordsPerBlock", "ecBlocks", "numBlocks", "dataCodewordsPerBlock", "numBits<PERSON><PERSON>ering", "z", "bitCount", "pushBit", "bit", "byte", "bits", "formatInfo", "errorCorrectionLevel", "dataMask", "p", "readCodewords", "matrix", "dimension", "bitsRead", "currentByte", "readingUp", "columnIndex", "columnOffset", "codewords", "readVersion", "provisionalVersion", "VERSIONS", "topRightVersionBits", "bottomLeftVersionBits", "bestDifference", "Infinity", "bestVersion", "difference", "readFormatInformation", "topLeftFormatInfoBits", "topRightBottomRightFormatInfoBits", "bestFormatInfo", "getDataBlocks", "ecLevel", "totalCodewords", "ecInfo", "block", "dataBlocks", "numDataCodewords", "slice", "shortBlockSize", "dataBlock", "shift", "largeBlockCount", "smallBlockCount", "decodeMatrix", "resultIndex", "correctedBytes", "resultBytes", "decodeData", "squareToQuadrilateral", "p1", "p2", "p3", "p4", "dx3", "dy3", "a11", "a12", "a13", "a21", "a22", "a23", "a31", "a32", "a33", "quadrilateralToSquare", "sToQ", "extract", "image", "location", "qToS", "sourcePixel", "mappingFunction", "sum", "values", "reduce", "reorderFinderPatterns", "pattern1", "pattern2", "pattern3", "bottomLeft", "topLeft", "topRight", "twoThreeDistance", "oneTwoDistance", "oneThreeDistance", "computeDimension", "countBlackWhite<PERSON>un", "moduleSize", "topDimension", "sideDimension", "countBlackWhiteRunTowardsPoint", "origin", "end", "steep", "fromX", "fromY", "toX", "toY", "dx", "currentPixel", "xStep", "realX", "realY", "switchPoints", "dy", "yStep", "distances", "distance", "awayFromEnd", "concat", "middleValue", "towardsEnd", "scoreBlackWhiteRun", "sequence", "ratios", "ratio", "averageSize", "scorePattern", "point", "max", "min", "vertError", "diagDownError", "diagUpError", "avgSize", "recenterLocation", "leftX", "round", "rightX", "topY", "bottomY", "locate", "activeFinderPatternQuads", "activeAlignmentPatternQuads", "lastBit", "scans", "abs", "averageFinderPatternBlocksize", "averageAlignmentPatternBlocksize", "validFinderPattern", "startX", "endX", "bottom", "matchingQuads", "line", "validAlignmentPattern", "finderPatternQuads", "filter", "alignmentPatternQuads", "quad", "scoredFinderPatternPositions", "score", "sort", "otherPoint", "otherPoints", "finderPatternGroups", "points", "alignment", "alignmentPattern", "midTopRight", "midTopLeft", "midBottomLeft", "centeredAlignment", "findAlignmentPattern", "e", "correctionToTopLeft", "sizeScore", "expectedAlignmentPattern", "scan", "locations", "decoded", "binaryData", "topRightCorner", "extracted", "topLeftCorner", "bottomRightCorner", "bottomLeftCorner", "topRightFinderPattern", "topLeftFinderPattern", "bottomLeftFinderPattern", "bottomRightAlignmentPattern", "inversionAttempts", "greyScaleWeights", "red", "green", "blue", "useIntegerApproximation", "canOverwriteImage", "mergeObject", "target", "src", "Object", "keys", "opt", "jsQR", "providedOptions", "options", "defaultOptions", "shouldInvert", "pixelCount", "bufferOffset", "greyscale<PERSON><PERSON>er", "greyscaleWeights", "greyscalePixels", "blackPointsBuffer", "blackPointsCount", "verticalRegionCount", "verticalRegion", "hortizontalRegion", "horizontalRegionCount", "pixelLumosity", "average", "blackPoints", "averageNeighborBlackPoint", "binarized", "binarizedBuffer", "inverted", "returnInverted", "invertedBuffer", "xRegion", "yRegion", "lum", "threshold", "tryInvertedFirst", "default", "grayscaleWeights", "self", "onmessage", "event", "self.onmessage", "postMessage", "id", "cornerPoints", "close"], "mappings": "kBAAaA,GASXC,YAAYC,EAAyBC,GACnC,IAAKA,CAAAA,KAAL,CAAaA,CACb,KAAKC,CAAAA,MAAL,CAAcF,CAAKG,CAAAA,MAAnB,CAA4BF,CAC5B,KAAKD,CAAAA,IAAL,CAAYA,EAXAI,kBAAW,CAACH,CAAD,CAAgBC,CAAhB,EACvB,MAAO,KAAIJ,CAAJ,CAAc,IAAIO,iBAAJ,CAAsBJ,CAAtB,CAA8BC,CAA9B,CAAd,CAAqDD,CAArD,EAaFK,GAAG,CAACC,CAAD,CAAYC,CAAZ,EACR,MAAQ,EAAR,CAAID,CAAJ,EAAaA,CAAb,EAAkB,IAAKN,CAAAA,KAAvB,EAAoC,CAApC,CAAgCO,CAAhC,EAAyCA,CAAzC,EAA8C,IAAKN,CAAAA,MAAnD,CACS,CAAA,CADT,CAGO,CAAC,CAAC,IAAKF,CAAAA,IAAL,CAAUQ,CAAV,CAAc,IAAKP,CAAAA,KAAnB,CAA2BM,CAA3B,EAGJE,GAAG,CAACF,CAAD,CAAYC,CAAZ,CAAuBE,CAAvB,EACR,IAAKV,CAAAA,IAAL,CAAUQ,CAAV,CAAc,IAAKP,CAAAA,KAAnB,CAA2BM,CAA3B,CAAA,CAAgCG,CAAA,CAAI,CAAJ,CAAQ,EAGnCC,SAAS,CAACC,CAAD,CAAeC,CAAf,CAA4BZ,CAA5B,CAA2CC,CAA3C,CAA2DQ,CAA3D,EACd,IAAK,IAAIF,EAAIK,CAAb,CAAkBL,CAAlB,CAAsBK,CAAtB,CAA4BX,CAA5B,CAAoCM,CAAA,EAApC,CACE,IAAK,IAAID,EAAIK,CAAb,CAAmBL,CAAnB,CAAuBK,CAAvB,CAA8BX,CAA9B,CAAqCM,CAAA,EAArC,CACE,IAAKE,CAAAA,GAAL,CAASF,CAAT,CAAYC,CAAZ,CAAe,CAAC,CAACE,CAAjB;AClBR,KAAMI,EAAN,CAGEf,YAAYE,EAAeC,EAAgBa,GACzC,IAAKd,CAAAA,KAAL,CAAaA,MAEb,IAAIc,CAAJ,EAAcA,CAAOZ,CAAAA,MAArB,GAAgCa,CAAhC,CACE,KAAUC,MAAJ,CAAU,mBAAV,CAAN,CAEF,IAAKjB,CAAAA,IAAL,CAAYe,CAAZ,EAAsB,IAAIV,iBAAJ,CAAsBW,CAAtB,EAEjBV,GAAG,CAACC,CAAD,CAAYC,CAAZ,EACR,MAAO,KAAKR,CAAAA,IAAL,CAAUQ,CAAV,CAAc,IAAKP,CAAAA,KAAnB,CAA2BM,CAA3B,EAEFE,GAAG,CAACF,CAAD,CAAYC,CAAZ,CAAuBU,CAAvB,EACR,IAAKlB,CAAAA,IAAL,CAAUQ,CAAV,CAAc,IAAKP,CAAAA,KAAnB,CAA2BM,CAA3B,CAAA,CAAgCW,EAfpC;KCTaC,IAKXpB,YAAYqB,GAFJ,cAAA,CADA,eACA,CADqB,CAI3B,KAAKA,CAAAA,KAAL,CAAaA,EAGRC,QAAQ,CAACC,CAAD,EACb,GAAc,CAAd,CAAIA,CAAJ,EAA6B,EAA7B,CAAmBA,CAAnB,EAAmCA,CAAnC,CAA6C,IAAKC,CAAAA,SAAL,EAA7C,CACE,KAAUN,MAAJ,CAAU,cAAV,CAA2BK,CAAQE,CAAAA,QAAR,EAA3B,CAAgD,OAAhD,CAAN,CAGF,IAAIC,EAAS,CAEb,IAAqB,CAArB,CAAI,IAAKC,CAAAA,SAAT,CAAwB,mBAEtB,mBAGAD,EAAA,EAAU,IAAKL,CAAAA,KAAL,CAAW,IAAKO,CAAAA,UAAhB,CAAV,IAAA,GAAA,EAAA,GAAA,GAAiDC,CACjDN,EAAA,EAAWO,CACX,KAAKH,CAAAA,SAAL,EAAkBG,CACK,EAAvB,GAAI,IAAKH,CAAAA,SAAT,GACE,IAAKA,CAAAA,SACL,CADiB,CACjB,CAAA,IAAKC,CAAAA,UAAL,EAFF,CARsB,CAexB,GAAc,CAAd,CAAIL,CAAJ,CAAiB,CACf,IAAA,CAAkB,CAAlB,EAAOA,CAAP,CAAA,CACEG,CAEA,CAFUA,CAEV,EAFoB,CAEpB,CAF0B,IAAKL,CAAAA,KAAL,CAAW,IAAKO,CAAAA,UAAhB,CAE1B,CAFwD,GAExD,CADA,IAAKA,CAAAA,UAAL,EACA,CAAAL,CAAA,EAAW,CAIC,EAAd,CAAIA,CAAJ,IAIE,EAAA,EAAA,CADAG,CACA,CADUA,CACV,EADoBH,CACpB,EADiC,IAAKF,CAAAA,KAAL,CAAW,IAAKO,CAAAA,UAAhB,CACjC,IAAA,GAAA,GAAA,GADwEC,CACxE;AAAA,IAAKF,CAAAA,SAAL,EAAkBJ,CAJpB,CARe,CAejB,MAAOG,GAGFF,SAAS,GACd,MAAO,EAAP,EAAY,IAAKH,CAAAA,KAAMjB,CAAAA,MAAvB,CAAgC,IAAKwB,CAAAA,UAArC,EAAmD,IAAKD,CAAAA,WClB5D,IAAYI,CAAZ,CAAY,EAAAA,CAAA,GAAAA,CAAA,GAAA,CACVA,EAAA,CAAA,OAAA,UACAA,EAAA,CAAA,YAAA,eACAA,EAAA,CAAA,IAAA,OACAA,EAAA,CAAA,KAAA,QACAA,EAAA,CAAA,GAAA,MACAA,EAAA,CAAA,gBAAA,mBAGF,KAAKC,CAAL,CAAK,EAAAA,CAAA,GAAAA,CAAA,GAAA,CACHA,EAAA,aAAA,EAAA,CAAA,aACAA,EAAA,UAAA,EAAA,CAAA,UACAA,EAAA,eAAA,EAAA,CAAA,eACAA,EAAA,OAAA,EAAA,CAAA,OACAA,EAAA,QAAA,EAAA,CAAA,QACAA,EAAA,MAAA,EAAA,CAAA,MACAA,EAAA,mBAAA,EAAA,CAAA,mBAoDF,gEAkCAC;QAASA,GAAU,CAACC,CAAD,CAAoBC,CAApB,EACjB,QAAA,CACIC,EAAO,mBAEkB,GAAI,IAAID,GAErC,KAAK,IAAIE,EAAI,CAAb,CAAgBA,CAAhB,CAAoBjC,CAApB,CAA4BiC,CAAA,EAA5B,CAAiC,CAC/B,mBACAhB,EAAMiB,CAAAA,IAAN,CAAWC,CAAX,CAF+B,CAIjC,GAAI,CACFH,CAAA,EAAQI,kBAAA,CAAmBnB,CAAMoB,CAAAA,GAAN,CAAUF,CAAA,EAAK,IAA2BG,CAAtB,GAAsBA,CAAhBH,CAAEd,CAAAA,QAAF,CAAW,EAAX,CAAgBiB,EAAAA,MAAvB,CAA8B,CAAC,CAA/B,CAAJ,EAAf,CAAwDC,CAAAA,IAAxD,CAA6D,EAA7D,CAAnB,CADN,CAEF,OAAA,CAAM,EAIR,MAAO,CAAEtB,MAAAA,CAAF,CAASe,KAAAA,CAAT;QAyBOQ,GAAM,CAAC3C,CAAD,CAA0B4C,CAA1B,aAIpB,uBASA,MAAA,EANET,KAAM,GACNf,MAAO,GACPyB,OAAQ,GACRD,QAAAA,EAGF,CAA6B,CAA7B,EAAOX,CAAOV,CAAAA,SAAP,EAAP,CAAA,CAAgC,CAC9B,mBACA,IAAIuB,CAAJ,GAAaf,CAASgB,CAAAA,UAAtB,CACE,MAAOtB,EACF,IAAIqB,CAAJ,GAAaf,CAASiB,CAAAA,GAAtB,CACsB,CAA3B,GAAIf,CAAOZ,CAAAA,QAAP,CAAgB,CAAhB,CAAJ,CACEI,CAAOoB,CAAAA,MAAOR,CAAAA,IAAd,CAAmB,CACjBY,KAAMnB,CAAKkB,CAAAA,GADM,CAEjBE,iBAAkBjB,CAAOZ,CAAAA,QAAP,CAAgB,CAAhB,CAFD,CAAnB,CADF,CAKkC,CAA3B,GAAIY,CAAOZ,CAAAA,QAAP,CAAgB,CAAhB,CAAJ,CACLI,CAAOoB,CAAAA,MAAOR,CAAAA,IAAd,CAAmB,CACjBY,KAAMnB,CAAKkB,CAAAA,GADM,CAEjBE,iBAAkBjB,CAAOZ,CAAAA,QAAP,CAAgB,EAAhB,CAFD,CAAnB,CADK,CAK2B,CAA3B,GAAIY,CAAOZ,CAAAA,QAAP,CAAgB,CAAhB,CAAJ,CACLI,CAAOoB,CAAAA,MAAOR,CAAAA,IAAd,CAAmB,CACjBY,KAAMnB,CAAKkB,CAAAA,GADM,CAEjBE,iBAAkBjB,CAAOZ,CAAAA,QAAP,CAAgB,EAAhB,CAFD,CAAnB,CADK,CAOLI,CAAOoB,CAAAA,MAAOR,CAAAA,IAAd,CAAmB,CACjBY,KAAMnB,CAAKkB,CAAAA,GADM,CAEjBE,iBAAkB,CAAC,CAFF,CAAnB,CAlBG,KAuBA,IAAIJ,CAAJ,GAAaf,CAASoB,CAAAA,OAAtB,CAA+B,aAhKpChB,EAAAA,CAAO,EAKX,KAFA,IAAIhC;AAAS8B,CAAOZ,CAAAA,QAAP,KADiB,GAAI,IA+JYa,EA9JjC,CAEb,CAAiB,CAAjB,EAAO/B,CAAP,CAAA,CAAoB,CAClB,oBACA,IAAW,GAAX,EAAIiD,CAAJ,CACE,KAAUnC,MAAJ,CAAU,iCAAV,CAAN,CAGF,uBAAA,4BAIAG,EAAMiB,CAAAA,IAAN,CAAW,EAAX,CAAgBgB,CAAhB,CAAmB,EAAnB,CAAwBf,CAAxB,CAA2B,EAA3B,CAAgCgB,CAAhC,CACAnB,EAAA,EAAQkB,CAAE7B,CAAAA,QAAF,EAAR,CAAuBc,CAAEd,CAAAA,QAAF,EAAvB,CAAsC8B,CAAE9B,CAAAA,QAAF,EACtCrB,EAAA,EAAU,CAZQ,CAgBpB,GAAe,CAAf,GAAIA,CAAJ,CAAkB,gBAEhB,IAAW,GAAX,EAAIiD,CAAJ,CACE,KAAUnC,MAAJ,CAAU,gCAAV,CAAN,yBAMFG,EAAMiB,CAAAA,IAAN,CAAW,EAAX,CAAgBgB,CAAhB,CAAmB,EAAnB,CAAwBf,CAAxB,CACAH,EAAA,EAAQkB,CAAE7B,CAAAA,QAAF,EAAR,CAAuBc,CAAEd,CAAAA,QAAF,EAVP,CAAlB,IAWO,IAAe,CAAf,GAAIrB,CAAJ,CAAkB,gBAEvB,IAAW,EAAX,EAAIiD,CAAJ,CACE,KAAUnC,MAAJ,CAAU,+BAAV,CAAN,CAGFG,CAAMiB,CAAAA,IAAN,CAAW,EAAX,CAAgBe,CAAhB,CACAjB,EAAA,EAAQiB,CAAI5B,CAAAA,QAAJ,EAPe,CAkIrBC,CAAOU,CAAAA,IAAP;AAA6BA,CAC7BV,EAAOL,CAAAA,KAAMiB,CAAAA,IAAb,CAAkB,GAAiBjB,CAAnC,CACAK,EAAOoB,CAAAA,MAAOR,CAAAA,IAAd,CAAmB,CACjBY,KAAMnB,CAAKqB,CAAAA,OADM,CAEjBhB,KAAoBA,CAFH,CAAnB,CAJoC,CAA/B,IAQA,IAAIW,CAAJ,GAAaf,CAASwB,CAAAA,YAAtB,CAAoC,SAjHzCpB,EAAAA,CAAO,EAIX,KADIhC,CACJ,CADa8B,CAAOZ,CAAAA,QAAP,IADgB,GAAI,IAgHuBa,EA/G3C,CACb,CAAiB,CAAjB,EAAO/B,CAAP,CAAA,EAQE,eAAA,EAAA,iBAAA,EAAA,IAAA,CAFAiB,CAAMiB,CAAAA,IAAN,CAAWmB,CAAA,CAA2BH,CAA3B,CAA8BI,CAAAA,UAA9B,CAAyC,CAAzC,CAAX,CAAwDD,CAAA,CAA2BlB,CAA3B,CAA8BmB,CAAAA,UAA9B,CAAyC,CAAzC,CAAxD,CAEA,CADAtB,CACA,EADQqB,CAAA,CAA2BH,CAA3B,CACR,CADwCG,CAAA,CAA2BlB,CAA3B,CACxC,CAAAnC,CAAA,EAAU,CAGG,EAAf,GAAIA,CAAJ,IAGE,cAAA,CADAiB,CAAMiB,CAAAA,IAAN,CAAWmB,CAAA,CAA2BH,CAA3B,CAA8BI,CAAAA,UAA9B,CAAyC,CAAzC,CAAX,CACA,CAAAtB,CAAA,EAAQqB,CAAA,CAA2BH,CAA3B,CAHV,CAoGI5B,EAAOU,CAAAA,IAAP,EAAkCA,CAClCV,EAAOL,CAAAA,KAAMiB,CAAAA,IAAb,CAAkB,GAAsBjB,CAAxC,CACAK,EAAOoB,CAAAA,MAAOR,CAAAA,IAAd,CAAmB,CACjBY,KAAMnB,CAAKyB,CAAAA,YADM,CAEjBpB,KAAyBA,CAFR,CAAnB,CAJyC,CAApC,IAQA,IAAIW,CAAJ,GAAaf,CAAS2B,CAAAA,IAAtB,EAIL,MAHoCxB,EAGpC,CAFAT,CAAOU,CAAAA,IAEP,EAFewB,CAAWxB,CAAAA,IAE1B,CADAV,CAAOL,CAAAA,KAAMiB,CAAAA,IAAb,CAAkB,GAAGsB,CAAWvC,CAAAA,KAAhC,CACA,CAAAK,CAAOoB,CAAAA,MAAOR,CAAAA,IAAd,CAAmB,CACjBY,KAAMnB,CAAK4B,CAAAA,IADM,CAEjBtC,MAAOuC,CAAWvC,CAAAA,KAFD,CAGjBe,KAAMwB,CAAWxB,CAAAA,IAHA,CAAnB,CAJK;IASA,IAAIW,CAAJ,GAAaf,CAAS6B,CAAAA,KAAtB,CAA6B,0BApFT,GAAI,IAqFS1B,GAnF1C,KAASE,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CAAoBjC,CAApB,CAA4BiC,CAAA,EAA5B,EAUE,eAAA,CAPIkB,CAOJ,CAPSO,IAAKC,CAAAA,KAAL,CAAWC,CAAX,CAAe,GAAf,CAOT,EAPiC,CAOjC,CAPuCA,CAOvC,CAP2C,GAO3C,CALET,CAKF,CANQ,IAAR,CAAIA,CAAJ,CACEA,CADF,CACO,KADP,CAGEA,CAHF,CAGO,KAGP,CAAAlC,CAAMiB,CAAAA,IAAN,CAAWiB,CAAX,EAAgB,CAAhB,CAAmBA,CAAnB,CAAuB,GAAvB,IAGsCX,6BAAAA,EAAAA,0BAuEpClB,EAAOU,CAAAA,IAAP,EAA2BA,CAC3BV,EAAOL,CAAAA,KAAMiB,CAAAA,IAAb,CAAkB,GAAejB,CAAjC,CACAK,EAAOoB,CAAAA,MAAOR,CAAAA,IAAd,CAAmB,CACjBY,KAAMnB,CAAK8B,CAAAA,KADM,CAEjBxC,MAAmBA,CAFF,CAGjBe,KAAkBA,CAHD,CAAnB,CAJkC,CAA7B,IASIW,EAAJ,GAAaf,CAASiC,CAAAA,gBAAtB,EACLvC,CAAOoB,CAAAA,MAAOR,CAAAA,IAAd,CAAmB,CACjBY,KAAMnB,CAAKkC,CAAAA,gBADM,CAEjBC,gBAAiBhC,CAAOZ,CAAAA,QAAP,CAAgB,CAAhB,CAFA,CAGjB6C,cAAejC,CAAOZ,CAAAA,QAAP,CAAgB,CAAhB,CAHE,CAIjB8C,OAAQlC,CAAOZ,CAAAA,QAAP,CAAgB,CAAhB,CAJS,CAAnB,CA9D4B,CAwEhC,GAA2B,CAA3B,GAAIY,CAAOV,CAAAA,SAAP,EAAJ,EAAwE,CAAxE,GAAgCU,CAAOZ,CAAAA,QAAP,CAAgBY,CAAOV,CAAAA,SAAP,EAAhB,CAAhC,CACE,MAAOE;KCrQU2C,GAInBrE,YAAYsE,EAAkBC,GAC5B,GAA4B,CAA5B,GAAIA,CAAanE,CAAAA,MAAjB,CACE,KAAUc,MAAJ,CAAU,kBAAV,CAAN,CAEF,IAAKoD,CAAAA,KAAL,CAAaA,CACb,eACA,IAAyB,CAAzB,CAAIE,CAAJ,EAAkD,CAAlD,GAA8BD,CAAA,CAAa,CAAb,CAA9B,CAAqD,CAEnD,IAAIE,EAAe,CACnB,KAAA,CAAOA,CAAP,CAAsBD,CAAtB,EAA2E,CAA3E,GAA4CD,CAAA,CAAaE,CAAb,CAA5C,CAAA,CACEA,CAAA,EAEF,IAAIA,CAAJ,GAAqBD,CAArB,CACE,IAAKD,CAAAA,YAAL,CAAoBD,CAAMI,CAAAA,IAAKH,CAAAA,YADjC,KAIE,KADA,IAAKA,CAAAA,YACIlC,CADW,IAAI/B,iBAAJ,CAAsBkE,CAAtB,CAA2CC,CAA3C,CACXpC,CAAAA,CAAAA,CAAI,CAAb,CAAgBA,CAAhB,CAAoB,IAAKkC,CAAAA,YAAanE,CAAAA,MAAtC,CAA8CiC,CAAA,EAA9C,CACE,IAAKkC,CAAAA,YAAL,CAAkBlC,CAAlB,CAAA,CAAuBkC,CAAA,CAAaE,CAAb,CAA4BpC,CAA5B,CAXwB,CAArD,IAeE,KAAKkC,CAAAA,YAAL,CAAoBA,EAIjBI,MAAM,GACX,MAAO,KAAKJ,CAAAA,YAAanE,CAAAA,MAAzB,CAAkC,EAG7BwE,MAAM,GACX,MAAgC,EAAhC,GAAO,IAAKL,CAAAA,YAAL,CAAkB,CAAlB,EAGFM,cAAc,CAACF,CAAD,EACnB,MAAO,KAAKJ,CAAAA,YAAL,CAAkB,IAAKA,CAAAA,YAAanE,CAAAA,MAApC,CAA6C,CAA7C,CAAiDuE,CAAjD,EAGFG,aAAa,CAACC,CAAD,EAClB,GAAI,IAAKH,CAAAA,MAAL,EAAJ,CACE,MAAOG,EAET;GAAIA,CAAMH,CAAAA,MAAN,EAAJ,CACE,MAAO,KAGT,KAAII,EAAsB,IAAKT,CAAAA,YAC3BU,EAAAA,CAAqBF,CAAMR,CAAAA,YAC3BS,EAAoB5E,CAAAA,MAAxB,CAAiC6E,CAAmB7E,CAAAA,MAApD,GACE,CAAC4E,CAAD,CAAsBC,CAAtB,CADF,CAC8C,CAACA,CAAD,CAAqBD,CAArB,CAD9C,CAGA,sCAAA,oBAEA,KAAK,IAAI3C,EAAI,CAAb,CAAgBA,CAAhB,CAAoB6C,CAApB,CAAgC7C,CAAA,EAAhC,CACE8C,CAAA,CAAQ9C,CAAR,CAAA,CAAa4C,CAAA,CAAmB5C,CAAnB,CAGf,KAASA,CAAT,CAAa6C,CAAb,CAAyB7C,CAAzB,CAA6B4C,CAAmB7E,CAAAA,MAAhD,CAAwDiC,CAAA,EAAxD,CACE8C,CAAA,CAAQ9C,CAAR,CAAA,CAA6B2C,CAAA1B,CAAoBjB,CAApBiB,CAAwB4B,CAAxB5B,CAA7B,CAAkE2B,CAAA1C,CAAmBF,CAAnBE,CAGpE,OAAO,KAAI8B,CAAJ,CAAkB,IAAKC,CAAAA,KAAvB,CAA8Ba,CAA9B,EAGFC,QAAQ,CAACC,CAAD,EACb,GAAe,CAAf,GAAIA,CAAJ,CACE,MAAO,KAAKf,CAAAA,KAAMI,CAAAA,IAEpB,IAAe,CAAf,GAAIW,CAAJ,CACE,MAAO,KAET,+BAAA,2BAEA,KAAK,IAAIhD,EAAI,CAAb,CAAgBA,CAAhB,CAAoBF,CAApB,CAA0BE,CAAA,EAA1B,CACEiD,CAAA,CAAQjD,CAAR,CAAA,CAAa,IAAKiC,CAAAA,KAAMc,CAAAA,QAAX,CAAoB,IAAKb,CAAAA,YAAL,CAAkBlC,CAAlB,CAApB,CAA0CgD,CAA1C,CAGf,OAAO,KAAIhB,CAAJ,CAAkB,IAAKC,CAAAA,KAAvB,CAA8BgB,CAA9B,EAGFC,YAAY,CAACR,CAAD,EACjB,GAAI,IAAKH,CAAAA,MAAL,EAAJ;AAAqBG,CAAMH,CAAAA,MAAN,EAArB,CACE,MAAO,KAAKN,CAAAA,KAAMI,CAAAA,IAEpB,wBAAA,4BAGA,eAAA,+BAEA,KAAK,IAAIrC,EAAI,CAAb,CAAgBA,CAAhB,CAAoBmD,CAApB,CAA6BnD,CAAA,EAA7B,CAAkC,CAChC,UACA,KAAK,IAAIoD,EAAI,CAAb,CAAgBA,CAAhB,CAAoBC,CAApB,CAA6BD,CAAA,EAA7B,CACEH,CAAA,CAAQjD,CAAR,CAAYoD,CAAZ,CAAA,CAAiBE,CAAA,CAAgBL,CAAA,CAAQjD,CAAR,CAAYoD,CAAZ,CAAhB,CACf,IAAKnB,CAAAA,KAAMc,CAAAA,QAAX,CAAoBQ,CAApB,CAA4BC,CAAA,CAAcJ,CAAd,CAA5B,CADe,CAHa,CAOlC,MAAO,KAAIpB,CAAJ,CAAkB,IAAKC,CAAAA,KAAvB,CAA8BgB,CAA9B,EAGFQ,kBAAkB,CAACnB,CAAD,CAAiBoB,CAAjB,EACvB,GAAa,CAAb,CAAIpB,CAAJ,CACE,KAAUzD,MAAJ,CAAU,4BAAV,CAAN,CAEF,GAAoB,CAApB,GAAI6E,CAAJ,CACE,MAAO,KAAKzB,CAAAA,KAAMI,CAAAA,IAEpB,4DAEA,KAAK,IAAIrC,EAAI,CAAb,CAAgBA,CAAhB,CAAoBF,CAApB,CAA0BE,CAAA,EAA1B,CACEiD,CAAA,CAAQjD,CAAR,CAAA,CAAa,IAAKiC,CAAAA,KAAMc,CAAAA,QAAX,CAAoB,IAAKb,CAAAA,YAAL,CAAkBlC,CAAlB,CAApB,CAA0C0D,CAA1C,CAEf,OAAO,KAAI1B,CAAJ,CAAkB,IAAKC,CAAAA,KAAvB;AAA8BgB,CAA9B,EAGFU,UAAU,CAAC1C,CAAD,EACf,IAAI5B,EAAS,CACb,IAAU,CAAV,GAAI4B,CAAJ,CAEE,MAAO,KAAKuB,CAAAA,cAAL,CAAoB,CAApB,CAET,+BACA,IAAU,CAAV,GAAIvB,CAAJ,CAKE,MAHA,KAAKiB,CAAAA,YAAa0B,CAAAA,OAAlB,CAA2BF,CAAD,GACCrE,CAAzB,EAAiCqE,EADnC,CAGOrE,CAAAA,CAETA,EAAA,CAAS,IAAK6C,CAAAA,YAAL,CAAkB,CAAlB,CACT,KAAK,IAAIlC,EAAI,CAAb,CAAgBA,CAAhB,CAAoBF,CAApB,CAA0BE,CAAA,EAA1B,CACEX,CAAA,CAASiE,CAAA,CAAgB,IAAKrB,CAAAA,KAAMc,CAAAA,QAAX,CAAoB9B,CAApB,CAAuB5B,CAAvB,CAAhB,CAAgD,IAAK6C,CAAAA,YAAL,CAAkBlC,CAAlB,CAAhD,CAEX,OAAOX,YCvIKiE,EAAe,CAACrC,CAAD,CAAYf,CAAZ,EAC7B,MAAOe,EAAP,CAAWf;KAGQ2D,IAUnBlG,YAAYmG,EAAmBhE,EAAciE,GAC3C,IAAKD,CAAAA,SAAL,CAAiBA,CACjB,KAAKhE,CAAAA,IAAL,CAAYA,CACZ,KAAKkE,CAAAA,aAAL,CAAqBD,CACrB,KAAKE,CAAAA,QAAL,CAAoBC,KAAJ,CAAU,IAAKpE,CAAAA,IAAf,CAChB,KAAKqE,CAAAA,QAAL,CAAoBD,KAAJ,CAAU,IAAKpE,CAAAA,IAAf,CAEZ3B,EAAAA,CAAI,CACR,KAAS6B,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CAAoB,IAAKF,CAAAA,IAAzB,CAA+BE,CAAA,EAA/B,CACE,IAAKiE,CAAAA,QAAL,CAAcjE,CAAd,CAEA,CAFmB7B,CAEnB,CADIA,CACJ,EADQ,CACR,CAAIA,CAAJ,EAAS,IAAK2B,CAAAA,IAAd,GACE3B,CADF,EACOA,CADP,CACW,IAAK2F,CAAAA,SADhB,EAC8B,IAAKhE,CAAAA,IADnC,CAC0C,CAD1C,CAKF,KAASE,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CAAoB,IAAKF,CAAAA,IAAzB,CAAgC,CAAhC,CAAmCE,CAAA,EAAnC,CACE,IAAKmE,CAAAA,QAAL,CAAc,IAAKF,CAAAA,QAAL,CAAcjE,CAAd,CAAd,CAAA,CAAkCA,CAEpC,KAAKqC,CAAAA,IAAL,CAAY,IAAIL,CAAJ,CAAkB,IAAlB,CAAwB/D,iBAAkBmG,CAAAA,IAAlB,CAAuB,CAAC,CAAD,CAAvB,CAAxB,CACZ,KAAKC,CAAAA,GAAL,CAAW,IAAIrC,CAAJ,CAAkB,IAAlB,CAAwB/D,iBAAkBmG,CAAAA,IAAlB,CAAuB,CAAC,CAAD,CAAvB,CAAxB,EAGNrB,QAAQ,CAAC9B,CAAD,CAAYf,CAAZ,EACb,MAAU,EAAV,GAAIe,CAAJ,EAAqB,CAArB,GAAef,CAAf,CACS,CADT,CAGO,IAAK+D,CAAAA,QAAL,EAAe,IAAKE,CAAAA,QAAL,CAAclD,CAAd,CAAf,CAAkC,IAAKkD,CAAAA,QAAL,CAAcjE,CAAd,CAAlC,GAAuD,IAAKJ,CAAAA,IAA5D,CAAmE,CAAnE,GAGFwE,OAAO,CAACrD,CAAD,EACZ,GAAU,CAAV;AAAIA,CAAJ,CACE,KAAUpC,MAAJ,CAAU,gBAAV,CAAN,CAEF,MAAO,KAAKoF,CAAAA,QAAL,CAAc,IAAKnE,CAAAA,IAAnB,CAA0B,IAAKqE,CAAAA,QAAL,CAAclD,CAAd,CAA1B,CAA6C,CAA7C,EAGFsD,aAAa,CAACjC,CAAD,CAAiBoB,CAAjB,EAClB,GAAa,CAAb,CAAIpB,CAAJ,CACE,KAAUzD,MAAJ,CAAU,qCAAV,CAAN,CAEF,GAAoB,CAApB,GAAI6E,CAAJ,CACE,MAAO,KAAKrB,CAAAA,iCAGdH,EAAA,CAAa,CAAb,CAAA,CAAkBwB,CAClB,OAAO,KAAI1B,CAAJ,CAAkB,IAAlB,CAAwBE,CAAxB,EAGFsC,GAAG,CAACvD,CAAD,EACR,GAAU,CAAV,GAAIA,CAAJ,CACE,KAAUpC,MAAJ,CAAU,mBAAV,CAAN,CAEF,MAAO,KAAKsF,CAAAA,QAAL,CAAclD,CAAd,EAGFwD,GAAG,CAACxD,CAAD,EACR,MAAO,KAAKgD,CAAAA,QAAL,CAAchD,CAAd;ACtEXyD,QAASA,GAAqB,CAACzC,CAAD,CAAmBhB,CAAnB,CAAqCf,CAArC,CAAuDyE,CAAvD,EAExB1D,CAAEqB,CAAAA,MAAF,EAAJ,CAAiBpC,CAAEoC,CAAAA,MAAF,EAAjB,GACE,CAACrB,CAAD,CAAIf,CAAJ,CADF,CACW,CAACA,CAAD,CAAIe,CAAJ,CADX,CAMA,KAAI2D,EAAQ3C,CAAMI,CAAAA,IAIlB,KAHA,IAAIwC,EAAI5C,CAAMoC,CAAAA,GAGd,CAAOS,CAAExC,CAAAA,MAAF,EAAP,EAAqBqC,CAArB,CAAyB,CAAzB,CAAA,CAA4B,CAC1B,OACA,QACAI,EAAA,CAAQD,CACRF,EAAA,CAAQC,CAGR,IAAIE,CAAMxC,CAAAA,MAAN,EAAJ,CAEE,MAAO,KAETuC,EAAA,CAAIE,CACAC,EAAAA,CAAIhD,CAAMI,CAAAA,mCAGd,MAAA,aAAA,CAAOyC,CAAExC,CAAAA,MAAF,EAAP,EAAqByC,CAAMzC,CAAAA,MAAN,EAArB,EAAuC,CAACwC,CAAEvC,CAAAA,MAAF,EAAxC,CAAA,CAAoD,CAClD,OAAmBD,CAAAA,mBAAnB,eAC6BE,CAAAA,gBAAiBF,CAAAA,UAAW4C,EACzDD,EAAA,CAAIA,CAAExC,CAAAA,aAAF,CAAgBR,CAAMsC,CAAAA,aAAN,CAAoBY,CAApB,CAAgCC,CAAhC,CAAhB,CACJN,EAAA,CAAIA,CAAErC,CAAAA,aAAF,CAAgBsC,CAAMtB,CAAAA,kBAAN,CAAyB0B,CAAzB,CAAqCC,CAArC,CAAhB,CAJ8C,CAOpDP,CAAA,CAAII,CAAE/B,CAAAA,YAAF,CAAe0B,CAAf,CAAsBnC,CAAAA,aAAtB,CAAoC4C,CAApC,CAEJ,IAAIP,CAAExC,CAAAA,MAAF,EAAJ,EAAkByC,CAAMzC,CAAAA,MAAN,EAAlB,CACE,MAAO,KAzBiB,IA6BHE,CAAAA,iBACzB;GAAyB,CAAzB,GAAI8C,CAAJ,CACE,MAAO,oBAIT,OAAO,CAACT,CAAE9B,CAAAA,QAAF,CAAWuB,CAAX,CAAD,CAAsBQ,CAAE/B,CAAAA,QAAF,CAAWuB,CAAX,CAAtB;QA2CO/D,GAAM,CAACvB,CAAD,CAAkBuG,CAAlB,EACpB,qCACAC,EAAYnH,CAAAA,GAAZ,CAAgBW,CAAhB,eAEkC,IAAK,EACvC,eAAoCwG,EAApC,2BAAA,CAGIC,EAAQ,CAAA,CACZ,KAAK,IAAIC,EAAI,CAAb,CAAgBA,CAAhB,CAAoBH,CAApB,CAA0BG,CAAA,EAA1B,CAA+B,CAC7B,4CACAC,EAAA,CAAqBA,CAAqB5H,CAAAA,MAA1C,CAAmD,CAAnD,CAAuD2H,CAAvD,CAAA,CAA4DE,CACzC,EAAnB,GAAIA,CAAJ,GACEH,CADF,CACU,CAAA,CADV,CAH6B,CAO/B,GAAI,CAACA,CAAL,CACE,MAAOD,aAG+BG,UAEM1D,eAAA,EAAA,CAA0B,CAA1B,EAA8B4D,EAAUN,EACtF,IAAmB,IAAnB,GAAIO,CAAJ,CACE,MAAO,KAGsC,EAAA,CAAAA,CAAA,EAAA,cAhE/C,IAAkB,CAAlB,GAAIC,CAAJ,CACE,CAAA,CAAO,CAACC,CAAaxD,CAAAA,cAAb,CAA4B,CAA5B,CAAD,CADT,KAAA,WAIIyD,EAAAA,CAAa,CACjB,KAASjG,CAAT,CAAa,CAAb,CAAgBA,CAAhB,EAA0BF,CAAAA,IAA1B,EAAkCmG,CAAlC,CAA+CF,CAA/C,CAA0D/F,CAAA,EAA1D,CACqC,CAAnC,GAAIgG,CAAarC,CAAAA,UAAb,CAAwB3D,CAAxB,CAAJ,GACEX,CAAA,CAAO4G,CAAP,CACA,EAD2B3B,CAAAA,OAAN,CAActE,CAAd,CACrB,CAAAiG,CAAA,EAFF,CAMA,EAAA,CADEA,CAAJ,GAAmBF,CAAnB,CACS,IADT,CAGO1G,CAdP,CAiEA,GAAsB,IAAtB,EAAI6G,CAAJ,CACE,MAAO,KAGwC,EAAA,CAAAJ,CAAA,EAAA;EAAeI,mBAhDhE,KAASlG,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CAAoB0F,CAApB,CAAuB1F,CAAA,EAAvB,CAA4B,aAgDoCkG,KA9C9D,KAAIC,EAAc,CAClB,KAAK,IAAI/C,EAAI,CAAb,CAAgBA,CAAhB,CAAoBsC,CAApB,CAAuBtC,CAAA,EAAvB,CACMpD,CAAJ,GAAUoD,CAAV,GACE+C,CADF,EACsBpD,CAAAA,QAAN,CAAeoD,CAAf,CAA4B7C,CAAA,CAAgB,CAAhB,EAAyBP,CAAAA,QAAN,CA2CHmD,CA3CkB,CAAe9C,CAAf,CAAf,CAAkCgD,CAAlC,CAAnB,CAA5B,CADhB,CAIF/G,EAAA,CAAOW,CAAP,CAAA,EAAkB+C,CAAAA,QAAN,CAAesD,CAAe1C,CAAAA,UAAf,CAA0ByC,CAA1B,CAAf,EAA2D9B,CAAAA,OAAN,CAAc6B,CAAd,CAArD,CACgB,EAA5B,IAAUnC,CAAAA,aAAV,GACE3E,CAAA,CAAOW,CAAP,CADF,EACoB+C,CAAAA,QAAN,CAAe1D,CAAA,CAAOW,CAAP,CAAf,CAA0BoG,CAA1B,CADd,CAT0B,CAiD5B,IAASpG,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CAAoBkG,CAAenI,CAAAA,MAAnC,CAA2CiC,CAAA,EAA3C,CAAgD,yBAE9C,IAAe,CAAf,CAAIsG,CAAJ,CACE,MAAO,KAETd,EAAA,CAAYc,CAAZ,CAAA,EAzCKjH,CAyC0Da,CAAgBF,CAAhBE,CALjB,CAQhD,MAAOsF;ACzHF,OACL,CACEe,SAAU,IADZ,CAEEC,cAAe,CAFjB,CAGEC,wBAAyB,EAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,CADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CALqB,CASrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CATqB,CAarB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,CAAvC,CAAD,CAFZ,CAbqB,CAJzB,EAuBA,CACEP,SAAU,IADZ,CAEEC,cAAe,CAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb;AAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CALqB,CASrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CATqB,CAarB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CAbqB,CAJzB,EAuBA,CACEP,SAAU,IADZ,CAEEC,cAAe,CAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CALqB,CASrB,CACEH,oBAAqB,EADvB;AAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CATqB,CAarB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CAbqB,CAJzB,EAuBA,CACEP,SAAU,IADZ,CAEEC,cAAe,CAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CALqB,CASrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CATqB,CAarB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,CAAvC,CAAD,CAFZ,CAbqB,CAJzB;AAuBA,CACEP,SAAU,IADZ,CAEEC,cAAe,CAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CALqB,CASrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CATqB,CAgBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAhBqB,CAJzB,EA6BA,CACEP,SAAU,IADZ,CAEEC,cAAe,CAFjB,CAGEC,wBAAyB,CAAC,CAAD;AAAI,EAAJ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CALqB,CASrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CATqB,CAarB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CAbqB,CAJzB,EAuBA,CACEP,SAAU,KADZ,CAEEC,cAAe,CAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB;AAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CALqB,CASrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CATqB,CAgBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAhBqB,CAJzB,EA6BA,CACEP,SAAU,KADZ,CAEEC,cAAe,CAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ;AAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CALqB,CAYrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAZqB,CAmBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAnBqB,CAJzB,EAgCA,CACEP,SAAU,KADZ,CAEEC,cAAe,CAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ;AAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CALqB,CAYrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAZqB,CAmBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAnBqB,CAJzB,EAgCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb;AAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb;AAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CALqB,CAYrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAZqB,CAmBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAnBqB,CAJzB,EAgCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CADqB;AAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAAD,CAFZ,CADqB;AAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CALqB,CAYrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAZqB,CAmBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAnBqB,CAJzB,EAgCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ;AAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB;AAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR;AAAY,EAAZ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,KADZ;AAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb;AAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb;AAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB;AAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ;AAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,EAAhB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAAD,CAFZ,CARqB,CAYrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ;AAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAZqB,CAmBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAnBqB,CAJzB,EAgCA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,EAAhB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAAD,CAFZ,CARqB,CAYrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ;AAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAZqB,CAmBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAAD,CAFZ,CAnBqB,CAJzB,EA6BA,CACEP,SAAU,KADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ;AAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB;AAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb;AAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb;AAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB;AAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,EAAhB,CAAoB,GAApB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ;AAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAAqB,GAArB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB;AAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD;AAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAAqB,GAArB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB;AAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAAqB,GAArB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ;AAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAAqB,GAArB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CAAC,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CAAD,CAFZ,CADqB,CAKrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CALqB,CAYrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAZqB,CAmBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb;AAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAnBqB,CAJzB,EAgCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAAqB,GAArB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB;AAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAAqB,GAArB,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb;AAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAC,CAAD,CAAI,EAAJ,CAAQ,EAAR,CAAY,EAAZ,CAAgB,GAAhB,CAAqB,GAArB,CAA0B,GAA1B,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB;AAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAE,CAAF,CAAK,EAAL,CAAS,EAAT,CAAa,EAAb,CAAiB,GAAjB,CAAsB,GAAtB,CAA2B,GAA3B,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb;AAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAE,CAAF,CAAK,EAAL,CAAS,EAAT,CAAa,EAAb,CAAiB,GAAjB,CAAsB,GAAtB,CAA2B,GAA3B,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB;AAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAE,CAAF,CAAK,EAAL,CAAS,EAAT,CAAa,EAAb,CAAiB,GAAjB,CAAsB,GAAtB,CAA2B,GAA3B,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,CAAb;AAAgBC,sBAAuB,GAAvC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ,CAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAE,CAAF,CAAK,EAAL,CAAS,EAAT,CAAa,EAAb,CAAiB,GAAjB,CAAsB,GAAtB,CAA2B,GAA3B,CAH3B;AAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,EAAvC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EAmCA,CACEP,SAAU,MADZ;AAEEC,cAAe,EAFjB,CAGEC,wBAAyB,CAAE,CAAF,CAAK,EAAL,CAAS,EAAT,CAAa,EAAb,CAAiB,GAAjB,CAAsB,GAAtB,CAA2B,GAA3B,CAH3B,CAIEC,sBAAuB,CACrB,CACEC,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,GAAxC,CADQ,CAER,CAAED,UAAW,CAAb,CAAgBC,sBAAuB,GAAvC,CAFQ,CAFZ,CADqB,CAQrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CARqB,CAerB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ,CAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAfqB,CAsBrB,CACEH,oBAAqB,EADvB,CAEEC,SAAU,CACR,CAAEC,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CADQ;AAER,CAAED,UAAW,EAAb,CAAiBC,sBAAuB,EAAxC,CAFQ,CAFZ,CAtBqB,CAJzB,EC5vCFC,SAASA,EAAgB,CAAC5I,CAAD,CAAYC,CAAZ,EACfD,CAAJ6I,EAAQ5I,CAEZ,KADI6I,CACJ,CADe,CACf,CAAOD,CAAP,CAAA,CACEC,CAAA,EACA,CAAAD,CAAA,EAAKA,CAAL,CAAS,CAEX,OAAOC,GAGTC,QAASA,EAAO,CAACC,CAAD,CAAWC,CAAX,EACd,MAAQA,EAAR,EAAgB,CAAhB,CAAqBD;AAIvB,QACE,CAAEE,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB;AAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,IAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,IAAR;AAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,IAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,IAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,IAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,GAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,IAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,IAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB;AAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,IAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,IAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EACA,CAAEH,KAAM,KAAR,CAAgBC,WAAY,CAAEC,qBAAsB,CAAxB,CAA2BC,SAAU,CAArC,CAA5B,EAhCF,KAoCGC,CAAD,EAAoC,CAApC,IAAgBA,CAAErJ,CAAAA,CAAlB,CAAsBqJ,CAAEtJ,CAAAA,CAAxB,EAA6B,EAC5BsJ,CAAD,EAA4B,CAA5B,GAAeA,CAAErJ,CAAAA,CAAjB,CAAqB,EACpBqJ,CAAD,EAA0B,CAA1B,GAAcA,CAAEtJ,CAAAA,CAAhB,CAAoB,EACnBsJ,CAAD,EAAkC,CAAlC,IAAeA,CAAErJ,CAAAA,CAAjB,CAAqBqJ,CAAEtJ,CAAAA,CAAvB,EAA4B,EAC3BsJ,CAAD,EAAkE,CAAlE,IAAehG,IAAKC,CAAAA,KAAL,CAAW+F,CAAErJ,CAAAA,CAAb,CAAiB,CAAjB,CAAf,CAAqCqD,IAAKC,CAAAA,KAAL,CAAW+F,CAAEtJ,CAAAA,CAAb,CAAiB,CAAjB,CAArC,EAA4D,EAC3DsJ,CAAD,EAAwD,CAAxD,GAAgBA,CAAEtJ,CAAAA,CAAlB,CAAsBsJ,CAAErJ,CAAAA,CAAxB;AAA6B,CAA7B,CAAoCqJ,CAAEtJ,CAAAA,CAAtC,CAA0CsJ,CAAErJ,CAAAA,CAA5C,CAAiD,EAChDqJ,CAAD,EAA8D,CAA9D,IAAkBA,CAAErJ,CAAAA,CAApB,CAAwBqJ,CAAEtJ,CAAAA,CAA1B,CAA+B,CAA/B,CAAqCsJ,CAAErJ,CAAAA,CAAvC,CAA2CqJ,CAAEtJ,CAAAA,CAA7C,CAAkD,CAAlD,EAAuD,EACtDsJ,CAAD,EAA8D,CAA9D,KAAkBA,CAAErJ,CAAAA,CAApB,CAAwBqJ,CAAEtJ,CAAAA,CAA1B,EAA+B,CAA/B,CAAqCsJ,CAAErJ,CAAAA,CAAvC,CAA2CqJ,CAAEtJ,CAAAA,CAA7C,CAAkD,CAAlD,EAAuD,EAoCzDuJ;QAASA,GAAa,CAACC,CAAD,CAAoBnH,CAApB,CAAsC8G,CAAtC,kBAEpB,eA7BA,2BACA,uBAA8CM,EAE9CD,EAAOpJ,CAAAA,SAAP,CAAiB,CAAjB,CAAoB,CAApB,CAAuB,CAAvB,CAA0B,CAA1B,CAA6B,CAAA,CAA7B,CACAoJ,EAAOpJ,CAAAA,SAAP,CAAiBqJ,CAAjB,CAA6B,CAA7B,CAAgC,CAAhC,CAAmC,CAAnC,CAAsC,CAAtC,CAAyC,CAAA,CAAzC,CACAD,EAAOpJ,CAAAA,SAAP,CAAiB,CAAjB,CAAoBqJ,CAApB,CAAgC,CAAhC,CAAmC,CAAnC,CAAsC,CAAtC,CAAyC,CAAA,CAAzC,CAGA,KAAK,KAAL,6BAAA,CACE,IAAK,KAAL,6BAAA,CACc,CAAZ,GAAMzJ,CAAN,EAAuB,CAAvB,GAAiBC,CAAjB,EAAkC,CAAlC,GAA4BD,CAA5B,EAAuCC,CAAvC,GAA6CwJ,CAA7C,CAAyD,CAAzD,EAA8DzJ,CAA9D,GAAoEyJ,CAApE,CAAgF,CAAhF,EAA2F,CAA3F,GAAqFxJ,CAArF,EACEuJ,CAAOpJ,CAAAA,SAAP,CAAiBJ,CAAjB,CAAqB,CAArB,CAAwBC,CAAxB,CAA4B,CAA5B,CAA+B,CAA/B,CAAkC,CAAlC,CAAqC,CAAA,CAArC,CAKNuJ,EAAOpJ,CAAAA,SAAP,CAAiB,CAAjB,CAAoB,CAApB,CAAuB,CAAvB,CAA0BqJ,CAA1B,CAAsC,EAAtC,CAA0C,CAAA,CAA1C,CACAD,EAAOpJ,CAAAA,SAAP,CAAiB,CAAjB,CAAoB,CAApB,CAAuBqJ,CAAvB,CAAmC,EAAnC,CAAuC,CAAvC,CAA0C,CAAA,CAA1C,CAE4B,EAA5B,EAAYpB,CAAAA,aAAZ,GACEmB,CAAOpJ,CAAAA,SAAP,CAAiBqJ,CAAjB,CAA6B,EAA7B,CAAiC,CAAjC,CAAoC,CAApC,CAAuC,CAAvC,CAA0C,CAAA,CAA1C,CACA,CAAAD,CAAOpJ,CAAAA,SAAP,CAAiB,CAAjB,CAAoBqJ,CAApB,CAAgC,EAAhC,CAAoC,CAApC,CAAuC,CAAvC,CAA0C,CAAA,CAA1C,CAFF,MAgBIC,EAAAA,CADAC,CACAD,CADc,CAIdE,EAAAA,CAAY,CAAA,CAChB,KAAK,IAAIC,EAAcJ,CAAdI,CAA0B,CAAnC,CAAoD,CAApD,CAAsCA,CAAtC,CAAuDA,CAAvD,EAAsE,CAAtE,CAAyE,CACnD,CAApB;AAAIA,CAAJ,EACEA,CAAA,EAEF,KAAK,IAAIhI,EAAI,CAAb,CAAgBA,CAAhB,CAAoB4H,CAApB,CAA+B5H,CAAA,EAA/B,CAAoC,CAClC,eACA,KAAK,IAAIiI,EAAe,CAAxB,CAA0C,CAA1C,CAA2BA,CAA3B,CAA6CA,CAAA,EAA7C,CAA6D,CAC3D,SACA,IAAI,CAvBHN,CAuBwBzJ,CAAAA,GAApB,CAAwBC,CAAxB,CAA2BC,CAA3B,CAAL,CAAoC,CAClCyJ,CAAA,EACA,KAAIV,EAAMQ,CAAOzJ,CAAAA,GAAP,CAAWC,CAAX,CAAcC,CAAd,CACNoJ,EAAA,CAAS,CAACpJ,EAAAA,CAAD,CAAID,EAAAA,CAAJ,CAAT,CAAJ,GACEgJ,CADF,CACQ,CAACA,CADT,CAGAW,EAAA,CAA2BA,CAA3B,EA7GQ,CA6GR,CAAsBX,CACL,EAAjB,GAAIU,CAAJ,GACEK,CAAUjI,CAAAA,IAAV,CAAe6H,CAAf,CAEA,CAAAA,CAAA,CADAD,CACA,CADW,CAFb,CAPkC,CAFuB,CAF3B,CAmBpCE,CAAA,CAAY,CAACA,CAvB0D,CAyBzE,MAAOG;AAGTC,QAASA,GAAW,CAACR,CAAD,EAClB,cAAA,eAEqCC,QACrC,IAA0B,CAA1B,EAAIQ,CAAJ,CACE,MAAOC,EAAA,CAASD,CAAT,CAA8B,CAA9B,CAGLE,EAAAA,CAAsB,CAC1B,KAAK,IAAIlK,EAAI,CAAb,CAAqB,CAArB,EAAgBA,CAAhB,CAAwBA,CAAA,EAAxB,CACE,IAAK,IAAID,EAAIyJ,CAAJzJ,CAAgB,CAAzB,CAA4BA,CAA5B,EAAiCyJ,CAAjC,CAA6C,EAA7C,CAAiDzJ,CAAA,EAAjD,CACEmK,CAAA,CAAsBpB,CAAA,CAAQS,CAAOzJ,CAAAA,GAAP,CAAWC,CAAX,CAAcC,CAAd,CAAR,CAA0BkK,CAA1B,CAItBC,EAAAA,CAAwB,CAC5B,KAASpK,CAAT,CAAa,CAAb,CAAqB,CAArB,EAAgBA,CAAhB,CAAwBA,CAAA,EAAxB,CACE,IAAK,IAAIC,EAAIwJ,CAAJxJ,CAAgB,CAAzB,CAA4BA,CAA5B,EAAiCwJ,CAAjC,CAA6C,EAA7C,CAAiDxJ,CAAA,EAAjD,CACEmK,CAAA,CAAwBrB,CAAA,CAAQS,CAAOzJ,CAAAA,GAAP,CAAWC,CAAX,CAAcC,CAAd,CAAR,CAA0BmK,CAA1B,CAIxBC,EAAAA,CAAiBC,QACrB,KAAIC,CACJ,KAAK,KAAL,KAAA,CAA8B,CAC5B,GAAIlI,CAAQ+F,CAAAA,QAAZ,GAAyB+B,CAAzB,EAAgD9H,CAAQ+F,CAAAA,QAAxD,GAAqEgC,CAArE,CACE,MAAO/H,EAGLmI,EAAAA,CAAa5B,CAAA,CAAiBuB,CAAjB,CAAsC9H,CAAQ+F,CAAAA,QAA9C,CACboC,EAAJ,CAAiBH,CAAjB,GACEE,CACA,CADclI,CACd,CAAAgI,CAAA,CAAiBG,CAFnB,CAKAA,EAAA,CAAa5B,CAAA,CAAiBwB,CAAjB,CAAwC/H,CAAQ+F,CAAAA,QAAhD,CACToC,EAAJ,CAAiBH,CAAjB,GACEE,CACA,CADclI,CACd,CAAAgI,CAAA,CAAiBG,CAFnB,CAZ4B,CAmB9B,GAAsB,CAAtB,EAAIH,CAAJ,CACE,MAAOE;AAIXE,QAASA,GAAqB,CAACjB,CAAD,EAC5B,IAAIkB,EAAwB,CAC5B,KAAK,IAAI1K,EAAI,CAAb,CAAqB,CAArB,EAAgBA,CAAhB,CAAwBA,CAAA,EAAxB,CACY,CAAV,GAAIA,CAAJ,GACE0K,CADF,CAC0B3B,CAAA,CAAQS,CAAOzJ,CAAAA,GAAP,CAAWC,CAAX,CAAc,CAAd,CAAR,CAA0B0K,CAA1B,CAD1B,CAIF,KAASzK,CAAT,CAAa,CAAb,CAAqB,CAArB,EAAgBA,CAAhB,CAAwBA,CAAA,EAAxB,CACY,CAAV,GAAIA,CAAJ,GACEyK,CADF,CAC0B3B,CAAA,CAAQS,CAAOzJ,CAAAA,GAAP,CAAW,CAAX,CAAcE,CAAd,CAAR,CAA0ByK,CAA1B,CAD1B,CAKF,eACIC,EAAAA,CAAoC,CACxC,KAAK,IAAI1K,EAAIwJ,CAAJxJ,CAAgB,CAAzB,CAA4BA,CAA5B,EAAiCwJ,CAAjC,CAA6C,CAA7C,CAAgDxJ,CAAA,EAAhD,CACE0K,CAAA,CAAoC5B,CAAA,CAAQS,CAAOzJ,CAAAA,GAAP,CAAW,CAAX,CAAcE,CAAd,CAAR,CAA0B0K,CAA1B,CAEtC,KAAS3K,CAAT,CAAayJ,CAAb,CAAyB,CAAzB,CAA4BzJ,CAA5B,CAAgCyJ,CAAhC,CAA2CzJ,CAAA,EAA3C,CACE2K,CAAA,CAAoC5B,CAAA,CAAQS,CAAOzJ,CAAAA,GAAP,CAAWC,CAAX,CAAc,CAAd,CAAR,CAA0B2K,CAA1B,CAGlCN,EAAAA,CAAiBC,QACjBM,EAAAA,CAAiB,IACrB,KAAK,KAAM,KAAA1B,EAAK,WAAAC,EAAhB,KAAA,CAAkD,CAChD,GAAID,CAAJ,GAAawB,CAAb,EAAsCxB,CAAtC,GAA+CyB,CAA/C,CACE,MAAOxB,EAELqB,EAAAA,CAAa5B,CAAA,CAAiB8B,CAAjB,CAAwCxB,CAAxC,CACbsB,EAAJ,CAAiBH,CAAjB,GACEO,CACA,CADiBzB,CACjB,CAAAkB,CAAA,CAAiBG,CAFnB,CAIIE,EAAJ,GAA8BC,CAA9B,GACEH,CACA,CADa5B,CAAA,CAAiB+B,CAAjB,CAAoDzB,CAApD,CACb,CAAIsB,CAAJ,CAAiBH,CAAjB,GACEO,CACA,CADiBzB,CACjB,CAAAkB,CAAA,CAAiBG,CAFnB,CAFF,CATgD,CAkBlD,MAAsB,EAAtB,EAAIH,CAAJ,CACSO,CADT,CAGO;AAGTC,QAASA,GAAa,CAACd,CAAD,CAAsB1H,CAAtB,CAAwCyI,CAAxC,EACpB,gCAAA,KAAA,CAMIC,EAAiB,CACrBC,EAAOvC,CAAAA,QAAShD,CAAAA,OAAhB,CAAwBwF,CAAA,GACtB,IAAK,IAAIpJ,EAAI,CAAb,CAAgBA,CAAhB,CAAoBoJ,CAAMvC,CAAAA,SAA1B,CAAqC7G,CAAA,EAArC,CACEqJ,CAAWpJ,CAAAA,IAAX,CAAgB,CAAEqJ,iBAAkBF,CAAMtC,CAAAA,qBAA1B,CAAiDoB,UAAW,EAA5D,CAAhB,CACA,CAAAgB,CAAA,EAAkBE,CAAMtC,CAAAA,qBAAxB,CAAgDqC,CAAOxC,CAAAA,oBAH3D,CAUA,IAAIuB,CAAUnK,CAAAA,MAAd,CAAuBmL,CAAvB,CACE,MAAO,KAEThB,EAAA,CAAYA,CAAUqB,CAAAA,KAAV,CAAgB,CAAhB,CAAmBL,CAAnB,iBAE4BpC,CAAAA,qBAExC,KAAS9G,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CAAoBwJ,CAApB,CAAoCxJ,CAAA,EAApC,CACE,IAAK,KAAL,KAAA,CACEyJ,CAAUvB,CAAAA,SAAUjI,CAAAA,IAApB,CAAyBiI,CAAUwB,CAAAA,KAAV,EAAzB,CAKJ,IAA6B,CAA7B,CAAIP,CAAOvC,CAAAA,QAAS7I,CAAAA,MAApB,CAGE,KAASiC,cAFgC6G,CAAAA,SAEhC7G,EAAAA,cADgC6G,CAAAA,SAChC7G,CAAAA,CAAAA,CAAI,CAAb,CAAgBA,CAAhB,CAAoB2J,CAApB,CAAqC3J,CAAA,EAArC,CACEqJ,CAAA,CAAWO,CAAX,CAA6B5J,CAA7B,CAAgCkI,CAAAA,SAAUjI,CAAAA,IAA1C,CAA+CiI,CAAUwB,CAAAA,KAAV,EAA/C,CAKJ,KAAA,CAA0B,CAA1B,CAAOxB,CAAUnK,CAAAA,MAAjB,CAAA,CACE,IAAK,KAAL,KAAA,CACE0L,CAAUvB,CAAAA,SAAUjI,CAAAA,IAApB,CAAyBiI,CAAUwB,CAAAA,KAAV,EAAzB,CAIJ;MAAOL,GAGTQ,QAASA,EAAY,CAAClC,CAAD,EACnB,WACA,IAAI,CAACnH,CAAL,CACE,MAAO,KAGT,YACA,IAAI,CAAC8G,CAAL,CACE,MAAO,aAG6B9G,EAAS8G,EAC/C,YAA0C9G,EAAS8G,uBACnD,IAAI,CAAC+B,CAAL,CACE,MAAO,kBAI2BpI,EAAGf,OAAYoJ,CAAAA,iBAAkB,6BAGjEQ,EAAAA,CAAc,CAClB,KAAK,KAAL,KAAA,CAAkC,kBACmBL,sCACnD,IAAI,CAACM,CAAL,CACE,MAAO,KAET,KAAK,IAAI/J,EAAI,CAAb,CAAgBA,CAAhB,CAAoByJ,CAAUH,CAAAA,gBAA9B,CAAgDtJ,CAAA,EAAhD,CACEgK,CAAA,CAAYF,CAAA,EAAZ,CAAA,CAA6BC,CAAA,CAAe/J,CAAf,CANC,CAUlC,GAAI,CACF,MAAOiK,GAAAA,CAAWD,CAAXC,CAAwBzJ,CAAQgG,CAAAA,aAAhCyD,CADL,CAEF,OAAA,CAAM,CACN,MAAO,KADD;AClTVC,QAASA,EAAqB,CAACC,CAAD,CAAYC,CAAZ,CAAuBC,CAAvB,CAAkCC,CAAlC,EAC5B,qBACA,sBACA,IAAY,CAAZ,GAAIC,CAAJ,EAAyB,CAAzB,GAAiBC,CAAjB,CACE,MAAO,CACLC,IAAKL,CAAGjM,CAAAA,CAARsM,CAAYN,CAAGhM,CAAAA,CADV,CAELuM,IAAKN,CAAGhM,CAAAA,CAARsM,CAAYP,CAAG/L,CAAAA,CAFV,CAGLuM,IAAK,CAHA,CAILC,IAAKP,CAAGlM,CAAAA,CAARyM,CAAYR,CAAGjM,CAAAA,CAJV,CAKL0M,IAAKR,CAAGjM,CAAAA,CAARyM,CAAYT,CAAGhM,CAAAA,CALV,CAML0M,IAAK,CANA,CAOLC,IAAKZ,CAAGhM,CAAAA,CAPH,CAQL6M,IAAKb,CAAG/L,CAAAA,CARH,CASL6M,IAAK,CATA,CAYP,cACA,cACA,cAAA,gDAKA,OAAO,CACLR,IAAKL,CAAGjM,CAAAA,CAARsM,CAAYN,CAAGhM,CAAAA,CAAfsM,CAAmBE,CAAnBF,CAAyBL,CAAGjM,CAAAA,CADvB,CAELuM,IAAKN,CAAGhM,CAAAA,CAARsM,CAAYP,CAAG/L,CAAAA,CAAfsM,CAAmBC,CAAnBD,CAAyBN,CAAGhM,CAAAA,CAFvB,CAGLuM,IAAAA,CAHK,CAILC,IAAKN,CAAGnM,CAAAA,CAARyM,CAAYT,CAAGhM,CAAAA,CAAfyM,CAAmBE,CAAnBF,CAAyBN,CAAGnM,CAAAA,CAJvB,CAKL0M,IAAKP,CAAGlM,CAAAA,CAARyM,CAAYV,CAAG/L,CAAAA,CAAfyM,CAAmBC,CAAnBD,CAAyBP,CAAGlM,CAAAA,CALvB,CAML0M,IAAAA,CANK,CAOLC,IAAKZ,CAAGhM,CAAAA,CAPH,CAQL6M,IAAKb,CAAG/L,CAAAA,CARH,CASL6M,IAAK,CATA;AAcXC,QAASA,GAAqB,CAACf,CAAD,CAAYC,CAAZ,CAAuBC,CAAvB,CAAkCC,CAAlC,QAESF,EAAIC,EAAIC,EAC7C,OAAO,CACLG,IAAKU,CAAKN,CAAAA,GAAVJ,CAAgBU,CAAKF,CAAAA,GAArBR,CAA2BU,CAAKL,CAAAA,GAAhCL,CAAsCU,CAAKH,CAAAA,GADtC,CAELN,IAAKS,CAAKR,CAAAA,GAAVD,CAAgBS,CAAKH,CAAAA,GAArBN,CAA2BS,CAAKT,CAAAA,GAAhCA,CAAsCS,CAAKF,CAAAA,GAFtC,CAGLN,IAAKQ,CAAKT,CAAAA,GAAVC,CAAgBQ,CAAKL,CAAAA,GAArBH,CAA2BQ,CAAKR,CAAAA,GAAhCA,CAAsCQ,CAAKN,CAAAA,GAHtC,CAILD,IAAKO,CAAKL,CAAAA,GAAVF,CAAgBO,CAAKJ,CAAAA,GAArBH,CAA2BO,CAAKP,CAAAA,GAAhCA,CAAsCO,CAAKF,CAAAA,GAJtC,CAKLJ,IAAKM,CAAKV,CAAAA,GAAVI,CAAgBM,CAAKF,CAAAA,GAArBJ,CAA2BM,CAAKR,CAAAA,GAAhCE,CAAsCM,CAAKJ,CAAAA,GALtC,CAMLD,IAAKK,CAAKR,CAAAA,GAAVG,CAAgBK,CAAKP,CAAAA,GAArBE,CAA2BK,CAAKV,CAAAA,GAAhCK,CAAsCK,CAAKL,CAAAA,GANtC,CAOLC,IAAKI,CAAKP,CAAAA,GAAVG,CAAgBI,CAAKH,CAAAA,GAArBD,CAA2BI,CAAKN,CAAAA,GAAhCE,CAAsCI,CAAKJ,CAAAA,GAPtC,CAQLC,IAAKG,CAAKT,CAAAA,GAAVM,CAAgBG,CAAKJ,CAAAA,GAArBC,CAA2BG,CAAKV,CAAAA,GAAhCO,CAAsCG,CAAKH,CAAAA,GARtC,CASLC,IAAKE,CAAKV,CAAAA,GAAVQ,CAAgBE,CAAKN,CAAAA,GAArBI,CAA2BE,CAAKT,CAAAA,GAAhCO,CAAsCE,CAAKP,CAAAA,GATtC;QA2BOQ,GAAO,CAACC,CAAD,CAAmBC,CAAnB,EACrB,UACEnN,EAAE,IAAKC,EAAG,KAAM,CAChBD,EAAEmN,WAAFnN,IADgB,CACYC,EAAG,GADf,EACqB,CACrCD,EAAEmN,WAAFnN,IADqC,CACTC,EAAGkN,WAAHlN,IADS,EACqB,CAC1DD,EAAE,GADwD,CACnDC,EAAGkN,WAAHlN,IADmD,EAH5D,eAMmDkN,WAAmBA,mBAA2BA,aANjG,CAbO,GAAEb,CAAAA,GAAF,CAoBqBc,CApBXd,CAAAA,GAAV,EAAkBG,CAAAA,GAAlB,CAoBqBW,CApBKb,CAAAA,GAA1B,EAAkCK,CAAAA,GAAlC,CAoBqBQ,CApBqBZ,CAAAA,GAajD,CAZO,GAAED,CAAAA,GAAF,CAmBqBa,CAnBXd,CAAAA,GAAV,EAAkBI,CAAAA,GAAlB,CAmBqBU,CAnBKb,CAAAA,GAA1B,EAAkCM,CAAAA,GAAlC,CAmBqBO,CAnBqBZ,CAAAA,GAYjD,CAXO,GAAEA,CAAAA,GAAF,CAkBqBY,CAlBXd,CAAAA,GAAV,EAAkBK,CAAAA,GAAlB,CAkBqBS,CAlBKb,CAAAA,GAA1B,EAAkCO,CAAAA,GAAlC,CAkBqBM,CAlBqBZ,CAAAA,GAWjD,CAVO,GAAEF,CAAAA,GAAF,CAiBqBc,CAjBXX,CAAAA,GAAV,EAAkBA,CAAAA,GAAlB,CAiBqBW,CAjBKV,CAAAA,GAA1B,EAAkCE,CAAAA,GAAlC,CAiBqBQ,CAjBqBT,CAAAA,GAUjD,CATO,GAAEJ,CAAAA,GAAF,CAgBqBa,CAhBXX,CAAAA,GAAV,EAAkBC,CAAAA,GAAlB,CAgBqBU,CAhBKV,CAAAA,GAA1B,EAAkCG,CAAAA,GAAlC,CAgBqBO,CAhBqBT,CAAAA,GASjD,CARO,GAAEH,CAAAA,GAAF,CAeqBY,CAfXX,CAAAA,GAAV,EAAkBE,CAAAA,GAAlB,CAeqBS,CAfKV,CAAAA,GAA1B,EAAkCI,CAAAA,GAAlC,CAeqBM,CAfqBT,CAAAA,GAQjD,CAPO,GAAEL,CAAAA,GAAF,CAcqBc,CAdXR,CAAAA,GAAV,EAAkBH,CAAAA,GAAlB,CAcqBW,CAdKP,CAAAA,GAA1B,EAAkCD,CAAAA,GAAlC,CAcqBQ,CAdqBN,CAAAA,GAOjD,CANO,GAAEP,CAAAA,GAAF,CAaqBa,CAbXR,CAAAA,GAAV,EAAkBF,CAAAA,GAAlB,CAaqBU,CAbKP,CAAAA,GAA1B,EAAkCA,CAAAA,GAAlC,CAaqBO,CAbqBN,CAAAA,GAMjD,CALO,GAAEN,CAAAA,GAAF;AAYqBY,CAZXR,CAAAA,GAAV,EAAkBD,CAAAA,GAAlB,CAYqBS,CAZKP,CAAAA,GAA1B,EAAkCC,CAAAA,GAAlC,CAYqBM,CAZqBN,CAAAA,gCAcMK,kBACrBlN,KAChC,MAAM+H,EAAwBwE,CAAxBxE,CAA8BhI,CAA9BgI,CAA4C2E,CAA5C3E,CAAkD/H,CAAlD+H,CAAgE8E,CACtE,OAAO,CACL9M,GAAcsM,CAAdtM,CAAoBA,CAApBA,CAAkCyM,CAAlCzM,CAAwCC,CAAxCD,CAAsD4M,CAAtD5M,EAA6DgI,CADxD,CAEL/H,GAAcsM,CAAdtM,CAAoBD,CAApBC,CAAkCyM,CAAlCzM,CAAwCA,CAAxCA,CAAsD4M,CAAtD5M,EAA6D+H,CAFxD,EAMT,KAAK,IAAI/H,EAAI,CAAb,CAAgBA,CAAhB,CAAoBkN,CAAS1D,CAAAA,SAA7B,CAAwCxJ,CAAA,EAAxC,CACE,IAAK,IAAID,EAAI,CAAb,CAAgBA,CAAhB,CAAoBmN,CAAS1D,CAAAA,SAA7B,CAAwCzJ,CAAA,EAAxC,CAA6C,CAG3C,kBACAwJ,EAAOtJ,CAAAA,GAAP,CAAWF,CAAX,CAAcC,CAAd,CAAiBiN,CAAMnN,CAAAA,GAAN,CAAUuD,IAAKC,CAAAA,KAAL,CAAW8J,CAAYrN,CAAAA,CAAvB,CAAV,CAAqCsD,IAAKC,CAAAA,KAAL,CAAW8J,CAAYpN,CAAAA,CAAvB,CAArC,CAAjB,CAJ2C,CAQ/C,MAAO,CACLuJ,OAAAA,CADK,CAEL8D,gBAAAA,CAFK,EC3FT,SAA0BvL,wBAA0B/B,CAAAA,GAAMA,CAAAA,eAAaC,CAAAA,GAAMA,CAAAA,KAE7EsN,SAASA,EAAG,CAACC,CAAD,EACV,MAAOA,EAAOC,CAAAA,MAAP,CAAc,CAAC3K,CAAD,CAAIf,CAAJ,CAAA,EAAUe,CAAV,CAAcf,CAA5B;AAIT2L,QAASA,GAAqB,CAACC,CAAD,CAAkBC,CAAlB,CAAmCC,CAAnC,EAE5B,UAAwCD,EAAxC,OAC0CC,EAD1C,OAE0CA,EAF1C,CAIIC,CAJJ,CAKIC,CALJ,CAMIC,CAGAC,EAAJ,EAAwBC,CAAxB,EAA0CD,CAA1C,EAA8DE,CAA9D,CACE,CAACL,CAAD,CAAaC,CAAb,CAAsBC,CAAtB,CADF,CACoC,CAACJ,CAAD,CAAWD,CAAX,CAAqBE,CAArB,CADpC,CAEWM,CAAJ,EAAwBF,CAAxB,EAA4CE,CAA5C,EAAgED,CAAhE,CACL,CAACJ,CAAD,CAAaC,CAAb,CAAsBC,CAAtB,CADK,CAC6B,CAACL,CAAD,CAAWC,CAAX,CAAqBC,CAArB,CAD7B,CAGL,CAACC,CAAD,CAAaC,CAAb,CAAsBC,CAAtB,CAHK,CAG6B,CAACL,CAAD,CAAWE,CAAX,CAAqBD,CAArB,CAMoF,EAAxH,EAAMI,CAAShO,CAAAA,CAAf,CAAmB+N,CAAQ/N,CAAAA,CAA3B,GAAiC8N,CAAW7N,CAAAA,CAA5C,CAAgD8N,CAAQ9N,CAAAA,CAAxD,GAAgE+N,CAAS/N,CAAAA,CAAzE,CAA6E8N,CAAQ9N,CAAAA,CAArF,GAA2F6N,CAAW9N,CAAAA,CAAtG,CAA0G+N,CAAQ/N,CAAAA,CAAlH,IACE,CAAC8N,CAAD,CAAaE,CAAb,CADF,CAC2B,CAACA,CAAD,CAAWF,CAAX,CAD3B,CAIA,OAAO,CAAEA,WAAAA,CAAF,CAAcC,QAAAA,CAAd,CAAuBC,SAAAA,CAAvB;AAITI,QAASA,GAAgB,CAACL,CAAD,CAAiBC,CAAjB,CAAkCF,CAAlC,CAAqDtE,CAArD,WAESsE,EAAYtE,EAAQ,MAClD+D,CAAA,CAAIc,CAAA,CAAmBN,CAAnB,CAA4BC,CAA5B,CAAsCxE,CAAtC,CAA8C,CAA9C,CAAJ,EAAwD,EACxD+D,CAAA,CAAIc,CAAA,CAAmBP,CAAnB,CAA+BC,CAA/B,CAAwCvE,CAAxC,CAAgD,CAAhD,CAAJ,EAA0D,EAC1D+D,CAAA,CAAIc,CAAA,CAAmBL,CAAnB,CAA6BD,CAA7B,CAAsCvE,CAAtC,CAA8C,CAA9C,CAAJ,EAAwD,GACtD,CAEJ,IAAiB,CAAjB,CAAI8E,CAAJ,CACE,KAAU5N,MAAJ,CAAU,qBAAV,CAAN,kBAG8CsN,uBACCF,KAC7CrE,EAAAA,CAAYnG,IAAKC,CAAAA,KAAL,EAAYgL,CAAZ,CAA2BC,CAA3B,EAA4C,CAA5C,CAAZ/E,CAA6D,CACjE,QAAQA,CAAR,CAAoB,CAApB,EACE,KAAK,CAAL,CACEA,CAAA,EACA,MACF,MAAK,CAAL,CACEA,CAAA,EALJ,CAQA,MAAO,CAAEA,UAAAA,CAAF,CAAa6E,WAAAA,CAAb;AAMTG,QAASA,EAA8B,CAACC,CAAD,CAAgBC,CAAhB,CAA4BnF,CAA5B,CAA+C5J,CAA/C,EACrC,QAA+BI,EAAEsD,UAAA,IAAA,EAAsBrD,EAAGqD,UAAA,IAAA,GAC1D,0CAMA,IAAIsL,CAAJ,CAAW,CACT,IAAAC,EAAQvL,IAAKC,CAAAA,KAAL,CAAWmL,CAAOzO,CAAAA,CAAlB,CACR,KAAA6O,EAAQxL,IAAKC,CAAAA,KAAL,CAAWmL,CAAO1O,CAAAA,CAAlB,CACR+O,EAAA,CAAMzL,IAAKC,CAAAA,KAAL,CAAWoL,CAAI1O,CAAAA,CAAf,CACN+O,EAAA,CAAM1L,IAAKC,CAAAA,KAAL,CAAWoL,CAAI3O,CAAAA,CAAf,CAJG,CAAX,IAME6O,EAGA,CAHQvL,IAAKC,CAAAA,KAAL,CAAWmL,CAAO1O,CAAAA,CAAlB,CAGR,CAFA8O,CAEA,CAFQxL,IAAKC,CAAAA,KAAL,CAAWmL,CAAOzO,CAAAA,CAAlB,CAER,CADA8O,CACA,CADMzL,IAAKC,CAAAA,KAAL,CAAWoL,CAAI3O,CAAAA,CAAf,CACN,CAAAgP,CAAA,CAAM1L,IAAKC,CAAAA,KAAL,CAAWoL,CAAI1O,CAAAA,CAAf,CAGR,oBAAA,gBAAA,CAEIqH,EAAQhE,IAAKC,CAAAA,KAAL,CAAW,CAAC0L,CAAZ,CAAiB,CAAjB,CAFZ,WAAA,WAAA,CAMIC,EAAe,CAAA,CAEnB,KAAK,IAAIlP,EAAI6O,CAAR,CAAe5O,EAAI6O,CAAxB,CAA+B9O,CAA/B,GAAqC+O,CAArC,CAA2CI,CAA3C,CAAkDnP,CAAlD,EAAuDmP,CAAvD,CAA8D,gBAM5D,IAAI3F,CAAOzJ,CAAAA,GAAP,CAAWqP,CAAX,CAAkBC,CAAlB,CAAJ,GAAiCH,CAAjC,GACEA,CAEI,CAFW,CAACA,CAEZ,CADJI,CAAaxN,CAAAA,IAAb,CAAkB,CAAC9B,EAAGoP,CAAJ,CAAWnP,EAAGoP,CAAd,CAAlB,CACI,CAAAC,CAAa1P,CAAAA,MAAb,GAAwBA,CAAxB,CAAiC,CAHvC,EAII,KAGJ0H,EAAA,EAASiI,CACT,IAAY,CAAZ,CAAIjI,CAAJ,CAAe,CACb,GAAIrH,CAAJ,GAAU+O,CAAV,CACE,KAEF/O,EAAA,EAAKuP,CACLlI,EAAA,EAAS2H,CALI,CAd6C;EAuB9D,KAASpN,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CAAoBjC,CAApB,CAA4BiC,CAAA,EAA5B,CACMyN,CAAA,CAAazN,CAAb,CAAJ,EAAuByN,CAAA,CAAazN,CAAb,CAAiB,CAAjB,CAAvB,CACE4N,CAAU3N,CAAAA,IAAV,CAAe4N,CAAA,CAASJ,CAAA,CAAazN,CAAb,CAAT,CAA0ByN,CAAA,CAAazN,CAAb,CAAiB,CAAjB,CAA1B,CAAf,CADF,CAGE4N,CAAU3N,CAAAA,IAAV,CAAe,CAAf,CAGJ,OAAO2N,GAMTpB,QAASA,EAAkB,CAACK,CAAD,CAAgBC,CAAhB,CAA4BnF,CAA5B,CAA+C5J,CAA/C,EACzB,aAAA,iBAGwD+O,EAAKnF,EAAQlG,SAAA,EAAA,EAAA,SACZ,CAAEtD,EAAE0O,GAAF1O,EAAF,CAAoBC,EAAGyO,GAAHzO,EAApB,EAAyCuJ,EAAQlG,SAAA,EAAA,EAAA,0BAG1G,OAAOqM,EAAYC,CAAAA,MAAZ,CAAmBC,CAAnB,CAAgCD,CAAAA,MAAhC,CAAuC,GAAGE,CAA1C,EAKTC,QAASA,EAAkB,CAACC,CAAD,CAAqBC,CAArB,EACzB,eAAA,CACI3I,EAAQ,CACZ2I,EAAOxK,CAAAA,OAAP,CAAe,CAACyK,CAAD,CAAQrO,CAAR,CAAA,GACbyF,CAAA,EAAShE,QAAA,CAAC0M,CAAA,CAASnO,CAAT,CAAD,CAAeqO,CAAf,CAAuBC,CAAvB,CAAuC,CAAvC,EADX,CAIA,OAAO,CAAEA,YAAAA,CAAF,CAAe7I,MAAAA,CAAf;AAMT8I,QAASA,EAAY,CAACC,CAAD,CAAeJ,CAAf,CAAiCzG,CAAjC,EACnB,GAAI,CACF,UAA8C,CAAExJ,EAAE,EAAJ,CAAQC,EAAGoQ,GAAX,EAAqB7G,EAAQyG,SAA3E,OAC4C,CAAEjQ,EAAEqQ,GAAJ,CAAapQ,EAAG,EAAhB,EAAqBuJ,EAAQyG,SADzE,QAIEjQ,EAAGsD,IAAKgN,CAAAA,GAAL,CAAS,CAAT,CAAYD,CAAMrQ,CAAAA,CAAlB,CAAsBqQ,CAAMpQ,CAAAA,CAA5B,CAAHD,CAAoC,EACpCC,EAAGqD,IAAKgN,CAAAA,GAAL,CAAS,CAAT,CAAYD,CAAMpQ,CAAAA,CAAlB,CAAsBoQ,CAAMrQ,CAAAA,CAA5B,CAAHC,CAAoC,GAE8BuJ,EAAQyG,SAP5E,QAUEjQ,EAAGsD,IAAKiN,CAAAA,GAAL,CAAS/G,CAAO9J,CAAAA,KAAhB,CAAuB2Q,CAAMrQ,CAAAA,CAA7B,CAAiCqQ,CAAMpQ,CAAAA,CAAvC,CAAHD,CAA+C,EAC/CC,EAAGqD,IAAKiN,CAAAA,GAAL,CAAS/G,CAAO7J,CAAAA,MAAhB,CAAwB0Q,CAAMpQ,CAAAA,CAA9B,CAAkCoQ,CAAMrQ,CAAAA,CAAxC,CAAHC,CAAgD,GAEqBuJ,EAAQyG,SAb/E,OAekDA,EAflD,OAgBgDA,EAhBhD,OAiB8DA,EAjB9D,OAkB4DA,EAlB5D,8DA+BA,kCAVEO,CAAUlJ,CAAAA,MAAQkJ,CAAUlJ,CAAAA,MAC5BmJ,CAAcnJ,CAAAA,MAAQmJ,CAAcnJ,CAAAA,MACpCoJ,CAAYpJ,CAAAA,MAAQoJ,CAAYpJ,CAAAA,MAQlC,6BAAA,CAHEhE,QAAA,CAACkN,CAAUL,CAAAA,WAAX,CAAyBQ,CAAzB,CAAqC,CAArC,CAGF,CAFErN,QAAA,CAACmN,CAAcN,CAAAA,WAAf,CAA6BQ,CAA7B,CAAyC,CAAzC,CAEF;AADErN,QAAA,CAACoN,CAAYP,CAAAA,WAAb,CAA2BQ,CAA3B,CAAuC,CAAvC,CACF,EAD8CA,CA/B5C,CAiCF,OAAA,CAAM,CACN,MAAOrG,SADD,EAKVsG,QAASA,EAAgB,CAACpH,CAAD,CAAoBF,CAApB,EAEvB,IADA,IAAIuH,EAAQvN,IAAKwN,CAAAA,KAAL,CAAWxH,CAAEtJ,CAAAA,CAAb,CACZ,CAAOwJ,CAAOzJ,CAAAA,GAAP,CAAW8Q,CAAX,CAAkBvN,IAAKwN,CAAAA,KAAL,CAAWxH,CAAErJ,CAAAA,CAAb,CAAlB,CAAP,CAAA,CACE4Q,CAAA,EAGF,KADA,IAAIE,EAASzN,IAAKwN,CAAAA,KAAL,CAAWxH,CAAEtJ,CAAAA,CAAb,CACb,CAAOwJ,CAAOzJ,CAAAA,GAAP,CAAWgR,CAAX,CAAmBzN,IAAKwN,CAAAA,KAAL,CAAWxH,CAAErJ,CAAAA,CAAb,CAAnB,CAAP,CAAA,CACE8Q,CAAA,YAKF,KADIC,CACJ,CADW1N,IAAKwN,CAAAA,KAAL,CAAWxH,CAAErJ,CAAAA,CAAb,CACX,CAAOuJ,CAAOzJ,CAAAA,GAAP,CAAWuD,IAAKwN,CAAAA,KAAL,CAAW9Q,CAAX,CAAX,CAA0BgR,CAA1B,CAAP,CAAA,CACEA,CAAA,EAGF,KADIC,CACJ,CADc3N,IAAKwN,CAAAA,KAAL,CAAWxH,CAAErJ,CAAAA,CAAb,CACd,CAAOuJ,CAAOzJ,CAAAA,GAAP,CAAWuD,IAAKwN,CAAAA,KAAL,CAAW9Q,CAAX,CAAX,CAA0BiR,CAA1B,CAAP,CAAA,CACEA,CAAA,EAIF,OAAO,CAAEjR,EAAAA,CAAF,CAAKC,IAAAA,EAAAA,GAAL;QAgBOiR,GAAM,CAAC1H,CAAD,EACpB,QAAA,CACI2H,EAAmC,EACvC,SACA,KAAIC,EAAsC,EAE1C,KAAK,IAAInR,EAAI,CAAb,CAAgBA,CAAhB,EAAqBuJ,CAAO7J,CAAAA,MAA5B,CAAoCM,CAAA,EAApC,CAAyC,CACvC,IAAIL,EAAS,CAAb,CACIyR,EAAU,CAAA,CACd,KAAIC,EAAQ,CAAC,CAAD,CAAI,CAAJ,CAAO,CAAP,CAAU,CAAV,CAAa,CAAb,CAEZ,KAAK,IAAItR,EAAI,CAAC,CAAd,CAAiBA,CAAjB,EAAsBwJ,CAAO9J,CAAAA,KAA7B,CAAoCM,CAAA,EAApC,CAAyC,CACvC,cAAsBC,EACtB,IAAIE,CAAJ,GAAUkR,CAAV,CACEzR,CAAA,EADF,KAEO,CACL0R,CAAA,CAAQ,CAACA,CAAA,CAAM,CAAN,CAAD,CAAWA,CAAA,CAAM,CAAN,CAAX,CAAqBA,CAAA,CAAM,CAAN,CAArB,CAA+BA,CAAA,CAAM,CAAN,CAA/B,CAAyC1R,CAAzC,CACRA,EAAA,CAAS,CACTyR,EAAA,CAAUlR,CAGV,oCAGEmD,IAAKiO,CAAAA,GAAL,CAASD,CAAA,CAAM,CAAN,CAAT,CAAoBE,CAApB,EAAqDA,GACrDlO,IAAKiO,CAAAA,GAAL,CAASD,CAAA,CAAM,CAAN,CAAT,CAAoB,CAApB,CAAwBE,CAAxB,EAAyD,EAAIA,GAC7DlO,IAAKiO,CAAAA,GAAL,CAASD,CAAA,CAAM,CAAN,CAAT,CAAoBE,CAApB,EAAqDA,GACrDlO,IAAKiO,CAAAA,GAAL,CAASD,CAAA,CAAM,CAAN,CAAT,CAAoBE,CAApB,EAAqDA,GACrD,CAACrR,CAGH,kBAAwD,4BAGtDmD,IAAKiO,CAAAA,GAAL,CAASD,CAAA,CAAM,CAAN,CAAT,CAAoBG,CAApB,EAAwDA,GACxDnO,IAAKiO,CAAAA,GAAL,CAASD,CAAA,CAAM,CAAN,CAAT,CAAoBG,CAApB,EAAwDA,GACxDtR,CAEF,IAAIuR,CAAJ,CAAwB,CAEtB,iBAAA,aAGaC,OAAAA,EAAQC,KAAAA,EAAM3R,EAAAA,qBAId4R,CAAAA;IAA6BA,CAAAA,aACvCD,GAAQ9K,CAAE+K,CAAAA,MAAOF,CAAAA,QAAUA,GAAU7K,CAAE+K,CAAAA,MAAOD,CAAAA,MAC9CD,GAAU7K,CAAE+K,CAAAA,MAAOF,CAAAA,QAAUC,GAAQ9K,CAAE+K,CAAAA,MAAOD,CAAAA,UAC5CN,CAAA,CAAM,CAAN,GAAYxK,CAAE+K,CAAAA,MAAOD,CAAAA,KAAO9K,CAAE+K,CAAAA,MAAOF,CAAAA,YACrCL,CAAA,CAAM,CAAN,GAAYxK,CAAE+K,CAAAA,MAAOD,CAAAA,KAAO9K,CAAE+K,CAAAA,MAAOF,CAAAA,QAGf,EAA3B,CAAIG,CAAclS,CAAAA,MAAlB,CACEkS,CAAA,CAAc,CAAd,CAAiBD,CAAAA,MADnB,CAC4BE,CAD5B,CAGEZ,CAAyBrP,CAAAA,IAAzB,CAA8B,CAAExB,IAAKyR,CAAP,CAAaF,OAAQE,CAArB,CAA9B,CAnBoB,CAsBxB,GAAIC,CAAJ,CAA2B,CAEzB,YAAA,aAGaL,OAAAA,EAAQ1R,EAAAA,EAAG2R,KAAAA,qBAIXC,CAAAA,mBAA6BA,CAAAA,aACvCD,GAAQ9K,CAAE+K,CAAAA,MAAOF,CAAAA,QAAUA,GAAU7K,CAAE+K,CAAAA,MAAOD,CAAAA,MAC9CD,GAAU7K,CAAE+K,CAAAA,MAAOF,CAAAA,QAAUC,GAAQ9K,CAAE+K,CAAAA,MAAOD,CAAAA,UAC5CN,CAAA,CAAM,CAAN,GAAYxK,CAAE+K,CAAAA,MAAOD,CAAAA,KAAO9K,CAAE+K,CAAAA,MAAOF,CAAAA,YACrCL,CAAA,CAAM,CAAN,GAAYxK,CAAE+K,CAAAA,MAAOD,CAAAA,KAAO9K,CAAE+K,CAAAA,MAAOF,CAAAA,QAGf,EAA3B,CAAIG,CAAclS,CAAAA,MAAlB;AACEkS,CAAA,CAAc,CAAd,CAAiBD,CAAAA,MADnB,CAC4BE,CAD5B,CAGEX,CAA4BtP,CAAAA,IAA5B,CAAiC,CAAExB,IAAKyR,CAAP,CAAaF,OAAQE,CAArB,CAAjC,CAnBuB,CA7CtB,CAJgC,CAyEzCE,CAAmBnQ,CAAAA,IAAnB,CAAwB,GAAGqP,CAAyBe,CAAAA,MAAzB,CAAgCpL,CAAA,EAAKA,CAAE+K,CAAAA,MAAO5R,CAAAA,CAAd,GAAoBA,CAApB,EAAiD,CAAjD,EAAyB6G,CAAE+K,CAAAA,MAAO5R,CAAAA,CAAlC,CAAsC6G,CAAExG,CAAAA,GAAIL,CAAAA,CAA5E,CAA3B,CACAkR,EAAA,CAA2BA,CAAyBe,CAAAA,MAAzB,CAAgCpL,CAAA,EAAKA,CAAE+K,CAAAA,MAAO5R,CAAAA,CAAd,GAAoBA,CAApD,CAE3BkS,EAAsBrQ,CAAAA,IAAtB,CAA2B,GAAGsP,CAA4Bc,CAAAA,MAA5B,CAAmCpL,CAAA,EAAKA,CAAE+K,CAAAA,MAAO5R,CAAAA,CAAd,GAAoBA,CAAvD,CAA9B,CACAmR,EAAA,CAA8BA,CAA4Bc,CAAAA,MAA5B,CAAmCpL,CAAA,EAAKA,CAAE+K,CAAAA,MAAO5R,CAAAA,CAAd,GAAoBA,CAAvD,CAlFS,CAsFzCgS,CAAmBnQ,CAAAA,IAAnB,CAAwB,GAAGqP,CAAyBe,CAAAA,MAAzB,CAAgCpL,CAAA,EAA6B,CAA7B,EAAKA,CAAE+K,CAAAA,MAAO5R,CAAAA,CAAd,CAAkB6G,CAAExG,CAAAA,GAAIL,CAAAA,CAAxD,CAA3B,CACAkS,EAAsBrQ,CAAAA,IAAtB,CAA2B,GAAGsP,CAA9B,MAUA,KAAK,KAAL,KAAA,CACmC,CAAjC,CAAIgB,CAAKP,CAAAA,MAAO5R,CAAAA,CAAhB,CAAoBmS,CAAK9R,CAAAA,GAAIL,CAAAA,CAA7B,IAQA,cAAA,WAAA,gBAAA,cAAA,GAAA,EAAA,SAAA,WAAA,EAAA,GAAA,CAAKuJ,CAAOzJ,CAAAA,GAAP,CAAWuD,IAAKwN,CAAAA,KAAL,CAAW9Q,CAAX,CAAX,CAA0BsD,IAAKwN,CAAAA,KAAL,CAAW7Q,CAAX,CAA1B,CAAL,IAQA,0BAJgDmS,8BAAuCA;EAIvF,EAAA,KAAA,SAAA,EAAA,IAD2BpS,EAAEsD,UAAA,EAAA,EAAerD,EAAGqD,UAAA,EAAA,GAAgB,EAAA,CAAI,CAAJ,CAAO,CAAP,CAAU,CAAV,CAAa,CAAb,EAAiBkG,EAChF,CAAA6I,CAA6BvQ,CAAAA,IAA7B,CAAkC,CAAEwQ,MAAAA,CAAF,CAAStS,EAAAA,CAAT,CAAYC,EAAAA,CAAZ,CAAe0B,KAAAA,CAAf,CAAlC,CARA,CARA,CAkBF,IAA0C,CAA1C,CAAI0Q,CAA6BzS,CAAAA,MAAjC,CAEE,MAAO,KAETyS,EAA6BE,CAAAA,IAA7B,CAAkC,CAACzP,CAAD,CAAIf,CAAJ,CAAA,EAAUe,CAAEwP,CAAAA,KAAZ,CAAoBvQ,CAAEuQ,CAAAA,KAAxD,MAIA,KAASzQ,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CAAoByB,IAAKiN,CAAAA,GAAL,CAAS8B,CAA6BzS,CAAAA,MAAtC,EAAA,CAApB,CAAiG,EAAEiC,CAAnG,CAAsG,YAIpG,KAAK,KAAL,KAAA,CACM2Q,CAAJ,GAAmBnC,CAAnB,EAGAoC,CAAY3Q,CAAAA,IAAZ,gCACK0Q,IACHF,MAAOE,CAAWF,CAAAA,KAAlBA,CAA2BhP,QAAA,CAACkP,CAAW7Q,CAAAA,IAAZ,CAAmB0O,CAAM1O,CAAAA,IAAzB,CAAkC,CAAlC,CAA3B2Q,CAAkEjC,CAAM1O,CAAAA,MAF1E,CAKF8Q,EAAYF,CAAAA,IAAZ,CAAiB,CAACzP,CAAD,CAAIf,CAAJ,CAAA,EAAUe,CAAEwP,CAAAA,KAAZ,CAAoBvQ,CAAEuQ,CAAAA,KAAvC,CAEAI,EAAoB5Q,CAAAA,IAApB,CAAyB,CACvB6Q,OAAQ,CAACtC,CAAD,CAAQoC,CAAA,CAAY,CAAZ,CAAR,CAAwBA,CAAA,CAAY,CAAZ,CAAxB,CADe,CAEvBH,MAAOjC,CAAMiC,CAAAA,KAAbA,CAAqBG,CAAA,CAAY,CAAZ,CAAeH,CAAAA,KAApCA,CAA4CG,CAAA,CAAY,CAAZ,CAAeH,CAAAA,KAFpC,CAAzB,CAfoG,CAoBtGI,CAAoBH,CAAAA,IAApB,CAAyB,CAACzP,CAAD,CAAIf,CAAJ,CAAA,EAAUe,CAAEwP,CAAAA,KAAZ,CAAoBvQ,CAAEuQ,CAAAA,KAA/C,CAGA,MAAM,SAAAtE,EAAU,QAAAD,EAAS,WAAAD;MACoBqE,EAAuBnE,EAAUD,EAASD,OAEnF8E,EAAJ,EACE1R,CAAOY,CAAAA,IAAP,CAAY,CACV+Q,iBAAkB,CAAE7S,EAAG4S,CAAUC,CAAAA,gBAAiB7S,CAAAA,CAAhC,CAAmCC,EAAG2S,CAAUC,CAAAA,gBAAiB5S,CAAAA,CAAjE,CADR,CAEV6N,WAAY,CAAC9N,EAAG8N,CAAW9N,CAAAA,CAAf,CAAkBC,EAAG6N,CAAW7N,CAAAA,CAAhC,CAFF,CAGVwJ,UAAWmJ,CAAUnJ,CAAAA,SAHX,CAIVsE,QAAS,CAAC/N,EAAG+N,CAAQ/N,CAAAA,CAAZ,CAAeC,EAAG8N,CAAQ9N,CAAAA,CAA1B,CAJC,CAKV+N,SAAU,CAAChO,EAAGgO,CAAShO,CAAAA,CAAb,CAAgBC,EAAG+N,CAAS/N,CAAAA,CAA5B,CALA,CAAZ,QAcyC+N,SACDD,SACGD,EAE7C,GAAA,KADqDqE,EAAuBW,EAAaC,EAAYC,EACrG,GACE9R,CAAOY,CAAAA,IAAP,CAAY,CACV+Q,iBAAkB,CAAE7S,EAAGiT,CAAkBJ,CAAAA,gBAAiB7S,CAAAA,CAAxC,CAA2CC,EAAGgT,CAAkBJ,CAAAA,gBAAiB5S,CAAAA,CAAjF,CADR,CAEV6N,WAAY,CAAE9N,EAAGgT,CAAchT,CAAAA,CAAnB,CAAsBC,EAAG+S,CAAe/S,CAAAA,CAAxC,CAFF,CAGV8N,QAAS,CAAE/N,EAAG+S,CAAW/S,CAAAA,CAAhB,CAAmBC,EAAG8S,CAAY9S,CAAAA,CAAlC,CAHC,CAIV+N,SAAU,CAAEhO,EAAG8S,CAAY9S,CAAAA,CAAjB,CAAoBC,EAAG6S,CAAa7S,CAAAA,CAApC,CAJA,CAKVwJ,UAAWwJ,CAAkBxJ,CAAAA,SALnB,CAAZ,CASF,OAAsB,EAAtB,GAAIvI,CAAOtB,CAAAA,MAAX,CACS,IADT,CAIOsB;AAGTgS,QAASA,EAAoB,CAAC1J,CAAD,CAAoB2I,CAApB,CAAmDnE,CAAnD,CAAoED,CAApE,CAAoFD,CAApF,EAG3B,IAAIrE,CAAJ,CACI6E,CACJ,IAAI,CACF,CAAC,CAAE,UAAA7E,CAAF,CAAa,WAAA6E,CAAb,CAAD,CAA6BF,EAAA,CAAiBL,CAAjB,CAA0BC,CAA1B,CAAoCF,CAApC,CAAgDtE,CAAhD,CAA7B,CADE,CAEF,MAAO2J,CAAP,CAAU,CACV,MAAO,KADG,CAMP,IAAA,EAAAnF,CAAShO,CAAAA,CAAT,CAAa+N,CAAQ/N,CAAAA,CAArB,CAAyB8N,CAAW9N,CAAAA,CAApC,CACA,EAAAgO,CAAS/N,CAAAA,CAAT,CAAa8N,CAAQ9N,CAAAA,CAArB,CAAyB6N,CAAW7N,CAAAA,SAEc6N,OAAgCE,eAEvF,QACEhO,EAAG+N,CAAQ/N,CAAAA,CAAXA,CAAeoT,CAAfpT,EAA+DA,CAA/DA,CAAmE+N,CAAQ/N,CAAAA,CAA3EA,EACAC,EAAG8N,CAAQ9N,CAAAA,CAAXA,CAAemT,CAAfnT,EAA+DA,CAA/DA,CAAmE8N,CAAQ9N,CAAAA,CAA3EA,MAICgC,CAAAA,IAAI6E,CAAA,GACH,MAAM9G,GAAK8G,CAAExG,CAAAA,GAAIqR,CAAAA,MAAX3R,CAAoB8G,CAAExG,CAAAA,GAAIsR,CAAAA,IAA1B5R,CAAiC8G,CAAE+K,CAAAA,MAAOF,CAAAA,MAA1C3R,CAAmD8G,CAAE+K,CAAAA,MAAOD,CAAAA,IAA5D5R,EAAoE,CACpEC,EAAAA,EAAK6G,CAAExG,CAAAA,GAAIL,CAAAA,CAAXA,CAAe6G,CAAE+K,CAAAA,MAAO5R,CAAAA,CAAxBA,CAA4B,CAA5BA,EAAiC,CACvC,IAAKuJ,CAAOzJ,CAAAA,GAAP,CAAWuD,IAAKC,CAAAA,KAAL,CAAWvD,CAAX,CAAX,CAA0BsD,IAAKC,CAAAA,KAAL,CAAWtD,CAAX,CAA1B,CAAL,CAAA,CAKA,IAAMqS,EADYlC,CAAAiD,CAAa,CAACrT,EAAGsD,IAAKC,CAAAA,KAAL,CAAWvD,CAAX,CAAJ,CAAmBC,EAAGqD,IAAKC,CAAAA,KAAL,CAAWtD,CAAX,CAAtB,CAAboT,CAAmD,CAAC,CAAD,CAAI,CAAJ,CAAO,CAAP,CAAnDA,CAA8D7J,CAA9D6J,CACZf,CAAoB5C,CAAA,CAAS,CAAC1P,EAAAA,CAAD,CAAIC,EAAAA,CAAJ,CAAT,CAAiBqT,CAAjB,CAC1B,OAAO,CAAEtT,EAAAA,CAAF,CAAKC,EAAAA,CAAL,CAAQqS,MAAAA,CAAR,CANP,GAQDJ,CAAAA,OAAO/R,CAAA,EAAK,CAAC,CAACA,EACdoS,CAAAA,KAAK,CAACzP,CAAD,CAAIf,CAAJ,CAAA,EAAUe,CAAEwP,CAAAA,KAAZ,CAAoBvQ,CAAEuQ,CAAAA,MAM9B,OAAO,CAAEO;kBAAF,CAAoBpJ,UAAAA,CAApB;ACzcT8J,QAASA,EAAI,CAAC/J,CAAD,EACX,WACA,IAAI,CAACgK,CAAL,CACE,MAAO,KAGT,KAAK,KAAL,KAAA,CAAgC,QACErG,iBHoSlC,IAAc,IAAd,EAAI3D,CAAJ,CACE,CAAA,CAAO,IADT,KAAA,CAGA,UACA,IAAItI,CAAJ,CACE,CAAA,CAAOA,CADT,KAAA,CAIA,IAASlB,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CAAoBwJ,CAAO9J,CAAAA,KAA3B,CAAkCM,CAAA,EAAlC,CACE,IAAK,IAAIC,EAAID,CAAJC,CAAQ,CAAjB,CAAoBA,CAApB,CAAwBuJ,CAAO7J,CAAAA,MAA/B,CAAuCM,CAAA,EAAvC,CACMuJ,CAAOzJ,CAAAA,GAAP,CAAWC,CAAX,CAAcC,CAAd,CAAJ,GAAyBuJ,CAAOzJ,CAAAA,GAAP,CAAWE,CAAX,CAAcD,CAAd,CAAzB,GACEwJ,CAAOtJ,CAAAA,GAAP,CAAWF,CAAX,CAAcC,CAAd,CAAiB,CAACuJ,CAAOzJ,CAAAA,GAAP,CAAWC,CAAX,CAAcC,CAAd,CAAlB,CACA,CAAAuJ,CAAOtJ,CAAAA,GAAP,CAAWD,CAAX,CAAcD,CAAd,CAAiB,CAACwJ,CAAOzJ,CAAAA,GAAP,CAAWE,CAAX,CAAcD,CAAd,CAAlB,CAFF,CAMJ,EAAA,CAAO0L,CAAA,CAAalC,CAAb,CAZP,CAJA,CGlSE,GAAIiK,CAAJ,CACE,MAAO,CACLC,WAAYD,CAAQ5S,CAAAA,KADf,CAELpB,KAAMgU,CAAQ7R,CAAAA,IAFT,CAGLU,OAAQmR,CAAQnR,CAAAA,MAHX,CAILD,QAASoR,CAAQpR,CAAAA,OAJZ,CAKL8K,SAAU,CACRwG,eAAgBC,CAAUtG,CAAAA,eAAV,CAA0BH,CAAS1D,CAAAA,SAAnC,CAA8C,CAA9C,CADR,CAERoK,cAAeD,CAAUtG,CAAAA,eAAV,CAA0B,CAA1B,CAA6B,CAA7B,CAFP,CAGRwG,kBAAmBF,CAAUtG,CAAAA,eAAV,CAA0BH,CAAS1D,CAAAA,SAAnC,CAA8C0D,CAAS1D,CAAAA,SAAvD,CAHX,CAIRsK,iBAAkBH,CAAUtG,CAAAA,eAAV,CAA0B,CAA1B;AAA6BH,CAAS1D,CAAAA,SAAtC,CAJV,CAMRuK,sBAAuB7G,CAASa,CAAAA,QANxB,CAORiG,qBAAsB9G,CAASY,CAAAA,OAPvB,CAQRmG,wBAAyB/G,CAASW,CAAAA,UAR1B,CAURqG,4BAA6BhH,CAAS0F,CAAAA,gBAV9B,CALL,CAiBLrJ,OAAQoK,CAAUpK,CAAAA,MAjBb,CAJqB,CAyBhC,MAAO,MAgBT,QACE4K,kBAAmB,cACnBC,iBAAkB,CAChBC,IAAK,KADW,CAEhBC,MAAO,KAFS,CAGhBC,KAAM,KAHU,CAIhBC,wBAAyB,CAAA,CAJT,EAMlBC,kBAAmB,CAAA,EAGrBC,SAASA,EAAW,CAACC,CAAD,CAAcC,CAAd,EAClBC,MAAOC,CAAAA,IAAP,CAAYF,CAAZ,CAAiBpP,CAAAA,OAAjB,CAAyBuP,CAAA,GACvBJ,CAAA,CAAOI,CAAP,CAAA,CAAcH,CAAA,CAAIG,CAAJ,EADhB;AAKFC,QAASA,EAAI,CAACxV,CAAD,CAA0BC,CAA1B,CAAyCC,CAAzC,CAAyDuV,CAAA,CAA2B,EAApF,EACX,yBACAP,EAAA,CAAYQ,CAAZ,CAAqBC,EAArB,CACAT,EAAA,CAAYQ,CAAZ,CAAqBD,CAArB,2EAI0DG,KAAAA,eAAAA,sBAAAA,UAAcF,qBAA0BA,sBAAvDzV,EAAOC,CVlElD,KAASC,CAAAA,MAAT,GAAiC,CAAjC,CAAoB0V,CAApB,CACE,KAAU5U,MAAJ,CAAU,qCAAV,CAAN,CAGF,IAAI6U,EAAe,CAGnB,IAAIb,CAAJ,CAAuB,CACrB,IAAAc,EAAkB,IAAI1V,iBAAJ,EAA2BU,CAAAA,MAA3B,CAAmC+U,CAAnC,CAAiDD,CAAjD,CAClBC,EAAA,EAAgBD,CAFK,SU2DoB5V,EAAOC,EVvDF6V,EAChD,IAAIC,CAAiBhB,CAAAA,uBAArB,CACE,IAAK,IAAIxU,EAAI,CAAb,CAAgBA,CAAhB,CUqDgDN,CVrDhD,CAA4BM,CAAA,EAA5B,CACE,IAAK,IAAID,EAAI,CAAb,CAAgBA,CAAhB,CUoDuCN,CVpDvC,CAA2BM,CAAA,EAA3B,CAAgC,CAC9B,WUmDqCN,IV/CrCgW,EAAgBxV,CAAAA,GAAhB,CAAoBF,CAApB,CAAuBC,CAAvB,CAEGwV,CAAiBnB,CAAAA,GAFpB,KAAA,CAE8BmB,CAAiBlB,CAAAA,KAF/C,OAAA;AAE2DkB,CAAiBjB,CAAAA,IAF5E,OAAA,CAEuF,GAFvF,EAE+F,CAF/F,CAL8B,CAFpC,IAaE,KAASvU,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CUyCgDN,CVzChD,CAA4BM,CAAA,EAA5B,CACE,IAASD,CAAT,CAAa,CAAb,CAAgBA,CAAhB,CUwCuCN,CVxCvC,CAA2BM,CAAA,EAA3B,EAKE,EAAA,GAAA,CUmCqCN,CVnCrC,EAAA,EAAAgW,CAAgBxV,CAAAA,GAAhB,CAAoBF,CAApB,CAAuBC,CAAvB,CACEwV,CAAiBnB,CAAAA,GADnB,KAAA,CAC6BmB,CAAiBlB,CAAAA,KAD9C,OAAA,CAC0DkB,CAAiBjB,CAAAA,IAD3E,OAAA,cUmCqC9U,iBAAOC,UVzBlD,IAAI+U,CAAJ,CAAuB,CACrB,IAAAiB,EAAoB,IAAI7V,iBAAJ,EAA2BU,CAAAA,MAA3B,CAAmC+U,CAAnC,CAAiDK,CAAjD,CACpBL,EAAA,EAAgBK,CAFK,WAI6BC,EAAqBF,EACzE,KAASG,CAAT,CAA0B,CAA1B,CAA6BA,CAA7B,CAA8CD,CAA9C,CAAmEC,CAAA,EAAnE,CACE,IAASC,CAAT,CAA6B,CAA7B,CAAgCA,CAAhC,CAAoDC,CAApD,CAA2ED,CAAA,EAA3E,CAAgG,CAC9F,IAAIxF,EAAMjG,QAAV,CACIgG,EAAM,CACV,KAAK,IAAIrQ,EAAI,CAAb,EAAA,CAAgBA,CAAhB,CAAiCA,CAAA,EAAjC,CACE,IAAK,IAAID,EAAI,CAAb,EAAA,CAAgBA,CAAhB,CAAiCA,CAAA,EAAjC,CAAsC,CACpC,qBACyD8V,IACzDvF,EAAA,CAAMjN,IAAKiN,CAAAA,GAAL,CAASA,CAAT,CAAc0F,EAAd,CACN3F,EAAA,CAAMhN,IAAKgN,CAAAA,GAAL,CAASA,CAAT,CAAc2F,EAAd,CAJ8B,CAWpCC,CAAAA,EAAW3F,CAAX2F,CAAiB5F,CAAjB4F,EAAwB,CAI5BA,EAAA,CAAU5S,IAAKiN,CAAAA,GAAL,CAAS,GAAT,KAAA,CAAc2F,CAAd,IACV,EAAI5F,CAAJ,CAAUC,CAAV,GAME2F,CAEA,CAFU3F,CAEV,CAFgB,CAEhB,CAAqB,CAArB,CAAIuF,CAAJ,EAA8C,CAA9C,CAA0BC,CAA1B,IAaE,UAJmCD,IAInC,CAHG,CAGH,CAHOK,CAAYpW,CAAAA,GAAZ,CAAgBgW,CAAhB,CAAoC,CAApC,CAAuCD,CAAvC,CAGP,CAFEK,CAAYpW,CAAAA,GAAZ,CAAgBgW,CAAhB,CAAoC,CAApC,CAAuCD,CAAvC,CAAwD,CAAxD,CAEF,EADI,CACJ,CAAIvF,CAAJ,CAAU6F,CAAV,GACEF,CADF,CACYE,CADZ,CAbF,CARF,CA0BAD;CAAYjW,CAAAA,GAAZ,CAAgB6V,CAAhB,CAAmCD,CAAnC,CAAmDI,CAAnD,CA9C8F,CAmD9FxB,CAAJ,GAGE,gCAFyDa,EAAcD,EAEvE,CADAC,CACA,EADgBD,CAChB,CAAAe,CAAA,CAAY,IAAI9W,CAAJ,CAAc+W,CAAd,CUnC6B5W,CVmC7B,CAHd,EAKE2W,CALF,CAKc9W,CAAUM,CAAAA,WAAV,CUrC6BH,CVqC7B,CUrCoCC,CVqCpC,CAGV4W,EAAAA,CAAsB,IACtBC,EAAJ,GACM9B,CAAJ,GAEE,gCADwDa,EAAcD,EACtE,CAAAiB,CAAA,CAAW,IAAIhX,CAAJ,CAAckX,CAAd,CU5C4B/W,CV4C5B,CAFb,EAIE6W,CAJF,CAIahX,CAAUM,CAAAA,WAAV,CU9C4BH,CV8C5B,CU9CmCC,CV8CnC,CALf,CASA,KAASmW,CAAT,CAA0B,CAA1B,CAA6BA,CAA7B,CAA8CD,CAA9C,CAAmEC,CAAA,EAAnE,CACE,IAASC,CAAT,CAA6B,CAA7B,CAAgCA,CAAhC,CAAoDC,CAApD,CAA2ED,CAAA,EAA3E,CAAgG,CAClD,CAAA,CAAAC,CAAA,KAAHzF,CAhJtC,EAAA,CAgJsCA,CAhJtC,EAAoB,CAAQD,CAAR,CAAcA,CAAd,EAiJiB,EAAA,CAAAuF,CAAA,KAAHtF,CAjJlC,EAAA,CAiJkCA,CAjJlC,EAAoB,CAAQD,CAAR,CAAcA,CAAd,EAkJnB/C,EAAAA,CAAM,CACV,KAASmJ,CAAT,CAAmB,CAAC,CAApB,CAAkC,CAAlC,EAAuBA,CAAvB,CAAqCA,CAAA,EAArC,CACE,IAASC,CAAT,CAAmB,CAAC,CAApB,CAAkC,CAAlC,EAAuBA,CAAvB,CAAqCA,CAAA,EAArC,CACEpJ,CAAA,EAAO4I,CAAYpW,CAAAA,GAAZ,CAAgBM,CAAhB,CAAuBqW,CAAvB,CAAgCpW,CAAhC,CAAsCqW,CAAtC,QAIX,KAASD,CAAT,CAAmB,CAAnB,EAAA,CAAsBA,CAAtB,CAA6CA,CAAA,EAA7C,CACE,IAASC,CAAT,CAAmB,CAAnB,EAAA,CAAsBA,CAAtB,CAA6CA,CAAA,EAA7C,EAKE,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,SAFiC1W,EAEjC,CADAoW,CAAUnW,CAAAA,GAAV,CAAcF,CAAd,CAAiBC,CAAjB,CAAoB2W,CAApB,EAA2BC,CAA3B,CACA,CAAIL,CAAJ,EACED,CAASrW,CAAAA,GAAT,CAAaF,CAAb,CAAgBC,CAAhB,CAAmB,EAAE2W,CAAF,EAASC,CAAT,CAAnB,CAjBwF,CAwBhG,CAAA,CADEL,CAAJ,CACS,CAAEH,UAAAA,CAAF,CAAaE,SAAAA,CAAb,CADT,CAGO,CAAEF,UAAAA,CAAF,CU7EP,MAAM,UAAAA,EAAU,SAAAE,IAGhB,EADIrV,CACJ,CADaqS,CAAA,CAAKuD,CAAA;AAAmBP,CAAnB,CAA8BF,CAAnC,CACb,GAA8C,aAA9C,GAAgBlB,CAAQf,CAAAA,iBAAxB,EAA6F,aAA7F,GAA+De,CAAQf,CAAAA,iBAAvE,GACElT,CADF,CACWqS,CAAA,CAAKuD,CAAA,CAAmBT,CAAnB,CAA+BE,CAApC,CADX,CAGA,OAAOrV,GAGR+T,CAAa8B,CAAAA,OAAb,CAAuB9B,CClGxB,KAAIb,EAAiE,YAArE,CACI4C,EAAqC,CAErC1C,IAAK,EAFgC,CAGrCC,MAAO,GAH8B,CAIrCC,KAAM,EAJ+B,CAKrCC,wBAAyB,CAAA,CALY,CAQzCwC;IAAKC,CAAAA,SAAL,CAAiBC,CAAAC,GACb,eAAA,cAIA,mBAAA,EACI,KAAK,QAAL,CAwBJ,EAAA,GAvBe3X,OAAAA,QAAAA,SAmB4B,CACvC2U,kBAAmBA,CADoB,CAEvCC,iBAAkB2C,CAFqB,EAI3C,EASCC,IAA2BI,CAAAA,WAA3B,CAAuC,CACpCC,GAjCiBA,CAgCmB,CAEpC5U,KAAM,UAF8B,CAGpCjD,KAAMyB,CAAOzB,CAAAA,IAHuB,CAKpC8X,aAAc,CACVrW,CAAOiM,CAAAA,QAAS0G,CAAAA,aADN,CAEV3S,CAAOiM,CAAAA,QAASwG,CAAAA,cAFN,CAGVzS,CAAOiM,CAAAA,QAAS2G,CAAAA,iBAHN,CAIV5S,CAAOiM,CAAAA,QAAS4G,CAAAA,gBAJN,CALsB,CAAvC,CATD,CACKkD,IAA2BI,CAAAA,WAA3B,CAAuC,CACpCC,GAzBaA,CAwBuB,CAEpC5U,KAAM,UAF8B,CAGpCjD,KAAM,IAH8B,CAAvC,CAvBG,MACJ,MAAK,kBAAL,CA8CJuX,CAAiB1C,CAAAA,GAAjB,CA7C4B7U,CA6CL,CAAA,GACvBuX,EAAiBzC,CAAAA,KAAjB,CA9C4B9U,CA8CH,CAAA,KACzBuX,EAAiBxC,CAAAA,IAAjB,CA/C4B/U,CA+CJ,CAAA,IACxBuX,EAAiBvC,CAAAA,uBAAjB,CAhD4BhV,CAgDe,CAAA,uBA/CnC;KACJ,MAAK,eAAL,CAkDJ,OAjDyBA,CAiDzB,EACI,KAAK,UAAL,CACI2U,CAAA,CAAoB,YACpB,MACJ,MAAK,QAAL,CACIA,CAAA,CAAoB,YACpB,MACJ,MAAK,MAAL,CACIA,CAAA,CAAoB,aACpB,MACJ,SACI,KAAU1T,MAAJ,CAAU,wBAAV,CAAN,CAXR,CAhDQ,KACJ,MAAK,OAAL,CAEIuW,IAAKO,CAAAA,KAAL,EAZR;"}