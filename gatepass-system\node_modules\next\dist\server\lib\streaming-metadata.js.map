{"version": 3, "sources": ["../../../src/server/lib/streaming-metadata.ts"], "sourcesContent": ["import {\n  getBotType,\n  HTML_LIMITED_BOT_UA_RE_STRING,\n} from '../../shared/lib/router/utils/is-bot'\nimport type { BaseNextRequest } from '../base-http'\n\nexport function shouldServeStreamingMetadata(\n  userAgent: string,\n  htmlLimitedBots: string | undefined\n): boolean {\n  const blockingMetadataUARegex = new RegExp(\n    htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING,\n    'i'\n  )\n  // Only block metadata for HTML-limited bots\n  if (userAgent && blockingMetadataUARegex.test(userAgent)) {\n    return false\n  }\n  return true\n}\n\n// When the request UA is a html-limited bot, we should do a dynamic render.\n// In this case, postpone state is not sent.\nexport function isHtmlBotRequest(req: {\n  headers: BaseNextRequest['headers']\n}): boolean {\n  const ua = req.headers['user-agent'] || ''\n  const botType = getBotType(ua)\n\n  return botType === 'html'\n}\n"], "names": ["isHtmlBotRequest", "shouldServeStreamingMetadata", "userAgent", "htmlLimitedBots", "blockingMetadataUARegex", "RegExp", "HTML_LIMITED_BOT_UA_RE_STRING", "test", "req", "ua", "headers", "botType", "getBotType"], "mappings": ";;;;;;;;;;;;;;;IAuBgBA,gBAAgB;eAAhBA;;IAjBAC,4BAA4B;eAA5BA;;;uBAHT;AAGA,SAASA,6BACdC,SAAiB,EACjBC,eAAmC;IAEnC,MAAMC,0BAA0B,IAAIC,OAClCF,mBAAmBG,oCAA6B,EAChD;IAEF,4CAA4C;IAC5C,IAAIJ,aAAaE,wBAAwBG,IAAI,CAACL,YAAY;QACxD,OAAO;IACT;IACA,OAAO;AACT;AAIO,SAASF,iBAAiBQ,GAEhC;IACC,MAAMC,KAAKD,IAAIE,OAAO,CAAC,aAAa,IAAI;IACxC,MAAMC,UAAUC,IAAAA,iBAAU,EAACH;IAE3B,OAAOE,YAAY;AACrB", "ignoreList": [0]}