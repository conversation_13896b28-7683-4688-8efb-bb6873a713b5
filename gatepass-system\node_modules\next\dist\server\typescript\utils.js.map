{"version": 3, "sources": ["../../../src/server/typescript/utils.ts"], "sourcesContent": ["import path from 'path'\n\nimport type tsModule from 'typescript/lib/tsserverlibrary'\ntype TypeScript = typeof import('typescript/lib/tsserverlibrary')\n\nlet ts: TypeScript\nlet info: tsModule.server.PluginCreateInfo\nlet appDirRegExp: RegExp\n\nexport function log(message: string) {\n  info.project.projectService.logger.info('[next] ' + message)\n}\n\n// This function has to be called initially.\nexport function init(opts: {\n  ts: TypeScript\n  info: tsModule.server.PluginCreateInfo\n}) {\n  const projectDir = opts.info.project.getCurrentDirectory()\n  ts = opts.ts\n  info = opts.info\n  appDirRegExp = new RegExp(\n    '^' + (projectDir + '(/src)?/app').replace(/[\\\\/]/g, '[\\\\/]')\n  )\n\n  log('Initialized Next.js TypeScript plugin: ' + projectDir)\n}\n\nexport function getTs() {\n  return ts\n}\n\nexport function getInfo() {\n  return info\n}\n\nexport function getTypeChecker() {\n  const program = info.languageService.getProgram()\n  if (!program) {\n    log('Failed to get program while while running getTypeChecker.')\n    return\n  }\n  const typeChecker = program.getTypeChecker()\n  if (!typeChecker) {\n    log('Failed to get type checker while running getTypeChecker.')\n    return\n  }\n  return typeChecker\n}\n\nexport function getSource(fileName: string) {\n  const program = info.languageService.getProgram()\n  if (!program) {\n    log('Failed to get program while running getSource for: ' + fileName)\n    return\n  }\n\n  const sourceFile = program.getSourceFile(fileName)\n  if (!sourceFile) {\n    log('Failed to get source file while running getSource for: ' + fileName)\n    return\n  }\n\n  return sourceFile\n}\n\nexport function removeStringQuotes(str: string): string {\n  return str.replace(/^['\"`]|['\"`]$/g, '')\n}\n\nexport const isPositionInsideNode = (position: number, node: tsModule.Node) => {\n  const start = node.getFullStart()\n  return start <= position && position <= node.getFullWidth() + start\n}\n\nexport const isDefaultFunctionExport = (\n  node: tsModule.Node\n): node is tsModule.FunctionDeclaration => {\n  if (ts.isFunctionDeclaration(node)) {\n    let hasExportKeyword = false\n    let hasDefaultKeyword = false\n\n    if (node.modifiers) {\n      for (const modifier of node.modifiers) {\n        if (modifier.kind === ts.SyntaxKind.ExportKeyword) {\n          hasExportKeyword = true\n        } else if (modifier.kind === ts.SyntaxKind.DefaultKeyword) {\n          hasDefaultKeyword = true\n        }\n      }\n    }\n\n    // `export default function`\n    if (hasExportKeyword && hasDefaultKeyword) {\n      return true\n    }\n  }\n  return false\n}\n\nexport const isInsideApp = (filePath: string) => {\n  return appDirRegExp.test(filePath)\n}\nexport const isAppEntryFile = (filePath: string) => {\n  return (\n    appDirRegExp.test(filePath) &&\n    /^(page|layout)\\.(mjs|js|jsx|ts|tsx)$/.test(path.basename(filePath))\n  )\n}\nexport const isPageFile = (filePath: string) => {\n  return (\n    appDirRegExp.test(filePath) &&\n    /^page\\.(mjs|js|jsx|ts|tsx)$/.test(path.basename(filePath))\n  )\n}\n\n// Check if a module is a client entry.\nexport function getEntryInfo(\n  fileName: string,\n  throwOnInvalidDirective?: boolean\n) {\n  const source = getSource(fileName)\n  if (source) {\n    let isDirective = true\n    let isClientEntry = false\n    let isServerEntry = false\n\n    ts.forEachChild(source!, (node) => {\n      if (\n        ts.isExpressionStatement(node) &&\n        ts.isStringLiteral(node.expression)\n      ) {\n        if (node.expression.text === 'use client') {\n          if (isDirective) {\n            isClientEntry = true\n          } else {\n            if (throwOnInvalidDirective) {\n              const e = {\n                messageText:\n                  'The `\"use client\"` directive must be put at the top of the file.',\n                start: node.expression.getStart(),\n                length: node.expression.getWidth(),\n              }\n              throw e\n            }\n          }\n        } else if (node.expression.text === 'use server') {\n          if (isDirective) {\n            isServerEntry = true\n          } else {\n            if (throwOnInvalidDirective) {\n              const e = {\n                messageText:\n                  'The `\"use server\"` directive must be put at the top of the file.',\n                start: node.expression.getStart(),\n                length: node.expression.getWidth(),\n              }\n              throw e\n            }\n          }\n        }\n\n        if (isClientEntry && isServerEntry) {\n          const e = {\n            messageText:\n              'Cannot use both \"use client\" and \"use server\" directives in the same file.',\n            start: node.expression.getStart(),\n            length: node.expression.getWidth(),\n          }\n          throw e\n        }\n      } else {\n        isDirective = false\n      }\n    })\n\n    return { client: isClientEntry, server: isServerEntry }\n  }\n\n  return { client: false, server: false }\n}\n"], "names": ["getEntryInfo", "getInfo", "getSource", "getTs", "getType<PERSON><PERSON>cker", "init", "isAppEntryFile", "isDefaultFunctionExport", "isInsideApp", "isPageFile", "isPositionInsideNode", "log", "removeStringQuotes", "ts", "info", "appDirRegExp", "message", "project", "projectService", "logger", "opts", "projectDir", "getCurrentDirectory", "RegExp", "replace", "program", "languageService", "getProgram", "typeC<PERSON>cker", "fileName", "sourceFile", "getSourceFile", "str", "position", "node", "start", "getFullStart", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isFunctionDeclaration", "hasExportKeyword", "hasDefaultKeyword", "modifiers", "modifier", "kind", "SyntaxKind", "ExportKeyword", "DefaultKeyword", "filePath", "test", "path", "basename", "throwOnInvalidDirective", "source", "isDirective", "isClientEntry", "isServerEntry", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "text", "e", "messageText", "getStart", "length", "getWidth", "client", "server"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IAqHgBA,YAAY;eAAZA;;IArFAC,OAAO;eAAPA;;IAkBAC,SAAS;eAATA;;IAtBAC,KAAK;eAALA;;IAQAC,cAAc;eAAdA;;IAtBAC,IAAI;eAAJA;;IAyFHC,cAAc;eAAdA;;IA5BAC,uBAAuB;eAAvBA;;IAyBAC,WAAW;eAAXA;;IASAC,UAAU;eAAVA;;IAvCAC,oBAAoB;eAApBA;;IA7DGC,GAAG;eAAHA;;IAyDAC,kBAAkB;eAAlBA;;;6DAlEC;;;;;;AAKjB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEG,SAASJ,IAAIK,OAAe;IACjCF,KAAKG,OAAO,CAACC,cAAc,CAACC,MAAM,CAACL,IAAI,CAAC,YAAYE;AACtD;AAGO,SAASX,KAAKe,IAGpB;IACC,MAAMC,aAAaD,KAAKN,IAAI,CAACG,OAAO,CAACK,mBAAmB;IACxDT,KAAKO,KAAKP,EAAE;IACZC,OAAOM,KAAKN,IAAI;IAChBC,eAAe,IAAIQ,OACjB,MAAM,AAACF,CAAAA,aAAa,aAAY,EAAGG,OAAO,CAAC,UAAU;IAGvDb,IAAI,4CAA4CU;AAClD;AAEO,SAASlB;IACd,OAAOU;AACT;AAEO,SAASZ;IACd,OAAOa;AACT;AAEO,SAASV;IACd,MAAMqB,UAAUX,KAAKY,eAAe,CAACC,UAAU;IAC/C,IAAI,CAACF,SAAS;QACZd,IAAI;QACJ;IACF;IACA,MAAMiB,cAAcH,QAAQrB,cAAc;IAC1C,IAAI,CAACwB,aAAa;QAChBjB,IAAI;QACJ;IACF;IACA,OAAOiB;AACT;AAEO,SAAS1B,UAAU2B,QAAgB;IACxC,MAAMJ,UAAUX,KAAKY,eAAe,CAACC,UAAU;IAC/C,IAAI,CAACF,SAAS;QACZd,IAAI,wDAAwDkB;QAC5D;IACF;IAEA,MAAMC,aAAaL,QAAQM,aAAa,CAACF;IACzC,IAAI,CAACC,YAAY;QACfnB,IAAI,4DAA4DkB;QAChE;IACF;IAEA,OAAOC;AACT;AAEO,SAASlB,mBAAmBoB,GAAW;IAC5C,OAAOA,IAAIR,OAAO,CAAC,kBAAkB;AACvC;AAEO,MAAMd,uBAAuB,CAACuB,UAAkBC;IACrD,MAAMC,QAAQD,KAAKE,YAAY;IAC/B,OAAOD,SAASF,YAAYA,YAAYC,KAAKG,YAAY,KAAKF;AAChE;AAEO,MAAM5B,0BAA0B,CACrC2B;IAEA,IAAIrB,GAAGyB,qBAAqB,CAACJ,OAAO;QAClC,IAAIK,mBAAmB;QACvB,IAAIC,oBAAoB;QAExB,IAAIN,KAAKO,SAAS,EAAE;YAClB,KAAK,MAAMC,YAAYR,KAAKO,SAAS,CAAE;gBACrC,IAAIC,SAASC,IAAI,KAAK9B,GAAG+B,UAAU,CAACC,aAAa,EAAE;oBACjDN,mBAAmB;gBACrB,OAAO,IAAIG,SAASC,IAAI,KAAK9B,GAAG+B,UAAU,CAACE,cAAc,EAAE;oBACzDN,oBAAoB;gBACtB;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAID,oBAAoBC,mBAAmB;YACzC,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEO,MAAMhC,cAAc,CAACuC;IAC1B,OAAOhC,aAAaiC,IAAI,CAACD;AAC3B;AACO,MAAMzC,iBAAiB,CAACyC;IAC7B,OACEhC,aAAaiC,IAAI,CAACD,aAClB,uCAAuCC,IAAI,CAACC,aAAI,CAACC,QAAQ,CAACH;AAE9D;AACO,MAAMtC,aAAa,CAACsC;IACzB,OACEhC,aAAaiC,IAAI,CAACD,aAClB,8BAA8BC,IAAI,CAACC,aAAI,CAACC,QAAQ,CAACH;AAErD;AAGO,SAAS/C,aACd6B,QAAgB,EAChBsB,uBAAiC;IAEjC,MAAMC,SAASlD,UAAU2B;IACzB,IAAIuB,QAAQ;QACV,IAAIC,cAAc;QAClB,IAAIC,gBAAgB;QACpB,IAAIC,gBAAgB;QAEpB1C,GAAG2C,YAAY,CAACJ,QAAS,CAAClB;YACxB,IACErB,GAAG4C,qBAAqB,CAACvB,SACzBrB,GAAG6C,eAAe,CAACxB,KAAKyB,UAAU,GAClC;gBACA,IAAIzB,KAAKyB,UAAU,CAACC,IAAI,KAAK,cAAc;oBACzC,IAAIP,aAAa;wBACfC,gBAAgB;oBAClB,OAAO;wBACL,IAAIH,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF3B,OAAOD,KAAKyB,UAAU,CAACI,QAAQ;gCAC/BC,QAAQ9B,KAAKyB,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF,OAAO,IAAI3B,KAAKyB,UAAU,CAACC,IAAI,KAAK,cAAc;oBAChD,IAAIP,aAAa;wBACfE,gBAAgB;oBAClB,OAAO;wBACL,IAAIJ,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF3B,OAAOD,KAAKyB,UAAU,CAACI,QAAQ;gCAC/BC,QAAQ9B,KAAKyB,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF;gBAEA,IAAIP,iBAAiBC,eAAe;oBAClC,MAAMM,IAAI;wBACRC,aACE;wBACF3B,OAAOD,KAAKyB,UAAU,CAACI,QAAQ;wBAC/BC,QAAQ9B,KAAKyB,UAAU,CAACM,QAAQ;oBAClC;oBACA,MAAMJ;gBACR;YACF,OAAO;gBACLR,cAAc;YAChB;QACF;QAEA,OAAO;YAAEa,QAAQZ;YAAea,QAAQZ;QAAc;IACxD;IAEA,OAAO;QAAEW,QAAQ;QAAOC,QAAQ;IAAM;AACxC", "ignoreList": [0]}