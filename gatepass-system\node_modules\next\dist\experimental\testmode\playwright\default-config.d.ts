import { type PlaywrightTestConfig } from '@playwright/test';
import type { NextOptionsConfig } from './next-options';
/**
 * This is the default configuration generated by <PERSON><PERSON> as of v1.43.0 with some modifications.
 *
 * - the `testMatch` property is configured to match all `*.spec.js` or `*.spec.ts` files within the `app` and `pages` directories
 * - the `use` property is configured with a baseURL matching the expected dev server endpoint (http://127.0.0.1:3000)
 * - the `webserver` property is configured to run `next dev`.
 */
export declare const defaultPlaywrightConfig: PlaywrightTestConfig<NextOptionsConfig>;
