{"version": 3, "sources": ["../../../src/server/app-render/flight-render-result.ts"], "sourcesContent": ["import { RSC_CONTENT_TYPE_HEADER } from '../../client/components/app-router-headers'\nimport RenderResult, { type RenderResultMetadata } from '../render-result'\n\n/**\n * Flight Response is always set to RSC_CONTENT_TYPE_HEADER to ensure it does not get interpreted as HTML.\n */\nexport class FlightRenderResult extends RenderResult {\n  constructor(\n    response: string | ReadableStream<Uint8Array>,\n    metadata: RenderResultMetadata = {}\n  ) {\n    super(response, { contentType: RSC_CONTENT_TYPE_HEADER, metadata })\n  }\n}\n"], "names": ["FlightRenderResult", "RenderResult", "constructor", "response", "metadata", "contentType", "RSC_CONTENT_TYPE_HEADER"], "mappings": ";;;;+BAMaA;;;eAAAA;;;kCAN2B;qEACgB;;;;;;AAKjD,MAAMA,2BAA2BC,qBAAY;IAClDC,YACEC,QAA6C,EAC7CC,WAAiC,CAAC,CAAC,CACnC;QACA,KAAK,CAACD,UAAU;YAAEE,aAAaC,yCAAuB;YAAEF;QAAS;IACnE;AACF", "ignoreList": [0]}