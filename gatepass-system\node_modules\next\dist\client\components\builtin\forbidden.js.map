{"version": 3, "sources": ["../../../../src/client/components/builtin/forbidden.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from '../http-access-fallback/error-fallback'\n\nexport default function Forbidden() {\n  return (\n    <HTTPAccessErrorFallback\n      status={403}\n      message=\"This page could not be accessed.\"\n    />\n  )\n}\n"], "names": ["Forbidden", "HTTPAccessErrorFallback", "status", "message"], "mappings": ";;;;+BAEA;;;eAAwBA;;;;+BAFgB;AAEzB,SAASA;IACtB,qBACE,qBAACC,sCAAuB;QACtBC,QAAQ;QACRC,SAAQ;;AAGd", "ignoreList": [0]}