import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';
import QRCode from 'qrcode';

const prisma = new PrismaClient();

function verifyToken(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('No token provided');
  }

  const token = authHeader.substring(7);
  return jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const decoded = verifyToken(request);
    const resolvedParams = await params;
    const gatepassId = resolvedParams.id;

    // Get gatepass details
    const gatepass = await prisma.gatepass.findUnique({
      where: { id: gatepassId },
      include: {
        student: {
          include: {
            college: true,
            department: true,
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!gatepass) {
      return NextResponse.json(
        { error: 'Gatepass not found' },
        { status: 404 }
      );
    }

    // Update printed timestamp
    await prisma.gatepass.update({
      where: { id: gatepassId },
      data: { printedAt: new Date() },
    });

    // Generate QR code
    const qrCodeDataURL = await QRCode.toDataURL(gatepassId, {
      width: 200,
      margin: 2,
    });

    // Generate HTML for printing
    const html = generateGatepassHTML(gatepass, qrCodeDataURL);

    // Return HTML response for printing
    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
        'Content-Disposition': `inline; filename="gatepass-${gatepassId}.html"`,
      },
    });
  } catch (error) {
    console.error('Error generating print:', error);
    if (error instanceof Error && error.message === 'No token provided') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function generateGatepassHTML(gatepass: any, qrCodeDataURL: string): string {
  const statusColor = gatepass.status === 'APPROVED' ? '#10B981' : 
                     gatepass.status === 'REJECTED' ? '#EF4444' : '#F59E0B';
  
  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Hospital Admission Gatepass</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 16px;
            color: #666;
        }
        .gatepass-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-section {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .section-title {
            font-weight: bold;
            font-size: 16px;
            color: #333;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .info-row {
            display: flex;
            margin-bottom: 8px;
        }
        .label {
            font-weight: bold;
            min-width: 120px;
            color: #555;
        }
        .value {
            color: #333;
        }
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 18px;
            color: white;
            background-color: ${statusColor};
            margin: 20px 0;
        }
        .footer {
            margin-top: 40px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        .print-btn {
            background-color: #3B82F6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
        }
        .print-btn:hover {
            background-color: #2563EB;
        }
    </style>
</head>
<body>
    <div class="no-print">
        <button class="print-btn" onclick="window.print()">Print Gatepass</button>
    </div>
    
    <div class="header">
        <div class="title">HOSPITAL ADMISSION GATEPASS</div>
        <div class="subtitle">Excel Group of Institutions</div>
    </div>

    <div class="status">
        STATUS: ${gatepass.status}
    </div>

    <div style="text-align: center; margin: 20px 0;">
        <img src="${qrCodeDataURL}" alt="QR Code" style="width: 150px; height: 150px;" />
        <p style="margin-top: 10px; font-weight: bold; font-size: 18px;">${gatepass.student.name}</p>
        <p style="margin-top: 5px; color: #666;">Scan QR code for verification</p>
    </div>

    <div class="gatepass-info">
        <div class="info-section">
            <div class="section-title">Student Information</div>
            <div class="info-row">
                <span class="label">Name:</span>
                <span class="value">${gatepass.student.name}</span>
            </div>
            <div class="info-row">
                <span class="label">Mobile:</span>
                <span class="value">${gatepass.student.mobileNumber}</span>
            </div>
            <div class="info-row">
                <span class="label">Address:</span>
                <span class="value">${gatepass.student.address}</span>
            </div>
            <div class="info-row">
                <span class="label">Father Name:</span>
                <span class="value">${gatepass.student.fatherName}</span>
            </div>
            <div class="info-row">
                <span class="label">Father Mobile:</span>
                <span class="value">${gatepass.student.fatherMobile}</span>
            </div>
        </div>

        <div class="info-section">
            <div class="section-title">Academic Information</div>
            <div class="info-row">
                <span class="label">College:</span>
                <span class="value">${gatepass.student.college.name}</span>
            </div>
            ${gatepass.student.department ? `
            <div class="info-row">
                <span class="label">Department:</span>
                <span class="value">${gatepass.student.department.name}</span>
            </div>
            ` : ''}
            <div class="info-row">
                <span class="label">Course:</span>
                <span class="value">${gatepass.student.interestedCourse}</span>
            </div>
        </div>
    </div>

    <div class="gatepass-info">
        <div class="info-section">
            <div class="section-title">Gatepass Details</div>
            <div class="info-row">
                <span class="label">Purpose:</span>
                <span class="value">${gatepass.purpose}</span>
            </div>
            <div class="info-row">
                <span class="label">Valid From:</span>
                <span class="value">${new Date(gatepass.validFrom).toLocaleDateString()}</span>
            </div>
            <div class="info-row">
                <span class="label">Valid Until:</span>
                <span class="value">${new Date(gatepass.validUntil).toLocaleDateString()}</span>
            </div>
            <div class="info-row">
                <span class="label">Gatepass ID:</span>
                <span class="value">${gatepass.id}</span>
            </div>
        </div>

        <div class="info-section">
            <div class="section-title">Authorization</div>
            <div class="info-row">
                <span class="label">Created By:</span>
                <span class="value">${gatepass.user.name}</span>
            </div>
            <div class="info-row">
                <span class="label">Created On:</span>
                <span class="value">${new Date(gatepass.createdAt).toLocaleDateString()}</span>
            </div>
            ${gatepass.verifiedAt ? `
            <div class="info-row">
                <span class="label">Verified On:</span>
                <span class="value">${new Date(gatepass.verifiedAt).toLocaleDateString()}</span>
            </div>
            ` : ''}
        </div>
    </div>

    <div class="footer">
        <p>This is a computer-generated gatepass. Please present this at the hospital admission desk.</p>
        <p>For any queries, contact the college administration.</p>
        <p>Generated on: ${new Date().toLocaleString()}</p>
    </div>

    <script>
        // Auto-detect and use default printer
        window.addEventListener('load', function() {
            // This will trigger the browser's print dialog
            // The browser will auto-detect available printers
        });
    </script>
</body>
</html>
  `;
}
