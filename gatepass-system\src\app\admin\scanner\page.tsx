'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface Gatepass {
  id: string;
  status: string;
  purpose: string;
  validFrom: string;
  validUntil: string;
  createdAt: string;
  student: {
    id: string;
    name: string;
    address: string;
    mobileNumber: string;
    fatherName: string;
    fatherMobile: string;
    interestedCourse: string;
    college: {
      name: string;
    };
    department?: {
      name: string;
    };
  };
  user: {
    name: string;
  };
}

export default function QRScanner() {
  const [isScanning, setIsScanning] = useState(false);
  const [scannedData, setScannedData] = useState<Gatepass | null>(null);
  const [error, setError] = useState('');
  const [manualId, setManualId] = useState('');
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const router = useRouter();

  const startScanning = async () => {
    try {
      setError('');
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setIsScanning(true);
        
        // Start scanning for QR codes
        scanQRCode();
      }
    } catch (err) {
      setError('Camera access denied or not available');
    }
  };

  const stopScanning = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsScanning(false);
  };

  const scanQRCode = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    const scan = () => {
      if (video.readyState === video.HAVE_ENOUGH_DATA) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        
        // Simple QR detection (in a real app, you'd use a proper QR library)
        // For now, we'll use manual input as fallback
      }

      if (isScanning) {
        requestAnimationFrame(scan);
      }
    };

    scan();
  };

  const fetchGatepassDetails = async (gatepassId: string) => {
    try {
      setError('');
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/gatepass/${gatepassId}`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        setScannedData(data);
        stopScanning();
      } else {
        setError('Gatepass not found or invalid');
      }
    } catch (error) {
      setError('Error fetching gatepass details');
    }
  };

  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (manualId.trim()) {
      fetchGatepassDetails(manualId.trim());
    }
  };

  const handlePrintDetails = () => {
    if (!scannedData) return;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const html = generateStudentDetailsHTML(scannedData);
      printWindow.document.write(html);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
      }, 1000);
    }
  };

  const generateStudentDetailsHTML = (gatepass: Gatepass): string => {
    const statusColor = gatepass.status === 'APPROVED' ? '#10B981' : 
                       gatepass.status === 'REJECTED' ? '#EF4444' : '#F59E0B';
    
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Student Details</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .status { 
          background-color: ${statusColor}; 
          color: white; 
          padding: 10px; 
          text-align: center; 
          font-weight: bold; 
          margin: 20px 0; 
        }
        .details { margin: 20px 0; }
        .row { margin: 10px 0; }
        .label { font-weight: bold; display: inline-block; width: 150px; }
    </style>
</head>
<body>
    <div class="header">
        <h2>Student Gatepass Details</h2>
        <p>Excel Group of Institutions</p>
    </div>
    
    <div class="status">STATUS: ${gatepass.status}</div>
    
    <div class="details">
        <div class="row"><span class="label">Student Name:</span> ${gatepass.student.name}</div>
        <div class="row"><span class="label">Mobile:</span> ${gatepass.student.mobileNumber}</div>
        <div class="row"><span class="label">Address:</span> ${gatepass.student.address}</div>
        <div class="row"><span class="label">Father Name:</span> ${gatepass.student.fatherName}</div>
        <div class="row"><span class="label">Father Mobile:</span> ${gatepass.student.fatherMobile}</div>
        <div class="row"><span class="label">College:</span> ${gatepass.student.college.name}</div>
        ${gatepass.student.department ? `<div class="row"><span class="label">Department:</span> ${gatepass.student.department.name}</div>` : ''}
        <div class="row"><span class="label">Course:</span> ${gatepass.student.interestedCourse}</div>
        <div class="row"><span class="label">Purpose:</span> ${gatepass.purpose}</div>
        <div class="row"><span class="label">Valid Until:</span> ${new Date(gatepass.validUntil).toLocaleDateString()}</div>
        <div class="row"><span class="label">Created By:</span> ${gatepass.user.name}</div>
        <div class="row"><span class="label">Gatepass ID:</span> ${gatepass.id}</div>
    </div>
    
    <script>window.print();</script>
</body>
</html>`;
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/');
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 inline-flex text-xs leading-5 font-semibold rounded-full";
    switch (status) {
      case 'PENDING':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'APPROVED':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'REJECTED':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">QR Code Scanner</h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/admin/dashboard')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Dashboard
              </button>
              <button
                onClick={() => router.push('/admin/verify')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Verify Gatepasses
              </button>
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">{error}</div>
        )}

        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Scan QR Code or Enter Gatepass ID</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Camera Scanner */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Camera Scanner</h3>
              <div className="space-y-4">
                {!isScanning ? (
                  <button
                    onClick={startScanning}
                    className="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Start Camera Scanner
                  </button>
                ) : (
                  <button
                    onClick={stopScanning}
                    className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Stop Scanner
                  </button>
                )}
                
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  className={`w-full h-64 bg-gray-200 rounded ${isScanning ? 'block' : 'hidden'}`}
                />
                <canvas ref={canvasRef} className="hidden" />
              </div>
            </div>

            {/* Manual Input */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Manual Entry</h3>
              <form onSubmit={handleManualSubmit} className="space-y-4">
                <div>
                  <label htmlFor="manualId" className="block text-sm font-medium text-gray-700">
                    Gatepass ID
                  </label>
                  <input
                    type="text"
                    id="manualId"
                    value={manualId}
                    onChange={(e) => setManualId(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-black"
                    placeholder="Enter Gatepass ID"
                  />
                </div>
                <button
                  type="submit"
                  className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Get Details
                </button>
                <button
                  type="button"
                  onClick={() => fetchGatepassDetails('e711233f-2459-4139-9a7c-432d8f6c9d48')}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Test with Sample ID
                </button>
              </form>
            </div>
          </div>
        </div>

        {/* Scanned Data Display */}
        {scannedData && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Student Details</h3>
              <button
                onClick={handlePrintDetails}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Print Details
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Student Information</h4>
                <div className="space-y-2 text-sm">
                  <div><span className="font-medium">Name:</span> {scannedData.student.name}</div>
                  <div><span className="font-medium">Mobile:</span> {scannedData.student.mobileNumber}</div>
                  <div><span className="font-medium">Address:</span> {scannedData.student.address}</div>
                  <div><span className="font-medium">Father:</span> {scannedData.student.fatherName}</div>
                  <div><span className="font-medium">Father Mobile:</span> {scannedData.student.fatherMobile}</div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Academic & Gatepass Info</h4>
                <div className="space-y-2 text-sm">
                  <div><span className="font-medium">College:</span> {scannedData.student.college.name}</div>
                  {scannedData.student.department && (
                    <div><span className="font-medium">Department:</span> {scannedData.student.department.name}</div>
                  )}
                  <div><span className="font-medium">Course:</span> {scannedData.student.interestedCourse}</div>
                  <div><span className="font-medium">Purpose:</span> {scannedData.purpose}</div>
                  <div><span className="font-medium">Valid Until:</span> {new Date(scannedData.validUntil).toLocaleDateString()}</div>
                  <div>
                    <span className="font-medium">Status:</span> 
                    <span className={`ml-2 ${getStatusBadge(scannedData.status)}`}>
                      {scannedData.status}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
