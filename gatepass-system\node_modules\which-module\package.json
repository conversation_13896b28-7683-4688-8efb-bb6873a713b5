{"name": "which-module", "version": "2.0.1", "description": "Find the module object for something that was require()d", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/nexdrew/which-module.git"}, "keywords": ["which", "module", "exports", "filename", "require", "reverse", "lookup"], "author": "nexdrew", "license": "ISC", "bugs": {"url": "https://github.com/nexdrew/which-module/issues"}, "homepage": "https://github.com/nexdrew/which-module#readme", "devDependencies": {"ava": "^2.0.0", "coveralls": "^3.0.3", "nyc": "^14.0.0", "standard": "^14.0.0", "standard-version": "^7.0.0"}}