{"version": 3, "sources": ["../../../../src/server/route-matcher-providers/dev/file-cache-route-matcher-provider.ts"], "sourcesContent": ["import type { RouteMatcher } from '../../route-matchers/route-matcher'\nimport { CachedRouteMatcherProvider } from '../helpers/cached-route-matcher-provider'\nimport type { FileReader } from './helpers/file-reader/file-reader'\n\n/**\n * This will memoize the matchers when the file contents are the same.\n */\nexport abstract class FileCacheRouteMatcherProvider<\n  M extends RouteMatcher = RouteMatcher,\n> extends CachedRouteMatcherProvider<M, ReadonlyArray<string>> {\n  constructor(dir: string, reader: FileReader) {\n    super({\n      load: async () => reader.read(dir),\n      compare: (left, right) => {\n        if (left.length !== right.length) return false\n\n        // Assuming the file traversal order is deterministic...\n        for (let i = 0; i < left.length; i++) {\n          if (left[i] !== right[i]) return false\n        }\n\n        return true\n      },\n    })\n  }\n}\n"], "names": ["FileCacheRouteMatcherProvider", "CachedRouteMatcherProvider", "constructor", "dir", "reader", "load", "read", "compare", "left", "right", "length", "i"], "mappings": ";;;;+BAOsBA;;;eAAAA;;;4CANqB;AAMpC,MAAeA,sCAEZC,sDAA0B;IAClCC,YAAYC,GAAW,EAAEC,MAAkB,CAAE;QAC3C,KAAK,CAAC;YACJC,MAAM,UAAYD,OAAOE,IAAI,CAACH;YAC9BI,SAAS,CAACC,MAAMC;gBACd,IAAID,KAAKE,MAAM,KAAKD,MAAMC,MAAM,EAAE,OAAO;gBAEzC,wDAAwD;gBACxD,IAAK,IAAIC,IAAI,GAAGA,IAAIH,KAAKE,MAAM,EAAEC,IAAK;oBACpC,IAAIH,IAAI,CAACG,EAAE,KAAKF,KAAK,CAACE,EAAE,EAAE,OAAO;gBACnC;gBAEA,OAAO;YACT;QACF;IACF;AACF", "ignoreList": [0]}