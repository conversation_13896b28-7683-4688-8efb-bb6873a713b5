{"version": 3, "sources": ["../../../src/server/app-render/strip-flight-headers.ts"], "sourcesContent": ["import type { IncomingHttpHeaders } from 'node:http'\n\nimport { FLIGHT_HEADERS } from '../../client/components/app-router-headers'\n\n/**\n * Removes the flight headers from the request.\n *\n * @param req the request to strip the headers from\n */\nexport function stripFlightHeaders(headers: IncomingHttpHeaders) {\n  for (const header of FLIGHT_HEADERS) {\n    delete headers[header.toLowerCase()]\n  }\n}\n"], "names": ["FLIGHT_HEADERS", "stripFlightHeaders", "headers", "header", "toLowerCase"], "mappings": "AAEA,SAASA,cAAc,QAAQ,6CAA4C;AAE3E;;;;CAIC,GACD,OAAO,SAASC,mBAAmBC,OAA4B;IAC7D,KAAK,MAAMC,UAAUH,eAAgB;QACnC,OAAOE,OAAO,CAACC,OAAOC,WAAW,GAAG;IACtC;AACF", "ignoreList": [0]}