{"name": "gatepass-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.12.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/qrcode": "^1.5.5", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "next": "15.4.1", "prisma": "^6.12.0", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0", "tsx": "^4.20.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}