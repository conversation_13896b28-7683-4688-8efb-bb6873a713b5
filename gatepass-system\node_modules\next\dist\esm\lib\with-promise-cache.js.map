{"version": 3, "sources": ["../../src/lib/with-promise-cache.ts"], "sourcesContent": ["import type { LRUCache } from '../server/lib/lru-cache'\n\n/* eslint-disable no-redeclare */\nexport function withPromiseCache<K, V>(\n  cache: LRUCache<Promise<V>>,\n  fn: (value: K) => Promise<V>\n): (value: K) => Promise<V>\nexport function withPromiseCache<T extends any[], K, V>(\n  cache: LRUCache<Promise<V>>,\n  fn: (...values: T) => Promise<V>,\n  getKey: (...values: T) => K\n): (...values: T) => Promise<V>\nexport function withPromiseCache<T extends any[], K, V>(\n  cache: LRUCache<Promise<V>>,\n  fn: (...values: T) => Promise<V>,\n  getKey?: (...values: T) => K\n): (...values: T) => Promise<V> {\n  return (...values: T) => {\n    const key = getKey ? getKey(...values) : values[0]\n    let p = cache.get(key)\n    if (!p) {\n      p = fn(...values)\n      p.catch(() => cache.remove(key))\n      cache.set(key, p)\n    }\n    return p\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cache", "fn", "<PERSON><PERSON><PERSON>", "values", "key", "p", "get", "catch", "remove", "set"], "mappings": "AAYA,OAAO,SAASA,iBACdC,KAA2B,EAC3BC,EAAgC,EAChCC,MAA4B;IAE5B,OAAO,CAAC,GAAGC;QACT,MAAMC,MAAMF,SAASA,UAAUC,UAAUA,MAAM,CAAC,EAAE;QAClD,IAAIE,IAAIL,MAAMM,GAAG,CAACF;QAClB,IAAI,CAACC,GAAG;YACNA,IAAIJ,MAAME;YACVE,EAAEE,KAAK,CAAC,IAAMP,MAAMQ,MAAM,CAACJ;YAC3BJ,MAAMS,GAAG,CAACL,KAAKC;QACjB;QACA,OAAOA;IACT;AACF", "ignoreList": [0]}