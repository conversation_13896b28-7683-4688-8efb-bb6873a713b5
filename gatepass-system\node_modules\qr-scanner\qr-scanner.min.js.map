{"version": 3, "file": "qr-scanner.min.js", "sources": ["src/qr-scanner.ts"], "sourcesContent": ["class QrScanner {\n    static readonly DEFAULT_CANVAS_SIZE = 400;\n    static readonly NO_QR_CODE_FOUND = 'No QR code found';\n    private static _disableBarcodeDetector = false;\n    private static _workerMessageId = 0;\n\n    /** @deprecated */\n    static set WORKER_PATH(workerPath: string) {\n        console.warn('Setting QrScanner.WORKER_PATH is not required and not supported anymore. '\n            + 'Have a look at the README for new setup instructions.');\n    }\n\n    static async hasCamera(): Promise<boolean> {\n        try {\n            return !!(await QrScanner.listCameras(false)).length;\n        } catch (e) {\n            return false;\n        }\n    }\n\n    static async listCameras(requestLabels = false): Promise<Array<QrScanner.Camera>> {\n        if (!navigator.mediaDevices) return [];\n\n        const enumerateCameras = async (): Promise<Array<MediaDeviceInfo>> =>\n            (await navigator.mediaDevices.enumerateDevices()).filter((device) => device.kind === 'videoinput');\n\n        // Note that enumerateDevices can always be called and does not prompt the user for permission.\n        // However, enumerateDevices only includes device labels if served via https and an active media stream exists\n        // or permission to access the camera was given. Therefore, if we're not getting labels but labels are requested\n        // ask for camera permission by opening a stream.\n        let openedStream: MediaStream | undefined;\n        try {\n            if (requestLabels && (await enumerateCameras()).every((camera) => !camera.label)) {\n                openedStream = await navigator.mediaDevices.getUserMedia({ audio: false, video: true });\n            }\n        } catch (e) {\n            // Fail gracefully, especially if the device has no camera or on mobile when the camera is already in use\n            // and some browsers disallow a second stream.\n        }\n\n        try {\n            return (await enumerateCameras()).map((camera, i) => ({\n                id: camera.deviceId,\n                label: camera.label || (i === 0 ? 'Default Camera' : `Camera ${i + 1}`),\n            }));\n        } finally {\n            // close the stream we just opened for getting camera access for listing the device labels\n            if (openedStream) {\n                console.warn('Call listCameras after successfully starting a QR scanner to avoid creating '\n                    + 'a temporary video stream');\n                QrScanner._stopVideoStream(openedStream);\n            }\n        }\n    }\n\n    readonly $video: HTMLVideoElement;\n    readonly $canvas: HTMLCanvasElement;\n    readonly $overlay?: HTMLDivElement;\n    private readonly $codeOutlineHighlight?: SVGSVGElement;\n    private readonly _onDecode?: (result: QrScanner.ScanResult) => void;\n    private readonly _legacyOnDecode?: (result: string) => void;\n    private readonly _legacyCanvasSize: number = QrScanner.DEFAULT_CANVAS_SIZE;\n    private _preferredCamera: QrScanner.FacingMode | QrScanner.DeviceId = 'environment';\n    private readonly _maxScansPerSecond: number = 25;\n    private _lastScanTimestamp: number = -1;\n    private _scanRegion: QrScanner.ScanRegion;\n    private _codeOutlineHighlightRemovalTimeout?: number;\n    private _qrEnginePromise: Promise<Worker | BarcodeDetector>\n    private _active: boolean = false;\n    private _paused: boolean = false;\n    private _flashOn: boolean = false;\n    private _destroyed: boolean = false;\n\n    constructor(\n        video: HTMLVideoElement,\n        onDecode: (result: QrScanner.ScanResult) => void,\n        options: {\n            onDecodeError?: (error: Error | string) => void,\n            calculateScanRegion?: (video: HTMLVideoElement) => QrScanner.ScanRegion,\n            preferredCamera?: QrScanner.FacingMode | QrScanner.DeviceId,\n            maxScansPerSecond?: number;\n            highlightScanRegion?: boolean,\n            highlightCodeOutline?: boolean,\n            overlay?: HTMLDivElement,\n            /** just a temporary flag until we switch entirely to the new api */\n            returnDetailedScanResult?: true,\n        },\n    );\n    /** @deprecated */\n    constructor(\n        video: HTMLVideoElement,\n        onDecode: (result: string) => void,\n        onDecodeError?: (error: Error | string) => void,\n        calculateScanRegion?: (video: HTMLVideoElement) => QrScanner.ScanRegion,\n        preferredCamera?: QrScanner.FacingMode | QrScanner.DeviceId,\n    );\n    /** @deprecated */\n    constructor(\n        video: HTMLVideoElement,\n        onDecode: (result: string) => void,\n        onDecodeError?: (error: Error | string) => void,\n        canvasSize?: number,\n        preferredCamera?: QrScanner.FacingMode | QrScanner.DeviceId,\n    );\n    /** @deprecated */\n    constructor(video: HTMLVideoElement, onDecode: (result: string) => void, canvasSize?: number);\n    constructor(\n        video: HTMLVideoElement,\n        onDecode: ((result: QrScanner.ScanResult) => void) | ((result: string) => void),\n        canvasSizeOrOnDecodeErrorOrOptions?: number | ((error: Error | string) => void) | {\n            onDecodeError?: (error: Error | string) => void,\n            calculateScanRegion?: (video: HTMLVideoElement) => QrScanner.ScanRegion,\n            preferredCamera?: QrScanner.FacingMode | QrScanner.DeviceId,\n            maxScansPerSecond?: number;\n            highlightScanRegion?: boolean,\n            highlightCodeOutline?: boolean,\n            overlay?: HTMLDivElement,\n            /** just a temporary flag until we switch entirely to the new api */\n            returnDetailedScanResult?: true,\n        },\n        canvasSizeOrCalculateScanRegion?: number | ((video: HTMLVideoElement) => QrScanner.ScanRegion),\n        preferredCamera?: QrScanner.FacingMode | QrScanner.DeviceId,\n    ) {\n        this.$video = video;\n        this.$canvas = document.createElement('canvas');\n\n        if (canvasSizeOrOnDecodeErrorOrOptions && typeof canvasSizeOrOnDecodeErrorOrOptions === 'object') {\n            // we got an options object using the new api\n            this._onDecode = onDecode as QrScanner['_onDecode'];\n        } else {\n            if (canvasSizeOrOnDecodeErrorOrOptions || canvasSizeOrCalculateScanRegion || preferredCamera) {\n                console.warn('You\\'re using a deprecated version of the QrScanner constructor which will be removed in '\n                    + 'the future');\n            } else {\n                // Only video and onDecode were specified and we can't distinguish between new or old api usage. For\n                // backwards compatibility we have to assume the old api for now. The options object is marked as non-\n                // optional in the parameter list above to make clear that ScanResult instead of string is only passed\n                // if an options object was provided. However, in the future once legacy support is removed, the options\n                // object should become optional.\n                console.warn('Note that the type of the scan result passed to onDecode will change in the future. '\n                    + 'To already switch to the new api today, you can pass returnDetailedScanResult: true.');\n            }\n            this._legacyOnDecode = onDecode as QrScanner['_legacyOnDecode'];\n        }\n\n        const options = typeof canvasSizeOrOnDecodeErrorOrOptions === 'object'\n            ? canvasSizeOrOnDecodeErrorOrOptions\n            : {};\n        this._onDecodeError = options.onDecodeError || (typeof canvasSizeOrOnDecodeErrorOrOptions === 'function'\n            ? canvasSizeOrOnDecodeErrorOrOptions\n            : this._onDecodeError);\n        this._calculateScanRegion = options.calculateScanRegion || (typeof canvasSizeOrCalculateScanRegion==='function'\n            ? canvasSizeOrCalculateScanRegion\n            : this._calculateScanRegion);\n        this._preferredCamera = options.preferredCamera || preferredCamera || this._preferredCamera;\n        this._legacyCanvasSize = typeof canvasSizeOrOnDecodeErrorOrOptions === 'number'\n            ? canvasSizeOrOnDecodeErrorOrOptions\n            : typeof canvasSizeOrCalculateScanRegion === 'number'\n                ? canvasSizeOrCalculateScanRegion\n                : this._legacyCanvasSize;\n        this._maxScansPerSecond = options.maxScansPerSecond || this._maxScansPerSecond;\n\n        this._onPlay = this._onPlay.bind(this);\n        this._onLoadedMetaData = this._onLoadedMetaData.bind(this);\n        this._onVisibilityChange = this._onVisibilityChange.bind(this);\n        this._updateOverlay = this._updateOverlay.bind(this);\n\n        // @ts-ignore\n        video.disablePictureInPicture = true;\n        // Allow inline playback on iPhone instead of requiring full screen playback,\n        // see https://webkit.org/blog/6784/new-video-policies-for-ios/\n        // @ts-ignore\n        video.playsInline = true;\n        // Allow play() on iPhone without requiring a user gesture. Should not really be needed as camera stream\n        // includes no audio, but just to be safe.\n        video.muted = true;\n\n        // Avoid Safari stopping the video stream on a hidden video.\n        // See https://github.com/cozmo/jsQR/issues/185\n        let shouldHideVideo = false;\n        if (video.hidden) {\n            video.hidden = false;\n            shouldHideVideo = true;\n        }\n        if (!document.body.contains(video)) {\n            document.body.appendChild(video);\n            shouldHideVideo = true;\n        }\n        const videoContainer = video.parentElement!;\n\n        if (options.highlightScanRegion || options.highlightCodeOutline) {\n            const gotExternalOverlay = !!options.overlay;\n            this.$overlay = options.overlay || document.createElement('div');\n            const overlayStyle = this.$overlay.style;\n            overlayStyle.position = 'absolute';\n            overlayStyle.display = 'none';\n            overlayStyle.pointerEvents = 'none';\n            this.$overlay.classList.add('scan-region-highlight');\n            if (!gotExternalOverlay && options.highlightScanRegion) {\n                // default style; can be overwritten via css, e.g. by changing the svg's stroke color, hiding the\n                // .scan-region-highlight-svg, setting a border, outline, background, etc.\n                this.$overlay.innerHTML = '<svg class=\"scan-region-highlight-svg\" viewBox=\"0 0 238 238\" '\n                    + 'preserveAspectRatio=\"none\" style=\"position:absolute;width:100%;height:100%;left:0;top:0;'\n                    + 'fill:none;stroke:#e9b213;stroke-width:4;stroke-linecap:round;stroke-linejoin:round\">'\n                    + '<path d=\"M31 2H10a8 8 0 0 0-8 8v21M207 2h21a8 8 0 0 1 8 8v21m0 176v21a8 8 0 0 1-8 8h-21m-176 '\n                    + '0H10a8 8 0 0 1-8-8v-21\"/></svg>';\n                try {\n                    this.$overlay.firstElementChild!.animate({ transform: ['scale(.98)', 'scale(1.01)'] }, {\n                        duration: 400,\n                        iterations: Infinity,\n                        direction: 'alternate',\n                        easing: 'ease-in-out',\n                    });\n                } catch (e) {}\n                videoContainer.insertBefore(this.$overlay, this.$video.nextSibling);\n            }\n            if (options.highlightCodeOutline) {\n                // default style; can be overwritten via css\n                this.$overlay.insertAdjacentHTML(\n                    'beforeend',\n                    '<svg class=\"code-outline-highlight\" preserveAspectRatio=\"none\" style=\"display:none;width:100%;'\n                        + 'height:100%;fill:none;stroke:#e9b213;stroke-width:5;stroke-dasharray:25;'\n                        + 'stroke-linecap:round;stroke-linejoin:round\"><polygon/></svg>',\n                );\n                this.$codeOutlineHighlight = this.$overlay.lastElementChild as SVGSVGElement;\n            }\n        }\n        this._scanRegion = this._calculateScanRegion(video);\n\n        requestAnimationFrame(() => {\n            // Checking in requestAnimationFrame which should avoid a potential additional re-flow for getComputedStyle.\n            const videoStyle = window.getComputedStyle(video);\n            if (videoStyle.display === 'none') {\n                video.style.setProperty('display', 'block', 'important');\n                shouldHideVideo = true;\n            }\n            if (videoStyle.visibility !== 'visible') {\n                video.style.setProperty('visibility', 'visible', 'important');\n                shouldHideVideo = true;\n            }\n            if (shouldHideVideo) {\n                // Hide the video in a way that doesn't cause Safari to stop the playback.\n                console.warn('QrScanner has overwritten the video hiding style to avoid Safari stopping the playback.');\n                video.style.opacity = '0';\n                video.style.width = '0';\n                video.style.height = '0';\n                if (this.$overlay && this.$overlay.parentElement) {\n                    this.$overlay.parentElement.removeChild(this.$overlay);\n                }\n                // @ts-ignore\n                delete this.$overlay!;\n                // @ts-ignore\n                delete this.$codeOutlineHighlight!;\n            }\n\n            if (this.$overlay) {\n                this._updateOverlay();\n            }\n        });\n\n        video.addEventListener('play', this._onPlay);\n        video.addEventListener('loadedmetadata', this._onLoadedMetaData);\n        document.addEventListener('visibilitychange', this._onVisibilityChange);\n        window.addEventListener('resize', this._updateOverlay);\n\n        this._qrEnginePromise = QrScanner.createQrEngine();\n    }\n\n    async hasFlash(): Promise<boolean> {\n        let stream: MediaStream | undefined;\n        try {\n            if (this.$video.srcObject) {\n                if (!(this.$video.srcObject instanceof MediaStream)) return false; // srcObject is not a camera stream\n                stream = this.$video.srcObject;\n            } else {\n                stream = (await this._getCameraStream()).stream;\n            }\n            return 'torch' in stream.getVideoTracks()[0].getSettings();\n        } catch (e) {\n            return false;\n        } finally {\n            // close the stream we just opened for detecting whether it supports flash\n            if (stream && stream !== this.$video.srcObject) {\n                console.warn('Call hasFlash after successfully starting the scanner to avoid creating '\n                    + 'a temporary video stream');\n                QrScanner._stopVideoStream(stream);\n            }\n        }\n    }\n\n    isFlashOn(): boolean {\n        return this._flashOn;\n    }\n\n    async toggleFlash(): Promise<void> {\n        if (this._flashOn) {\n            await this.turnFlashOff();\n        } else {\n            await this.turnFlashOn();\n        }\n    }\n\n    async turnFlashOn(): Promise<void> {\n        if (this._flashOn || this._destroyed) return;\n        this._flashOn = true;\n        if (!this._active || this._paused) return; // flash will be turned on later on .start()\n        try {\n            if (!await this.hasFlash()) throw 'No flash available';\n            // Note that the video track is guaranteed to exist and to be a MediaStream due to the check in hasFlash\n            await (this.$video.srcObject as MediaStream).getVideoTracks()[0].applyConstraints({\n                // @ts-ignore: constraint 'torch' is unknown to ts\n                advanced: [{ torch: true }],\n            });\n        } catch (e) {\n            this._flashOn = false;\n            throw e;\n        }\n    }\n\n    async turnFlashOff(): Promise<void> {\n        if (!this._flashOn) return;\n        // applyConstraints with torch: false does not work to turn the flashlight off, as a stream's torch stays\n        // continuously on, see https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints#torch. Therefore,\n        // we have to stop the stream to turn the flashlight off.\n        this._flashOn = false;\n        await this._restartVideoStream();\n    }\n\n    destroy(): void {\n        this.$video.removeEventListener('loadedmetadata', this._onLoadedMetaData);\n        this.$video.removeEventListener('play', this._onPlay);\n        document.removeEventListener('visibilitychange', this._onVisibilityChange);\n        window.removeEventListener('resize', this._updateOverlay);\n\n        this._destroyed = true;\n        this._flashOn = false;\n        this.stop(); // sets this._paused = true and this._active = false\n        QrScanner._postWorkerMessage(this._qrEnginePromise, 'close');\n    }\n\n    async start(): Promise<void> {\n        if (this._destroyed) throw new Error('The QR scanner can not be started as it had been destroyed.');\n        if (this._active && !this._paused) return;\n\n        if (window.location.protocol !== 'https:') {\n            // warn but try starting the camera anyways\n            console.warn('The camera stream is only accessible if the page is transferred via https.');\n        }\n\n        this._active = true;\n        if (document.hidden) return; // camera will be started as soon as tab is in foreground\n        this._paused = false;\n        if (this.$video.srcObject) {\n            // camera stream already/still set\n            await this.$video.play();\n            return;\n        }\n\n        try {\n            const { stream, facingMode } = await this._getCameraStream();\n            if (!this._active || this._paused) {\n                // was stopped in the meantime\n                QrScanner._stopVideoStream(stream);\n                return;\n            }\n            this._setVideoMirror(facingMode);\n            this.$video.srcObject = stream;\n            await this.$video.play();\n\n            // Restart the flash if it was previously on\n            if (this._flashOn) {\n                this._flashOn = false; // force turnFlashOn to restart the flash\n                this.turnFlashOn().catch(() => {});\n            }\n        } catch (e) {\n            if (this._paused) return;\n            this._active = false;\n            throw e;\n        }\n    }\n\n    stop(): void {\n        this.pause();\n        this._active = false;\n    }\n\n    async pause(stopStreamImmediately = false): Promise<boolean> {\n        this._paused = true;\n        if (!this._active) return true;\n        this.$video.pause();\n\n        if (this.$overlay) {\n            this.$overlay.style.display = 'none';\n        }\n\n        const stopStream = () => {\n            if (this.$video.srcObject instanceof MediaStream) {\n                // revoke srcObject only if it's a stream which was likely set by us\n                QrScanner._stopVideoStream(this.$video.srcObject);\n                this.$video.srcObject = null;\n            }\n        };\n\n        if (stopStreamImmediately) {\n            stopStream();\n            return true;\n        }\n\n        await new Promise((resolve) => setTimeout(resolve, 300));\n        if (!this._paused) return false;\n        stopStream();\n        return true;\n    }\n\n    async setCamera(facingModeOrDeviceId: QrScanner.FacingMode | QrScanner.DeviceId): Promise<void> {\n        if (facingModeOrDeviceId === this._preferredCamera) return;\n        this._preferredCamera = facingModeOrDeviceId;\n        // Restart the scanner with the new camera which will also update the video mirror and the scan region.\n        await this._restartVideoStream();\n    }\n\n    static async scanImage(\n        imageOrFileOrBlobOrUrl: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement | OffscreenCanvas | ImageBitmap\n            | SVGImageElement | File | Blob | URL | String,\n        options: {\n            scanRegion?: QrScanner.ScanRegion | null,\n            qrEngine?: Worker | BarcodeDetector | Promise<Worker | BarcodeDetector> | null,\n            canvas?: HTMLCanvasElement | null,\n            disallowCanvasResizing?: boolean,\n            alsoTryWithoutScanRegion?: boolean,\n            /** just a temporary flag until we switch entirely to the new api */\n            returnDetailedScanResult?: true,\n        },\n    ): Promise<QrScanner.ScanResult>;\n    /** @deprecated */\n    static async scanImage(\n        imageOrFileOrBlobOrUrl: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement | OffscreenCanvas | ImageBitmap\n            | SVGImageElement | File | Blob | URL | String,\n        scanRegion?: QrScanner.ScanRegion | null,\n        qrEngine?: Worker | BarcodeDetector | Promise<Worker | BarcodeDetector> | null,\n        canvas?: HTMLCanvasElement | null,\n        disallowCanvasResizing?: boolean,\n        alsoTryWithoutScanRegion?: boolean,\n    ): Promise<string>;\n    static async scanImage(\n        imageOrFileOrBlobOrUrl: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement | OffscreenCanvas | ImageBitmap\n            | SVGImageElement | File | Blob | URL | String,\n        scanRegionOrOptions?: QrScanner.ScanRegion | {\n            scanRegion?: QrScanner.ScanRegion | null,\n            qrEngine?: Worker | BarcodeDetector | Promise<Worker | BarcodeDetector> | null,\n            canvas?: HTMLCanvasElement | null,\n            disallowCanvasResizing?: boolean,\n            alsoTryWithoutScanRegion?: boolean,\n            /** just a temporary flag until we switch entirely to the new api */\n            returnDetailedScanResult?: true,\n        } | null,\n        qrEngine?: Worker | BarcodeDetector | Promise<Worker | BarcodeDetector> | null,\n        canvas?: HTMLCanvasElement | null,\n        disallowCanvasResizing: boolean = false,\n        alsoTryWithoutScanRegion: boolean = false,\n    ): Promise<string | QrScanner.ScanResult> {\n        let scanRegion: QrScanner.ScanRegion | null | undefined;\n        let returnDetailedScanResult = false;\n        if (scanRegionOrOptions && (\n            'scanRegion' in scanRegionOrOptions\n            || 'qrEngine' in scanRegionOrOptions\n            || 'canvas' in scanRegionOrOptions\n            || 'disallowCanvasResizing' in scanRegionOrOptions\n            || 'alsoTryWithoutScanRegion' in scanRegionOrOptions\n            || 'returnDetailedScanResult' in scanRegionOrOptions\n        )) {\n            // we got an options object using the new api\n            scanRegion = scanRegionOrOptions.scanRegion;\n            qrEngine = scanRegionOrOptions.qrEngine;\n            canvas = scanRegionOrOptions.canvas;\n            disallowCanvasResizing = scanRegionOrOptions.disallowCanvasResizing || false;\n            alsoTryWithoutScanRegion = scanRegionOrOptions.alsoTryWithoutScanRegion || false;\n            returnDetailedScanResult = true;\n        } else if (scanRegionOrOptions || qrEngine || canvas || disallowCanvasResizing || alsoTryWithoutScanRegion) {\n            console.warn('You\\'re using a deprecated api for scanImage which will be removed in the future.');\n        } else {\n            // Only imageOrFileOrBlobOrUrl was specified and we can't distinguish between new or old api usage. For\n            // backwards compatibility we have to assume the old api for now. The options object is marked as non-\n            // optional in the parameter list above to make clear that ScanResult instead of string is only returned if\n            // an options object was provided. However, in the future once legacy support is removed, the options object\n            // should become optional.\n            console.warn('Note that the return type of scanImage will change in the future. To already switch to the '\n                + 'new api today, you can pass returnDetailedScanResult: true.');\n        }\n\n        const gotExternalEngine = !!qrEngine;\n\n        try {\n            let image: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement | OffscreenCanvas | ImageBitmap\n                | SVGImageElement;\n            let canvasContext: CanvasRenderingContext2D;\n            [qrEngine, image] = await Promise.all([\n                qrEngine || QrScanner.createQrEngine(),\n                QrScanner._loadImage(imageOrFileOrBlobOrUrl),\n            ]);\n            [canvas, canvasContext] = QrScanner._drawToCanvas(image, scanRegion, canvas, disallowCanvasResizing);\n            let detailedScanResult: QrScanner.ScanResult;\n\n            if (qrEngine instanceof Worker) {\n                const qrEngineWorker = qrEngine; // for ts to know that it's still a worker later in the event listeners\n                if (!gotExternalEngine) {\n                    // Enable scanning of inverted color qr codes.\n                    QrScanner._postWorkerMessageSync(qrEngineWorker, 'inversionMode', 'both');\n                }\n                detailedScanResult = await new Promise((resolve, reject) => {\n                    let timeout: number;\n                    let onMessage: (event: MessageEvent) => void;\n                    let onError: (error: ErrorEvent | string) => void;\n                    let expectedResponseId = -1;\n                    onMessage = (event: MessageEvent) => {\n                        if (event.data.id !== expectedResponseId) {\n                            return;\n                        }\n                        qrEngineWorker.removeEventListener('message', onMessage);\n                        qrEngineWorker.removeEventListener('error', onError);\n                        clearTimeout(timeout);\n                        if (event.data.data !== null) {\n                            resolve({\n                                data: event.data.data,\n                                cornerPoints: QrScanner._convertPoints(event.data.cornerPoints, scanRegion),\n                            });\n                        } else {\n                            reject(QrScanner.NO_QR_CODE_FOUND);\n                        }\n                    };\n                    onError = (error: ErrorEvent | string) => {\n                        qrEngineWorker.removeEventListener('message', onMessage);\n                        qrEngineWorker.removeEventListener('error', onError);\n                        clearTimeout(timeout);\n                        const errorMessage = !error ? 'Unknown Error' : ((error as ErrorEvent).message || error);\n                        reject('Scanner error: ' + errorMessage);\n                    };\n                    qrEngineWorker.addEventListener('message', onMessage);\n                    qrEngineWorker.addEventListener('error', onError);\n                    timeout = setTimeout(() => onError('timeout'), 10000);\n                    const imageData = canvasContext.getImageData(0, 0, canvas!.width, canvas!.height);\n                    expectedResponseId = QrScanner._postWorkerMessageSync(\n                        qrEngineWorker,\n                        'decode',\n                        imageData,\n                        [imageData.data.buffer],\n                    );\n                });\n            } else {\n                detailedScanResult = await Promise.race([\n                    new Promise<QrScanner.ScanResult>((resolve, reject) => window.setTimeout(\n                        () => reject('Scanner error: timeout'),\n                        10000,\n                    )),\n                    (async (): Promise<QrScanner.ScanResult> => {\n                        try {\n                            const [scanResult] = await qrEngine.detect(canvas!);\n                            if (!scanResult) throw QrScanner.NO_QR_CODE_FOUND;\n                            return {\n                                data: scanResult.rawValue,\n                                cornerPoints: QrScanner._convertPoints(scanResult.cornerPoints, scanRegion),\n                            };\n                        } catch (e) {\n                            const errorMessage = (e as Error).message || e as string;\n                            if (/not implemented|service unavailable/.test(errorMessage)) {\n                                // Not implemented can apparently for some reason happen even though getSupportedFormats\n                                // in createQrScanner reported that it's supported, see issue #98.\n                                // Service unavailable can happen after some time when the BarcodeDetector crashed and\n                                // can theoretically be recovered from by creating a new BarcodeDetector. However, in\n                                // newer browsers this issue does not seem to be present anymore and therefore we do not\n                                // apply this optimization anymore but just set _disableBarcodeDetector in both cases.\n                                // Also note that if we got an external qrEngine that crashed, we should possibly notify\n                                // the caller about it, but we also don't do this here, as it's such an unlikely case.\n                                QrScanner._disableBarcodeDetector = true;\n                                // retry without passing the broken BarcodeScanner instance\n                                return QrScanner.scanImage(imageOrFileOrBlobOrUrl, {\n                                    scanRegion,\n                                    canvas,\n                                    disallowCanvasResizing,\n                                    alsoTryWithoutScanRegion,\n                                });\n                            }\n                            throw `Scanner error: ${errorMessage}`;\n                        }\n                    })(),\n                ]);\n            }\n            return returnDetailedScanResult ? detailedScanResult : detailedScanResult.data;\n        } catch (e) {\n            if (!scanRegion || !alsoTryWithoutScanRegion) throw e;\n            const detailedScanResult = await QrScanner.scanImage(\n                imageOrFileOrBlobOrUrl,\n                { qrEngine, canvas, disallowCanvasResizing },\n            );\n            return returnDetailedScanResult ? detailedScanResult : detailedScanResult.data;\n        } finally {\n            if (!gotExternalEngine) {\n                QrScanner._postWorkerMessage(qrEngine!, 'close');\n            }\n        }\n    }\n\n    setGrayscaleWeights(red: number, green: number, blue: number, useIntegerApproximation: boolean = true): void {\n        // Note that for the native BarcodeDecoder or if the worker was destroyed, this is a no-op. However, the native\n        // implementations work also well with colored qr codes.\n        QrScanner._postWorkerMessage(\n            this._qrEnginePromise,\n            'grayscaleWeights',\n            { red, green, blue, useIntegerApproximation }\n        );\n    }\n\n    setInversionMode(inversionMode: QrScanner.InversionMode): void {\n        // Note that for the native BarcodeDecoder or if the worker was destroyed, this is a no-op. However, the native\n        // implementations scan normal and inverted qr codes by default\n        QrScanner._postWorkerMessage(this._qrEnginePromise, 'inversionMode', inversionMode);\n    }\n\n    static async createQrEngine(): Promise<Worker | BarcodeDetector>;\n    /** @deprecated */\n    static async createQrEngine(workerPath: string): Promise<Worker | BarcodeDetector>;\n    static async createQrEngine(workerPath?: string): Promise<Worker | BarcodeDetector> {\n        if (workerPath) {\n            console.warn('Specifying a worker path is not required and not supported anymore.');\n        }\n\n        // @ts-ignore no types defined for import\n        const createWorker = () => (import('./qr-scanner-worker.min.js') as Promise<{ createWorker: () => Worker }>)\n            .then((module) => module.createWorker());\n\n        const useBarcodeDetector = !QrScanner._disableBarcodeDetector\n            && 'BarcodeDetector' in window\n            && BarcodeDetector.getSupportedFormats\n            && (await BarcodeDetector.getSupportedFormats()).includes('qr_code');\n\n        if (!useBarcodeDetector) return createWorker();\n\n        // On Macs with an M1/M2 processor and macOS Ventura (macOS version 13), the BarcodeDetector is broken in\n        // Chromium based browsers, regardless of the version. For that constellation, the BarcodeDetector does not\n        // error but does not detect QR codes. Macs without an M1/M2 or before Ventura are fine.\n        // See issue #209 and https://bugs.chromium.org/p/chromium/issues/detail?id=1382442\n        // TODO update this once the issue in macOS is fixed\n        const userAgentData = navigator.userAgentData;\n        const isChromiumOnMacWithArmVentura = userAgentData // all Chromium browsers support userAgentData\n            && userAgentData.brands.some(({ brand }) => /Chromium/i.test(brand))\n            && /mac ?OS/i.test(userAgentData.platform)\n            // Does it have an ARM chip (e.g. M1/M2) and Ventura? Check this last as getHighEntropyValues can\n            // theoretically trigger a browser prompt, although no browser currently does seem to show one.\n            // If browser or user refused to return the requested values, assume broken ARM Ventura, to be safe.\n            && await userAgentData.getHighEntropyValues(['architecture', 'platformVersion'])\n                .then(({ architecture, platformVersion }) =>\n                    /arm/i.test(architecture || 'arm') && parseInt(platformVersion || '13') >= /* Ventura */ 13)\n                .catch(() => true);\n        if (isChromiumOnMacWithArmVentura) return createWorker();\n\n        return new BarcodeDetector({ formats: ['qr_code'] });\n    }\n\n    private _onPlay(): void {\n        this._scanRegion = this._calculateScanRegion(this.$video);\n        this._updateOverlay();\n        if (this.$overlay) {\n            this.$overlay.style.display = '';\n        }\n        this._scanFrame();\n    }\n\n    private _onLoadedMetaData(): void {\n        this._scanRegion = this._calculateScanRegion(this.$video);\n        this._updateOverlay();\n    }\n\n    private _onVisibilityChange(): void {\n        if (document.hidden) {\n            this.pause();\n        } else if (this._active) {\n            this.start();\n        }\n    }\n\n    private _calculateScanRegion(video: HTMLVideoElement): QrScanner.ScanRegion {\n        // Default scan region calculation. Note that this can be overwritten in the constructor.\n        const smallestDimension = Math.min(video.videoWidth, video.videoHeight);\n        const scanRegionSize = Math.round(2 / 3 * smallestDimension);\n        return {\n            x: Math.round((video.videoWidth - scanRegionSize) / 2),\n            y: Math.round((video.videoHeight - scanRegionSize) / 2),\n            width: scanRegionSize,\n            height: scanRegionSize,\n            downScaledWidth: this._legacyCanvasSize,\n            downScaledHeight: this._legacyCanvasSize,\n        };\n    }\n\n    private _updateOverlay(): void {\n        requestAnimationFrame(() => {\n            // Running in requestAnimationFrame which should avoid a potential additional re-flow for getComputedStyle\n            // and offsetWidth, offsetHeight, offsetLeft, offsetTop.\n            if (!this.$overlay) return;\n            const video = this.$video;\n            const videoWidth = video.videoWidth;\n            const videoHeight = video.videoHeight;\n            const elementWidth = video.offsetWidth;\n            const elementHeight = video.offsetHeight;\n            const elementX = video.offsetLeft;\n            const elementY = video.offsetTop;\n\n            const videoStyle = window.getComputedStyle(video);\n            const videoObjectFit = videoStyle.objectFit;\n            const videoAspectRatio = videoWidth / videoHeight;\n            const elementAspectRatio = elementWidth / elementHeight;\n            let videoScaledWidth: number;\n            let videoScaledHeight: number;\n            switch (videoObjectFit) {\n                case 'none':\n                    videoScaledWidth = videoWidth;\n                    videoScaledHeight = videoHeight;\n                    break;\n                case 'fill':\n                    videoScaledWidth = elementWidth;\n                    videoScaledHeight = elementHeight;\n                    break;\n                default: // 'cover', 'contains', 'scale-down'\n                    if (videoObjectFit === 'cover'\n                        ? videoAspectRatio > elementAspectRatio\n                        : videoAspectRatio < elementAspectRatio) {\n                        // The scaled height is the element height\n                        // - for 'cover' if the video aspect ratio is wider than the element aspect ratio\n                        //   (scaled height matches element height and scaled width overflows element width)\n                        // - for 'contains'/'scale-down' if element aspect ratio is wider than the video aspect ratio\n                        //   (scaled height matched element height and element width overflows scaled width)\n                        videoScaledHeight = elementHeight;\n                        videoScaledWidth = videoScaledHeight * videoAspectRatio;\n                    } else {\n                        videoScaledWidth = elementWidth;\n                        videoScaledHeight = videoScaledWidth / videoAspectRatio;\n                    }\n                    if (videoObjectFit === 'scale-down') {\n                        // for 'scale-down' the dimensions are the minimum of 'contains' and 'none'\n                        videoScaledWidth = Math.min(videoScaledWidth, videoWidth);\n                        videoScaledHeight = Math.min(videoScaledHeight, videoHeight);\n                    }\n            }\n\n            // getComputedStyle is so nice to convert keywords (left, center, right, top, bottom) to percent and makes\n            // sure to set the default of 50% if only one or no component was provided, therefore we can be sure that\n            // both components are set. Additionally, it converts units other than px (e.g. rem) to px.\n            const [videoX, videoY] = videoStyle.objectPosition.split(' ').map((length, i) => {\n                const lengthValue = parseFloat(length);\n                return length.endsWith('%')\n                    ? (!i ? elementWidth - videoScaledWidth : elementHeight - videoScaledHeight) * lengthValue / 100\n                    : lengthValue;\n            });\n\n            const regionWidth = this._scanRegion.width || videoWidth;\n            const regionHeight = this._scanRegion.height || videoHeight;\n            const regionX = this._scanRegion.x || 0;\n            const regionY = this._scanRegion.y || 0;\n\n            const overlayStyle = this.$overlay.style;\n            overlayStyle.width = `${regionWidth / videoWidth * videoScaledWidth}px`;\n            overlayStyle.height = `${regionHeight / videoHeight * videoScaledHeight}px`;\n            overlayStyle.top = `${elementY + videoY + regionY / videoHeight * videoScaledHeight}px`;\n            const isVideoMirrored = /scaleX\\(-1\\)/.test(video.style.transform!);\n            overlayStyle.left = `${elementX\n                + (isVideoMirrored ? elementWidth - videoX - videoScaledWidth : videoX)\n                + (isVideoMirrored ? videoWidth - regionX - regionWidth : regionX) / videoWidth * videoScaledWidth}px`;\n            // apply same mirror as on video\n            overlayStyle.transform = video.style.transform;\n        });\n    }\n\n    private static _convertPoints(\n        points: QrScanner.Point[],\n        scanRegion?: QrScanner.ScanRegion | null,\n    ): QrScanner.Point[] {\n        if (!scanRegion) return points;\n        const offsetX = scanRegion.x || 0;\n        const offsetY = scanRegion.y || 0;\n        const scaleFactorX = scanRegion.width && scanRegion.downScaledWidth\n            ? scanRegion.width / scanRegion.downScaledWidth\n            : 1;\n        const scaleFactorY = scanRegion.height && scanRegion.downScaledHeight\n            ? scanRegion.height / scanRegion.downScaledHeight\n            : 1;\n        for (const point of points) {\n            point.x = point.x * scaleFactorX + offsetX;\n            point.y = point.y * scaleFactorY + offsetY;\n        }\n        return points;\n    }\n\n    private _scanFrame(): void {\n        if (!this._active || this.$video.paused || this.$video.ended) return;\n        // If requestVideoFrameCallback is available use that to avoid unnecessary scans on the same frame as the\n        // camera's framerate can be lower than the screen refresh rate and this._maxScansPerSecond, especially in dark\n        // settings where the exposure time is longer. Both, requestVideoFrameCallback and requestAnimationFrame are not\n        // being fired if the tab is in the background, which is what we want.\n        const requestFrame = 'requestVideoFrameCallback' in this.$video\n            // @ts-ignore\n            ? this.$video.requestVideoFrameCallback.bind(this.$video)\n            : requestAnimationFrame;\n        requestFrame(async () => {\n            if (this.$video.readyState <= 1) {\n                // Skip scans until the video is ready as drawImage() only works correctly on a video with readyState\n                // > 1, see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage#Notes.\n                // This also avoids false positives for videos paused after a successful scan which remains visible on\n                // the canvas until the video is started again and ready.\n                this._scanFrame();\n                return;\n            }\n\n            const timeSinceLastScan = Date.now() - this._lastScanTimestamp;\n            const minimumTimeBetweenScans = 1000 / this._maxScansPerSecond;\n            if (timeSinceLastScan < minimumTimeBetweenScans) {\n                await new Promise((resolve) => setTimeout(resolve, minimumTimeBetweenScans - timeSinceLastScan));\n            }\n            // console.log('Scan rate:', Math.round(1000 / (Date.now() - this._lastScanTimestamp)));\n            this._lastScanTimestamp = Date.now();\n\n            let result: QrScanner.ScanResult | undefined;\n            try {\n                result = await QrScanner.scanImage(this.$video, {\n                    scanRegion: this._scanRegion,\n                    qrEngine: this._qrEnginePromise,\n                    canvas: this.$canvas,\n                });\n            } catch (error) {\n                if (!this._active) return;\n                this._onDecodeError(error as Error | string);\n            }\n\n            if (QrScanner._disableBarcodeDetector && !(await this._qrEnginePromise instanceof Worker)) {\n                // replace the disabled BarcodeDetector\n                this._qrEnginePromise = QrScanner.createQrEngine();\n            }\n\n            if (result) {\n                if (this._onDecode) {\n                    this._onDecode(result);\n                } else if (this._legacyOnDecode) {\n                    this._legacyOnDecode(result.data);\n                }\n\n                if (this.$codeOutlineHighlight) {\n                    clearTimeout(this._codeOutlineHighlightRemovalTimeout);\n                    this._codeOutlineHighlightRemovalTimeout = undefined;\n                    this.$codeOutlineHighlight.setAttribute(\n                        'viewBox',\n                        `${this._scanRegion.x || 0} `\n                            + `${this._scanRegion.y || 0} `\n                            + `${this._scanRegion.width || this.$video.videoWidth} `\n                            + `${this._scanRegion.height || this.$video.videoHeight}`,\n                    );\n                    const polygon = this.$codeOutlineHighlight.firstElementChild!;\n                    polygon.setAttribute('points', result.cornerPoints.map(({x, y}) => `${x},${y}`).join(' '));\n                    this.$codeOutlineHighlight.style.display = '';\n                }\n            } else if (this.$codeOutlineHighlight && !this._codeOutlineHighlightRemovalTimeout) {\n                // hide after timeout to make it flash less when on some frames the QR code is detected and on some not\n                this._codeOutlineHighlightRemovalTimeout = setTimeout(\n                    () => this.$codeOutlineHighlight!.style.display = 'none',\n                    100,\n                );\n            }\n\n            this._scanFrame();\n        });\n    }\n\n    private _onDecodeError(error: Error | string): void {\n        // default error handler; can be overwritten in the constructor\n        if (error === QrScanner.NO_QR_CODE_FOUND) return;\n        console.log(error);\n    }\n\n    private async _getCameraStream(): Promise<{ stream: MediaStream, facingMode: QrScanner.FacingMode }> {\n        if (!navigator.mediaDevices) throw 'Camera not found.';\n\n        const preferenceType = /^(environment|user)$/.test(this._preferredCamera)\n            ? 'facingMode'\n            : 'deviceId';\n        const constraintsWithoutCamera: Array<MediaTrackConstraints> = [{\n            width: { min: 1024 }\n        }, {\n            width: { min: 768 }\n        }, {}];\n        const constraintsWithCamera = constraintsWithoutCamera.map((constraint) => Object.assign({}, constraint, {\n            [preferenceType]: { exact: this._preferredCamera },\n        }));\n\n        for (const constraints of [...constraintsWithCamera, ...constraintsWithoutCamera]) {\n            try {\n                const stream = await navigator.mediaDevices.getUserMedia({ video: constraints, audio: false });\n                // Try to determine the facing mode from the stream, otherwise use a guess or 'environment' as\n                // default. Note that the guess is not always accurate as Safari returns cameras of different facing\n                // mode, even for exact facingMode constraints.\n                const facingMode = this._getFacingMode(stream)\n                    || (constraints.facingMode\n                        ? this._preferredCamera as QrScanner.FacingMode // a facing mode we were able to fulfill\n                        : (this._preferredCamera === 'environment'\n                            ? 'user' // switch as _preferredCamera was environment but we are not able to fulfill it\n                            : 'environment' // switch from unfulfilled user facingMode or default to environment\n                        )\n                    );\n                return { stream, facingMode };\n            } catch (e) {}\n        }\n\n        throw 'Camera not found.';\n    }\n\n    private async _restartVideoStream(): Promise<void> {\n        // Note that we always pause the stream and not only if !this._paused as even if this._paused === true, the\n        // stream might still be running, as it's by default only stopped after a delay of 300ms.\n        const wasPaused = this._paused;\n        const paused = await this.pause(true);\n        if (!paused || wasPaused || !this._active) return;\n        await this.start();\n    }\n\n    private static _stopVideoStream(stream : MediaStream): void {\n        for (const track of stream.getTracks()) {\n            track.stop(); //  note that this will also automatically turn the flashlight off\n            stream.removeTrack(track);\n        }\n    }\n\n    private _setVideoMirror(facingMode: QrScanner.FacingMode): void {\n        // in user facing mode mirror the video to make it easier for the user to position the QR code\n        const scaleFactor = facingMode === 'user'? -1 : 1;\n        this.$video.style.transform = 'scaleX(' + scaleFactor + ')';\n    }\n\n    private _getFacingMode(videoStream: MediaStream): QrScanner.FacingMode | null {\n        const videoTrack = videoStream.getVideoTracks()[0];\n        if (!videoTrack) return null; // unknown\n        // inspired by https://github.com/JodusNodus/react-qr-reader/blob/master/src/getDeviceId.js#L13\n        return /rear|back|environment/i.test(videoTrack.label)\n            ? 'environment'\n            : /front|user|face/i.test(videoTrack.label)\n                ? 'user'\n                : null; // unknown\n    }\n\n    private static _drawToCanvas(\n        image: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement | OffscreenCanvas | ImageBitmap\n            | SVGImageElement,\n        scanRegion?: QrScanner.ScanRegion | null,\n        canvas?: HTMLCanvasElement | null,\n        disallowCanvasResizing= false,\n    ): [HTMLCanvasElement, CanvasRenderingContext2D] {\n        canvas = canvas || document.createElement('canvas');\n        const scanRegionX = scanRegion && scanRegion.x ? scanRegion.x : 0;\n        const scanRegionY = scanRegion && scanRegion.y ? scanRegion.y : 0;\n        const scanRegionWidth = scanRegion && scanRegion.width\n            ? scanRegion.width\n            : (image as HTMLVideoElement).videoWidth || image.width as number;\n        const scanRegionHeight = scanRegion && scanRegion.height\n            ? scanRegion.height\n            : (image as HTMLVideoElement).videoHeight || image.height as number;\n\n        if (!disallowCanvasResizing) {\n            const canvasWidth = scanRegion && scanRegion.downScaledWidth\n                ? scanRegion.downScaledWidth\n                : scanRegionWidth;\n            const canvasHeight = scanRegion && scanRegion.downScaledHeight\n                ? scanRegion.downScaledHeight\n                : scanRegionHeight;\n            // Setting the canvas width or height clears the canvas, even if the values didn't change, therefore only\n            // set them if they actually changed.\n            if (canvas.width !== canvasWidth) {\n                canvas.width = canvasWidth;\n            }\n            if (canvas.height !== canvasHeight) {\n                canvas.height = canvasHeight;\n            }\n        }\n\n        const context = canvas.getContext('2d', { alpha: false })!;\n        context.imageSmoothingEnabled = false; // gives less blurry images\n        context.drawImage(\n            image,\n            scanRegionX, scanRegionY, scanRegionWidth, scanRegionHeight,\n            0, 0, canvas.width, canvas.height,\n        );\n        return [canvas, context];\n    }\n\n    private static async _loadImage(\n        imageOrFileOrBlobOrUrl: HTMLImageElement | HTMLVideoElement | HTMLCanvasElement | OffscreenCanvas | ImageBitmap\n            | SVGImageElement | File | Blob | URL | String,\n    ): Promise<HTMLImageElement | HTMLVideoElement | HTMLCanvasElement | OffscreenCanvas | ImageBitmap\n        | SVGImageElement > {\n        if (imageOrFileOrBlobOrUrl instanceof Image) {\n            await QrScanner._awaitImageLoad(imageOrFileOrBlobOrUrl);\n            return imageOrFileOrBlobOrUrl;\n        } else if (imageOrFileOrBlobOrUrl instanceof HTMLVideoElement\n            || imageOrFileOrBlobOrUrl instanceof HTMLCanvasElement\n            || imageOrFileOrBlobOrUrl instanceof SVGImageElement\n            || 'OffscreenCanvas' in window && imageOrFileOrBlobOrUrl instanceof OffscreenCanvas\n            || 'ImageBitmap' in window && imageOrFileOrBlobOrUrl instanceof ImageBitmap) {\n            return imageOrFileOrBlobOrUrl;\n        } else if (imageOrFileOrBlobOrUrl instanceof File || imageOrFileOrBlobOrUrl instanceof Blob\n            || imageOrFileOrBlobOrUrl instanceof URL || typeof imageOrFileOrBlobOrUrl === 'string') {\n            const image = new Image();\n            if (imageOrFileOrBlobOrUrl instanceof File || imageOrFileOrBlobOrUrl instanceof Blob) {\n                image.src = URL.createObjectURL(imageOrFileOrBlobOrUrl);\n            } else {\n                image.src = imageOrFileOrBlobOrUrl.toString();\n            }\n            try {\n                await QrScanner._awaitImageLoad(image);\n                return image;\n            } finally {\n                if (imageOrFileOrBlobOrUrl instanceof File || imageOrFileOrBlobOrUrl instanceof Blob) {\n                    URL.revokeObjectURL(image.src);\n                }\n            }\n        } else {\n            throw 'Unsupported image type.';\n        }\n    }\n\n    private static async _awaitImageLoad(image: HTMLImageElement): Promise<void> {\n        if (image.complete && image.naturalWidth !== 0) return; // already loaded\n        await new Promise<void>((resolve, reject) => {\n            const listener = (event: ErrorEvent | Event) => {\n                image.removeEventListener('load', listener);\n                image.removeEventListener('error', listener);\n                if (event instanceof ErrorEvent) {\n                    reject('Image load error');\n                } else {\n                    resolve();\n                }\n            };\n            image.addEventListener('load', listener);\n            image.addEventListener('error', listener);\n        });\n    }\n\n    private static async _postWorkerMessage(\n        qrEngineOrQrEnginePromise: Worker | BarcodeDetector | Promise<Worker | BarcodeDetector>,\n        type: string,\n        data?: any,\n        transfer?: Transferable[],\n    ): Promise<number> {\n        return QrScanner._postWorkerMessageSync(await qrEngineOrQrEnginePromise, type, data, transfer);\n    }\n\n    // sync version of _postWorkerMessage without performance overhead of async functions\n    private static _postWorkerMessageSync(\n        qrEngine: Worker | BarcodeDetector,\n        type: string,\n        data?: any,\n        transfer?: Transferable[],\n    ): number {\n        if (!(qrEngine instanceof Worker)) return -1;\n        const id = QrScanner._workerMessageId++;\n        qrEngine.postMessage({\n            id,\n            type,\n            data,\n        }, transfer);\n        return id;\n    }\n}\n\ndeclare namespace QrScanner {\n    export interface ScanRegion {\n        x?: number;\n        y?: number;\n        width?: number;\n        height?: number;\n        downScaledWidth?: number;\n        downScaledHeight?: number;\n    }\n\n    export type FacingMode = 'environment' | 'user';\n    export type DeviceId = string;\n\n    export interface Camera {\n        id: DeviceId;\n        label: string;\n    }\n\n    export type InversionMode = 'original' | 'invert' | 'both';\n\n    export interface Point {\n        x: number;\n        y: number;\n    }\n\n    export interface ScanResult {\n        data: string;\n        // In clockwise order, starting at top left, but this might not be guaranteed in the future.\n        cornerPoints: QrScanner.Point[];\n    }\n}\n\n// simplified from https://wicg.github.io/shape-detection-api/#barcode-detection-api\ndeclare class BarcodeDetector {\n    constructor(options?: { formats: string[] });\n    static getSupportedFormats(): Promise<string[]>;\n    detect(image: ImageBitmapSource): Promise<Array<{ rawValue: string, cornerPoints: QrScanner.Point[] }>>;\n}\n\n// simplified from https://github.com/lukewarlow/user-agent-data-types/blob/master/index.d.ts\ndeclare global {\n    interface Navigator {\n        readonly userAgentData?: {\n            readonly platform: string;\n            readonly brands: Array<{\n                readonly brand: string;\n                readonly version: string;\n            }>;\n            getHighEntropyValues(hints: string[]): Promise<{\n                readonly architecture?: string;\n                readonly platformVersion?: string;\n            }>;\n        };\n    }\n}\n\nexport default QrScanner;\n"], "names": ["QrScanner", "constructor", "video", "onDecode", "canvasSizeOrOnDecodeErrorOrOptions", "canvasSizeOrCalculateScanRegion", "preferredCamera", "DEFAULT_CANVAS_SIZE", "$video", "$canvas", "document", "createElement", "_onDecode", "console", "warn", "_legacyOnDecode", "_onDecodeError", "options", "onDecodeError", "_calculateScanRegion", "calculateScanRegion", "_preferredCamera", "_legacyCanvasSize", "_maxScansPerSecond", "maxScansPerSecond", "_onPlay", "bind", "_onLoadedMetaData", "_onVisibilityChange", "_updateOverlay", "disablePictureInPicture", "playsInline", "muted", "shouldHideVideo", "hidden", "body", "contains", "append<PERSON><PERSON><PERSON>", "highlightScanRegion", "highlightCodeOutline", "$overlay", "overlay", "overlayStyle", "position", "display", "pointerEvents", "classList", "add", "gotExternalOverlay", "innerHTML", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "animate", "transform", "duration", "iterations", "Infinity", "direction", "easing", "e", "videoContainer", "insertBefore", "nextS<PERSON>ling", "insertAdjacentHTML", "$codeOutlineHighlight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_scanRegion", "requestAnimationFrame", "videoStyle", "style", "setProperty", "visibility", "opacity", "width", "height", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "window", "_qrEnginePromise", "createQrEngine", "worker<PERSON><PERSON>", "hasCamera", "length", "listCameras", "requestLabels", "navigator", "mediaDevices", "filter", "device", "openedStream", "every", "enumerateCameras", "camera", "label", "getUserMedia", "audio", "map", "i", "id", "deviceId", "_stopVideoStream", "hasFlash", "stream", "srcObject", "MediaStream", "_getCameraStream", "getVideoTracks", "getSettings", "isFlashOn", "_flashOn", "toggleFlash", "turnFlashOff", "turnFlashOn", "_destroyed", "_active", "_paused", "applyConstraints", "advanced", "torch", "_restartVideoStream", "destroy", "removeEventListener", "stop", "_postWorkerMessage", "start", "Error", "location", "protocol", "play", "facingMode", "_setVideoMirror", "catch", "pause", "stopStreamImmediately", "stopStream", "Promise", "resolve", "setTimeout", "setCamera", "facingModeOrDeviceId", "scanImage", "imageOrFileOrBlobOrUrl", "scanRegionOrOptions", "qrEngine", "canvas", "disallowCanvasResizing", "alsoTryWithoutScanRegion", "scanRegion", "returnDetailedScanResult", "image", "canvasContext", "all", "_loadImage", "_drawToC<PERSON>vas", "detailedScanResult", "Worker", "gotExternalEngine", "_postWorkerMessageSync", "qrEngineWorker", "reject", "timeout", "onMessage", "onError", "expectedResponseId", "event", "data", "clearTimeout", "cornerPoints", "_convertPoints", "NO_QR_CODE_FOUND", "error", "imageData", "buffer", "race", "scanResult", "rawValue", "message", "test", "errorMessage", "_disableBarcodeDetector", "setGrayscaleWeights", "red", "green", "blue", "useIntegerApproximation", "setInversionMode", "inversionMode", "then", "module", "createWorker", "BarcodeDetector", "getSupportedFormats", "includes", "userAgentData", "brands", "some", "brand", "platform", "getHighEntropyValues", "architecture", "platformVersion", "parseInt", "formats", "_scanFrame", "x", "Math", "round", "videoWidth", "scanRegionSize", "y", "videoHeight", "downScaledWidth", "downScaledHeight", "videoObjectFit", "videoScaledWidth", "videoScaledHeight", "elementWidth", "elementHeight", "videoAspectRatio", "elementAspectRatio", "min", "videoY", "lengthValue", "parseFloat", "endsWith", "regionWidth", "regionHeight", "top", "elementY", "regionY", "left", "elementX", "isVideoMirrored", "videoX", "regionX", "points", "point", "scaleFactorX", "offsetX", "scaleFactorY", "offsetY", "paused", "ended", "requestVideoFrameCallback", "readyState", "timeSinceLastScan", "minimumTimeBetweenScans", "_lastScanTimestamp", "Date", "now", "result", "_codeOutlineHighlightRemovalTimeout", "undefined", "setAttribute", "join", "log", "constraint", "preferenceType", "exact", "constraints", "wasPaused", "track", "removeTrack", "_getFacingMode", "videoStream", "videoTrack", "scanRegionWidth", "scanRegionHeight", "canvasWidth", "canvasHeight", "alpha", "context", "imageSmoothingEnabled", "drawImage", "scanRegionX", "scanRegionY", "Image", "_awaitImageLoad", "HTMLVideoElement", "HTMLCanvasElement", "SVGImageElement", "OffscreenCanvas", "ImageBitmap", "File", "Blob", "URL", "src", "createObjectURL", "toString", "revokeObjectURL", "complete", "naturalWidth", "listener", "ErrorEvent", "qrEngineOrQrEnginePromise", "type", "transfer", "postMessage"], "mappings": "aAAA,KAAMA,EAAN,CA0GIC,YACIC,EACAC,EACAC,EAWAC,EACAC,GA5Da,sBAAA,CAA4BN,CAAUO,CAAAA,mBAC/C,sBAAA,CAA8D,aACrD,wBAAA,CAA6B,EACtC,wBAAA,CAA6B,CAAC,CAO9B,gBAAA,CADA,aACA,CAFA,YAEA,CAHA,YAGA,CAHmB,CAAA,CAuDvB,KAAKC,CAAAA,MAAL,CAAcN,CACd,KAAKO,CAAAA,OAAL,CAAeC,QAASC,CAAAA,aAAT,CAAuB,QAAvB,CAEXP,EAAJ,EAAwF,QAAxF,GAA0C,MAAOA,EAAjD,CAEI,IAAKQ,CAAAA,SAFT,CAEqBT,CAFrB,EAIQC,CAAJ,EAA0CC,CAA1C,EAA6EC,CAA7E,CACIO,OAAQC,CAAAA,IAAR,CAAa,oGAAb,CADJ,CASID,OAAQC,CAAAA,IAAR,CAAa,0KAAb,CAGJ;AAAA,IAAKC,CAAAA,eAAL,CAAuBZ,CAhB3B,wBAoBMC,EACA,EACN,KAAKY,CAAAA,cAAL,CAAsBC,CAAQC,CAAAA,aAA9B,GAA8F,UAA9C,GAAA,MAAOd,EAAP,CAC1CA,CAD0C,CAE1C,IAAKY,CAAAA,cAFX,CAGA,KAAKG,CAAAA,oBAAL,CAA4BF,CAAQG,CAAAA,mBAApC,GAAqG,UAAzC,GAAA,MAAOf,EAAP,CACtDA,CADsD,CAEtD,IAAKc,CAAAA,oBAFX,CAGA,KAAKE,CAAAA,gBAAL,CAAwBJ,CAAQX,CAAAA,eAAhC,EAAmDA,CAAnD,EAAsE,IAAKe,CAAAA,gBAC3E,KAAKC,CAAAA,iBAAL,CAAuE,QAA9C,GAAA,MAAOlB,EAAP,CACnBA,CADmB,CAEwB,QAA3C,GAAA,MAAOC,EAAP,CACIA,CADJ,CAEI,IAAKiB,CAAAA,iBACf,KAAKC,CAAAA,kBAAL,CAA0BN,CAAQO,CAAAA,iBAAlC,EAAuD,IAAKD,CAAAA,kBAE5D,KAAKE,CAAAA,OAAL,CAAe,IAAKA,CAAAA,OAAQC,CAAAA,IAAb,CAAkB,IAAlB,CACf,KAAKC,CAAAA,iBAAL;AAAyB,IAAKA,CAAAA,iBAAkBD,CAAAA,IAAvB,CAA4B,IAA5B,CACzB,KAAKE,CAAAA,mBAAL,CAA2B,IAAKA,CAAAA,mBAAoBF,CAAAA,IAAzB,CAA8B,IAA9B,CAC3B,KAAKG,CAAAA,cAAL,CAAsB,IAAKA,CAAAA,cAAeH,CAAAA,IAApB,CAAyB,IAAzB,CAGtBxB,EAAM4B,CAAAA,uBAAN,CAAgC,CAAA,CAIhC5B,EAAM6B,CAAAA,WAAN,CAAoB,CAAA,CAGpB7B,EAAM8B,CAAAA,KAAN,CAAc,CAAA,CAId,KAAIC,EAAkB,CAAA,CAClB/B,EAAMgC,CAAAA,MAAV,GACIhC,CAAMgC,CAAAA,MACN,CADe,CAAA,CACf,CAAAD,CAAA,CAAkB,CAAA,CAFtB,CAIKvB,SAASyB,CAAAA,IAAKC,CAAAA,QAAd,CAAuBlC,CAAvB,CAAL,GACIQ,QAASyB,CAAAA,IAAKE,CAAAA,WAAd,CAA0BnC,CAA1B,CACA,CAAA+B,CAAA,CAAkB,CAAA,CAFtB,mBAMA,IAAIhB,CAAQqB,CAAAA,mBAAZ,EAAmCrB,CAAQsB,CAAAA,oBAA3C,CAAiE,KAClCtB,SAC3B,KAAKuB,CAAAA,QAAL,CAAgBvB,CAAQwB,CAAAA,OAAxB,EAAmC/B,QAASC,CAAAA,aAAT,CAAuB,KAAvB,uBAEnC+B,EAAaC,CAAAA,QAAb,CAAwB,UACxBD,EAAaE,CAAAA,OAAb,CAAuB,MACvBF;CAAaG,CAAAA,aAAb,CAA6B,MAC7B,KAAKL,CAAAA,QAASM,CAAAA,SAAUC,CAAAA,GAAxB,CAA4B,uBAA5B,CACA,IAAI,CAACC,CAAL,EAA2B/B,CAAQqB,CAAAA,mBAAnC,CAAwD,CAGpD,IAAKE,CAAAA,QAASS,CAAAA,SAAd,CAA0B,uWAK1B,IAAI,CACA,IAAKT,CAAAA,QAASU,CAAAA,iBAAmBC,CAAAA,OAAjC,CAAyC,CAAEC,UAAW,CAAC,YAAD;AAAe,aAAf,CAAb,CAAzC,CAAuF,CACnFC,SAAU,GADyE,CAEnFC,WAAYC,QAFuE,CAGnFC,UAAW,WAHwE,CAInFC,OAAQ,aAJ2E,CAAvF,CADA,CAOF,MAAOC,CAAP,CAAU,EACZC,CAAeC,CAAAA,YAAf,CAA4B,IAAKpB,CAAAA,QAAjC,CAA2C,IAAKhC,CAAAA,MAAOqD,CAAAA,WAAvD,CAhBoD,CAkBpD5C,CAAQsB,CAAAA,oBAAZ,GAEI,IAAKC,CAAAA,QAASsB,CAAAA,kBAAd,CACI,WADJ,CAEI,oOAFJ,CAMA,CAAA,IAAKC,CAAAA,qBAAL,CAA6B,IAAKvB,CAAAA,QAASwB,CAAAA,gBAR/C,CA1B6D,CAqCjE,IAAKC,CAAAA,WAAL;AAAmB,IAAK9C,CAAAA,oBAAL,CAA0BjB,CAA1B,CAEnBgE,sBAAA,CAAsB,EAAA,GAElB,gCAC2B,OAA3B,GAAIC,CAAWvB,CAAAA,OAAf,GACI1C,CAAMkE,CAAAA,KAAMC,CAAAA,WAAZ,CAAwB,SAAxB,CAAmC,OAAnC,CAA4C,WAA5C,CACA,CAAApC,CAAA,CAAkB,CAAA,CAFtB,CAI8B,UAA9B,GAAIkC,CAAWG,CAAAA,UAAf,GACIpE,CAAMkE,CAAAA,KAAMC,CAAAA,WAAZ,CAAwB,YAAxB,CAAsC,SAAtC,CAAiD,WAAjD,CACA,CAAApC,CAAA,CAAkB,CAAA,CAFtB,CAIIA,EAAJ,GAEIpB,OAAQC,CAAAA,IAAR,CAAa,yFAAb,CAUA,CATAZ,CAAMkE,CAAAA,KAAMG,CAAAA,OASZ,CATsB,GAStB,CARArE,CAAMkE,CAAAA,KAAMI,CAAAA,KAQZ,CARoB,GAQpB,CAPAtE,CAAMkE,CAAAA,KAAMK,CAAAA,MAOZ,CAPqB,GAOrB,CANI,IAAKjC,CAAAA,QAMT,EANqB,IAAKA,CAAAA,QAASkC,CAAAA,aAMnC,EALI,IAAKlC,CAAAA,QAASkC,CAAAA,aAAcC,CAAAA,WAA5B,CAAwC,IAAKnC,CAAAA,QAA7C,CAKJ;AAFA,OAAO,IAAKA,CAAAA,QAEZ,CAAA,OAAO,IAAKuB,CAAAA,qBAZhB,CAeI,KAAKvB,CAAAA,QAAT,EACI,IAAKX,CAAAA,cAAL,GA3BR,CA+BA3B,EAAM0E,CAAAA,gBAAN,CAAuB,MAAvB,CAA+B,IAAKnD,CAAAA,OAApC,CACAvB,EAAM0E,CAAAA,gBAAN,CAAuB,gBAAvB,CAAyC,IAAKjD,CAAAA,iBAA9C,CACAjB,SAASkE,CAAAA,gBAAT,CAA0B,kBAA1B,CAA8C,IAAKhD,CAAAA,mBAAnD,CACAiD,OAAOD,CAAAA,gBAAP,CAAwB,QAAxB,CAAkC,IAAK/C,CAAAA,cAAvC,CAEA,KAAKiD,CAAAA,gBAAL,CAAwB9E,CAAU+E,CAAAA,cAAV,GAlQjB,sBAAW,CAACC,CAAD,EAClBnE,OAAQC,CAAAA,IAAR,CAAa,gIAAb,EAISmE,sBAAS,GAClB,GAAI,CACA,MAAO,CAAC,CAAsCC,CAApC,MAAMlF,CAAUmF,CAAAA,WAAV,CAAsB,CAAA,CAAtB,CAA8BD,EAAAA,MAD9C,CAEF,MAAOxB,CAAP,CAAU,CACR,MAAO,CAAA,CADC,EAKHyB,wBAAW,CAACC,CAAA;AAAgB,CAAA,CAAjB,EACpB,GAAI,CAACC,SAAUC,CAAAA,YAAf,CAA6B,MAAO,EAEpC,gBACoDC,gDAAAA,EAAAA,OAAQC,yBAD5D,CAOIC,CACJ,IAAI,CACIL,CAAJ,EAAgDM,CAA1B,MAAMC,CAAA,EAAoBD,EAAAA,KAA3B,CAAkCE,CAAD,EAAY,CAACA,CAAOC,CAAAA,KAArD,CAArB,GACIJ,CADJ,CACmB,MAAMJ,SAAUC,CAAAA,YAAaQ,CAAAA,YAAvB,CAAoC,CAAEC,MAAO,CAAA,CAAT,CAAgB7F,MAAO,CAAA,CAAvB,CAApC,CADzB,CADA,CAIF,MAAOwD,CAAP,CAAU,EAKZ,GAAI,CACA,MAAkCsC,CAA1B,MAAML,CAAA,EAAoBK,EAAAA,GAA3B,CAA+B,CAACJ,CAAD,CAASK,CAAT,CAAA,EAAgB,EAClDC,GAAIN,CAAOO,CAAAA,QADuC,CAElDN,MAAOD,CAAOC,CAAAA,KAAdA,GAA8B,CAAN,GAAAI,CAAA,CAAU,gBAAV,CAA6B,UAAUA,CAAV,CAAc,CAAd,EAArDJ,CAFkD,EAA/C,CADP,CAAJ,OAKU,CAEFJ,CAAJ,GACI5E,OAAQC,CAAAA,IAAR,CAAa,sGAAb,CAEA,CAAAd,CAAUoG,CAAAA,gBAAV,CAA2BX,CAA3B,CAHJ,CAFM,EA+NRY,cAAQ,GACV,IAAIC,CACJ;GAAI,CACA,GAAI,IAAK9F,CAAAA,MAAO+F,CAAAA,SAAhB,CAA2B,CACvB,GAAI,EAAE,IAAK/F,CAAAA,MAAO+F,CAAAA,SAAd,WAAmCC,YAAnC,CAAJ,CAAqD,MAAO,CAAA,CAC5DF,EAAA,CAAS,IAAK9F,CAAAA,MAAO+F,CAAAA,SAFE,CAA3B,IAIID,EAAA,CAAyCA,CAA/B,MAAM,IAAKG,CAAAA,gBAAL,EAAyBH,EAAAA,MAE7C,OAAO,OAAP,EAAkBA,EAAOI,CAAAA,cAAP,EAAA,CAAwB,CAAxB,CAA2BC,CAAAA,WAA3B,EAPlB,CAQF,MAAOjD,CAAP,CAAU,CACR,MAAO,CAAA,CADC,CARZ,OAUU,CAEF4C,CAAJ,EAAcA,CAAd,GAAyB,IAAK9F,CAAAA,MAAO+F,CAAAA,SAArC,GACI1F,OAAQC,CAAAA,IAAR,CAAa,kGAAb,CAEA,CAAAd,CAAUoG,CAAAA,gBAAV,CAA2BE,CAA3B,CAHJ,CAFM,EAUdM,SAAS,GACL,MAAO,KAAKC,CAAAA,SAGVC,iBAAW,GACT,IAAKD,CAAAA,QAAT,CACI,MAAM,IAAKE,CAAAA,YAAL,EADV,CAGI,MAAM,IAAKC,CAAAA,WAAL,GAIRA,iBAAW,GACb,GAASH,CAAL,IAAKA,CAAAA,QAAT;AAA0BI,CAAL,IAAKA,CAAAA,UAA1B,GACA,IAAKJ,CAAAA,QACD,CADY,CAAA,CACZ,CAAC,IAAKK,CAAAA,OAAN,EAAsBC,CAAL,IAAKA,CAAAA,OAF1B,EAGA,GAAI,CACA,GAAI,CAAC,MAAM,IAAKd,CAAAA,QAAL,EAAX,CAA4B,KAAM,oBAAN,CAE5B,MAAO,IAAK7F,CAAAA,MAAO+F,CAAAA,SAA0BG,CAAAA,cAAtC,EAAA,CAAuD,CAAvD,CAA0DU,CAAAA,gBAA1D,CAA2E,CAE9EC,SAAU,CAAC,CAAEC,MAAO,CAAA,CAAT,CAAD,CAFoE,CAA3E,CAHP,CAOF,MAAO5D,CAAP,CAAU,CAER,KADA,KAAKmD,CAAAA,QACCnD,CADU,CAAA,CACVA,CAAAA,CAAN,CAFQ,EAMVqD,kBAAY,GACT,IAAKF,CAAAA,QAAV,GAIA,IAAKA,CAAAA,QACL,CADgB,CAAA,CAChB,CAAA,MAAM,IAAKU,CAAAA,mBAAL,EALN,EAQJC,OAAO,GACH,IAAKhH,CAAAA,MAAOiH,CAAAA,mBAAZ,CAAgC,gBAAhC,CAAkD,IAAK9F,CAAAA,iBAAvD,CACA,KAAKnB,CAAAA,MAAOiH,CAAAA,mBAAZ,CAAgC,MAAhC,CAAwC,IAAKhG,CAAAA,OAA7C,CACAf,SAAS+G,CAAAA,mBAAT,CAA6B,kBAA7B;AAAiD,IAAK7F,CAAAA,mBAAtD,CACAiD,OAAO4C,CAAAA,mBAAP,CAA2B,QAA3B,CAAqC,IAAK5F,CAAAA,cAA1C,CAEA,KAAKoF,CAAAA,UAAL,CAAkB,CAAA,CAClB,KAAKJ,CAAAA,QAAL,CAAgB,CAAA,CAChB,KAAKa,CAAAA,IAAL,EACA1H,EAAU2H,CAAAA,kBAAV,CAA6B,IAAK7C,CAAAA,gBAAlC,CAAoD,OAApD,EAGE8C,WAAK,GACP,GAAI,IAAKX,CAAAA,UAAT,CAAqB,KAAUY,MAAJ,CAAU,6DAAV,CAAN,CACrB,GAASX,CAAL,IAAKA,CAAAA,OAAT,EAAqB,IAAKC,CAAAA,OAA1B,CAQA,GANiC,QAMpBjF,GANT2C,MAAOiD,CAAAA,QAASC,CAAAA,QAMP7F,EAJTrB,OAAQC,CAAAA,IAAR,CAAa,4EAAb,CAISoB,CADb,IAAKgF,CAAAA,OACQhF,CADE,CAAA,CACFA,CAAAA,CAATxB,QAASwB,CAAAA,MAAb,CAEA,GADA,IAAKiF,CAAAA,OACWZ;AADD,CAAA,CACCA,CAAZ,IAAK/F,CAAAA,MAAO+F,CAAAA,SAAhB,CAEI,MAAM,IAAK/F,CAAAA,MAAOwH,CAAAA,IAAZ,EAFV,KAMA,IAAI,CACA,KAAM,OAAA1B,EAAQ,WAAA2B,gCACV,EAAC,IAAKf,CAAAA,OAAV,EAAqB,IAAKC,CAAAA,OAA1B,CAEInH,CAAUoG,CAAAA,gBAAV,CAA2BE,CAA3B,CAFJ,EAKA,IAAK4B,CAAAA,eAAL,CAAqBD,CAArB,CAKA,CAJA,IAAKzH,CAAAA,MAAO+F,CAAAA,SAIZ,CAJwBD,CAIxB,CAHA,MAAM,IAAK9F,CAAAA,MAAOwH,CAAAA,IAAZ,EAGN,CAAI,IAAKnB,CAAAA,QAAT,GACI,IAAKA,CAAAA,QACL,CADgB,CAAA,CAChB,CAAA,IAAKG,CAAAA,WAAL,EAAmBmB,CAAAA,KAAnB,CAAyB,EAAA,IAAzB,CAFJ,CAVA,CAFA,CAgBF,MAAOzE,CAAP,CAAU,CACR,GAASyD,CAAL,IAAKA,CAAAA,OAAT,CAEA,KADA,KAAKD,CAAAA,OACCxD,CADS,CAAA,CACTA,CAAAA,CAAN,CAHQ,EAOhBgE,IAAI,GACA,IAAKU,CAAAA,KAAL,EACA,KAAKlB,CAAAA,OAAL,CAAe,CAAA,EAGbkB,WAAK,CAACC,CAAA,CAAwB,CAAA,CAAzB,EACP,IAAKlB,CAAAA,OAAL,CAAe,CAAA,CACf,IAAI,CAAC,IAAKD,CAAAA,OAAV,CAAmB,MAAO,CAAA,CAC1B,KAAK1G,CAAAA,MAAO4H,CAAAA,KAAZ,EAEI,KAAK5F,CAAAA,QAAT,GACI,IAAKA,CAAAA,QAAS4B,CAAAA,KAAMxB,CAAAA,OADxB;AACkC,MADlC,CAIA,YACQ,IAAKpC,CAAAA,MAAO+F,CAAAA,SAAhB,WAAqCC,YAArC,GAEIxG,CAAUoG,CAAAA,gBAAV,CAA2B,IAAK5F,CAAAA,MAAO+F,CAAAA,SAAvC,CACA,CAAA,IAAK/F,CAAAA,MAAO+F,CAAAA,SAAZ,CAAwB,IAH5B,EAOJ,IAAI8B,CAAJ,CAEI,MADAC,EAAA,EACO,CAAA,CAAA,CAGX,OAAM,IAAIC,OAAJ,CAAaC,CAAD,EAAaC,UAAA,CAAWD,CAAX,CAAoB,GAApB,CAAzB,CACN,IAAI,CAAC,IAAKrB,CAAAA,OAAV,CAAmB,MAAO,CAAA,CAC1BmB,EAAA,EACA,OAAO,CAAA,EAGLI,eAAS,CAACC,CAAD,EACPA,CAAJ,GAA6B,IAAKtH,CAAAA,gBAAlC,GACA,IAAKA,CAAAA,gBAEL,CAFwBsH,CAExB,CAAA,MAAM,IAAKpB,CAAAA,mBAAL,EAHN,EA6BSqB,sBAAS,CAClBC,CADkB,CAGlBC,CAHkB,CAYlBC,CAZkB,CAalBC,CAbkB,CAclBC,CAAA,CAAkC,CAAA,CAdhB,CAelBC,CAAA,CAAoC,CAAA,CAflB,EAiBlB,IAAIC,CAAJ,CACIC,EAA2B,CAAA,CAC3BN,EAAJ,GACI,YADJ,EACoBA,EADpB,EAEO,UAFP,EAEqBA,EAFrB,EAGO,QAHP,EAGmBA,EAHnB,EAIO,wBAJP,EAImCA,EAJnC,EAKO,0BALP;AAKqCA,CALrC,EAMO,0BANP,EAMqCA,EANrC,GASIK,CAKA,CALaL,CAAoBK,CAAAA,UAKjC,CAJAJ,CAIA,CAJWD,CAAoBC,CAAAA,QAI/B,CAHAC,CAGA,CAHSF,CAAoBE,CAAAA,MAG7B,CAFAC,CAEA,CAFyBH,CAAoBG,CAAAA,sBAE7C,EAFuE,CAAA,CAEvE,CADAC,CACA,CAD2BJ,CAAoBI,CAAAA,wBAC/C,EAD2E,CAAA,CAC3E,CAAAE,CAAA,CAA2B,CAAA,CAd/B,EAeWN,CAAJ,EAA2BC,CAA3B,EAAuCC,CAAvC,EAAiDC,CAAjD,EAA2EC,CAA3E,CACHrI,OAAQC,CAAAA,IAAR,CAAa,kFAAb,CADG,CAQHD,OAAQC,CAAAA,IAAR,CAAa,wJAAb,MAIsBiI,CAE1B,IAAI,CACA,IAAIM,CAAJ,CAEIC,CACJ,EAACP,CAAD,CAAWM,CAAX,CAAA,CAAoB,MAAMd,OAAQgB,CAAAA,GAAR,CAAY,CAClCR,CADkC,EACtB/I,CAAU+E,CAAAA,cAAV,EADsB,CAElC/E,CAAUwJ,CAAAA,UAAV,CAAqBX,CAArB,CAFkC,CAAZ,CAI1B;CAACG,CAAD,CAASM,CAAT,CAAA,CAA0BtJ,CAAUyJ,CAAAA,aAAV,CAAwBJ,CAAxB,CAA+BF,CAA/B,CAA2CH,CAA3C,CAAmDC,CAAnD,CAC1B,KAAIS,CAEJ,IAAIX,CAAJ,WAAwBY,OAAxB,CAAgC,CAC5B,OACKC,EAAL,EAEI5J,CAAU6J,CAAAA,sBAAV,CAAiCC,CAAjC,CAAiD,eAAjD,CAAkE,MAAlE,CAEJJ,EAAA,CAAqB,MAAM,IAAInB,OAAJ,CAAY,CAACC,CAAD,CAAUuB,CAAV,CAAA,GACnC,IAAIC,CAAJ,CACIC,CADJ,CAEIC,CAFJ,CAGIC,EAAqB,CAAC,CAC1BF,EAAA,CAAaG,CAADH,GACJG,CAAMC,CAAAA,IAAKnE,CAAAA,EAAf,GAAsBiE,CAAtB,GAGAL,CAAerC,CAAAA,mBAAf,CAAmC,SAAnC,CAA8CwC,CAA9C,CAGA,CAFAH,CAAerC,CAAAA,mBAAf,CAAmC,OAAnC,CAA4CyC,CAA5C,CAEA,CADAI,YAAA,CAAaN,CAAb,CACA,CAAwB,IAAxB,GAAII,CAAMC,CAAAA,IAAKA,CAAAA,IAAf,CACI7B,CAAA,CAAQ,CACJ6B,KAAMD,CAAMC,CAAAA,IAAKA,CAAAA,IADb,CAEJE,aAAcvK,CAAUwK,CAAAA,cAAV,CAAyBJ,CAAMC,CAAAA,IAAKE,CAAAA,YAApC,CAAkDpB,CAAlD,CAFV,CAAR,CADJ,CAMIY,CAAA,CAAO/J,CAAUyK,CAAAA,gBAAjB,CAZJ,EAeJP,EAAA,CAAWQ,CAADR,GACNJ,CAAerC,CAAAA,mBAAf,CAAmC,SAAnC,CAA8CwC,CAA9C,CACAH,EAAerC,CAAAA,mBAAf,CAAmC,OAAnC,CAA4CyC,CAA5C,CACAI,aAAA,CAAaN,CAAb,CAEAD,EAAA,CAAO,iBAAP;4BAAA,GAEJD,EAAelF,CAAAA,gBAAf,CAAgC,SAAhC,CAA2CqF,CAA3C,CACAH,EAAelF,CAAAA,gBAAf,CAAgC,OAAhC,CAAyCsF,CAAzC,CACAF,EAAA,CAAUvB,UAAA,CAAW,EAAA,EAAMyB,CAAA,CAAQ,SAAR,CAAjB,CAAqC,GAArC,CACV,wBAA8C,EAAGlB,QAAeA,SAChEmB,EAAA,CAAqBnK,CAAU6J,CAAAA,sBAAV,CACjBC,CADiB,CAEjB,QAFiB,CAGjBa,CAHiB,CAIjB,CAACA,CAAUN,CAAAA,IAAKO,CAAAA,MAAhB,CAJiB,EAhCE,CANC,CAAhC,IA8CIlB,EAAA,CAAqB,MAAMnB,OAAQsC,CAAAA,IAAR,CAAa,CACpC,IAAItC,OAAJ,CAAkC,CAACC,CAAD,CAAUuB,CAAV,CAAA,EAAqBlF,MAAO4D,CAAAA,UAAP,CACnD,EAAA,EAAMsB,CAAA,CAAO,wBAAP,CAD6C,CAEnD,GAFmD,CAAvD,CADoC,CAKnC,QAAA,GACG,GAAI,CACA,yBACA,IAAI,CAACe,CAAL,CAAiB,KAAM9K,EAAUyK,CAAAA,gBAAhB,CACjB,MAAO,CACHJ,KAAMS,CAAWC,CAAAA,QADd,CAEHR,aAAcvK,CAAUwK,CAAAA,cAAV,CAAyBM,CAAWP,CAAAA,YAApC,CAAkDpB,CAAlD,CAFX,CAHP,CAOF,MAAOzF,CAAP,CAAU,IACcsH,CAAAA,UACtB;GAAI,qCAAsCC,CAAAA,IAAtC,CAA2CC,CAA3C,CAAJ,CAWI,MAFAlL,EAAUmL,CAAAA,uBAEH,CAF6B,CAAA,CAE7B,CAAAnL,CAAU4I,CAAAA,SAAV,CAAoBC,CAApB,CAA4C,CAC/CM,WAAAA,CAD+C,CAE/CH,OAAAA,CAF+C,CAG/CC,uBAAAA,CAH+C,CAI/CC,yBAAAA,CAJ+C,CAA5C,CAOX,MAAM,kBAAkBgC,CAAlB,EAAN,CApBQ,EARf,CAAD,EALoC,CAAb,CAsC/B,OAAO9B,EAAA,CAA2BM,CAA3B,CAAgDA,CAAmBW,CAAAA,IA/F1E,CAgGF,MAAO3G,CAAP,CAAU,CACR,GAAI,CAACyF,CAAL,EAAmB,CAACD,CAApB,CAA8C,KAAMxF,EAAN,CAC9C,0BAC0B,CACtBqF,SAAAA,CADsB,CACZC,OAAAA,CADY,CACJC,uBAAAA,CADI,EAG1B,OAAOG,EAAA,CAA2BM,CAA3B,CAAgDA,CAAmBW,CAAAA,IANlE,CAhGZ,OAuGU,CACDT,CAAL,EACI5J,CAAU2H,CAAAA,kBAAV,CAA6BoB,CAA7B,CAAwC,OAAxC,CAFE,EAOdqC,mBAAmB,CAACC,CAAD,CAAcC,CAAd,CAA6BC,CAA7B,CAA2CC,CAAA,CAAmC,CAAA,CAA9E,EAGfxL,CAAU2H,CAAAA,kBAAV,CACI,IAAK7C,CAAAA,gBADT,CAEI,kBAFJ,CAGI,CAAEuG,IAAAA,CAAF,CAAOC,MAAAA,CAAP;AAAcC,KAAAA,CAAd,CAAoBC,wBAAAA,CAApB,CAHJ,EAOJC,gBAAgB,CAACC,CAAD,EAGZ1L,CAAU2H,CAAAA,kBAAV,CAA6B,IAAK7C,CAAAA,gBAAlC,CAAoD,eAApD,CAAqE4G,CAArE,EAMS3G,2BAAc,CAACC,CAAD,EACnBA,CAAJ,EACInE,OAAQC,CAAAA,IAAR,CAAa,qEAAb,gDAKC6K,CAAAA,KAAMC,CAAD,EAAYA,CAAOC,CAAAA,YAAP,GAOtB,IAAI,4BAAA,EAJG,iBAIH,EAJwBhH,OAIxB,EAHGiH,eAAgBC,CAAAA,mBAGnB,EAFiDC,CAA7C,MAAMF,eAAgBC,CAAAA,mBAAhB,EAAuCC,EAAAA,QAA9C,CAAuD,SAAvD,CAEH,CAAJ,CAAyB,MAAOH,EAAA,EAOhC,8BAWA;QAAA,EATOI,CAAcC,CAAAA,MAAOC,CAAAA,IAArB,CAA0B,CAAC,CAAE,MAAAC,CAAF,CAAD,CAAA,EAAe,WAAYnB,CAAAA,IAAZ,CAAiBmB,CAAjB,CAAzC,CASP,EARO,UAAWnB,CAAAA,IAAX,CAAgBgB,CAAcI,CAAAA,QAA9B,CAQP,EAJO,MAAMJ,CAAcK,CAAAA,oBAAd,CAAmC,CAAC,cAAD,CAAiB,iBAAjB,CAAnC,CACJX,CAAAA,IADI,CACC,CAAC,CAAE,aAAAY,CAAF,CAAgB,gBAAAC,CAAhB,CAAD,CAAA,EACF,MAAOvB,CAAAA,IAAP,CAAYsB,CAAZ,EAA4B,KAA5B,CADE,EACuF,EADvF,EACoCE,QAAA,CAASD,CAAT,EAA4B,IAA5B,CAFrC,CAGJrE,CAAAA,KAHI,CAGE,EAAA,EAAM,CAAA,CAHR,CAIb,CAA0C0D,CAAA,EAA1C,CAEO,IAAIC,eAAJ,CAAoB,CAAEY,QAAS,CAAC,SAAD,CAAX,CAApB,EAGHjL,OAAO,GACX,IAAKwC,CAAAA,WAAL,CAAmB,IAAK9C,CAAAA,oBAAL,CAA0B,IAAKX,CAAAA,MAA/B,CACnB,KAAKqB,CAAAA,cAAL,EACI,KAAKW,CAAAA,QAAT,GACI,IAAKA,CAAAA,QAAS4B,CAAAA,KAAMxB,CAAAA,OADxB,CACkC,EADlC,CAGA,KAAK+J,CAAAA,UAAL,GAGIhL,iBAAiB,GACrB,IAAKsC,CAAAA,WAAL,CAAmB,IAAK9C,CAAAA,oBAAL,CAA0B,IAAKX,CAAAA,MAA/B,CACnB;IAAKqB,CAAAA,cAAL,GAGID,mBAAmB,GACnBlB,QAASwB,CAAAA,MAAb,CACI,IAAKkG,CAAAA,KAAL,EADJ,CAEW,IAAKlB,CAAAA,OAFhB,EAGI,IAAKU,CAAAA,KAAL,GAIAzG,oBAAoB,CAACjB,CAAD,EAGxB,2CADmDA,eAEnD,OAAO,CACH0M,EAAGC,IAAKC,CAAAA,KAAL,EAAY5M,CAAM6M,CAAAA,UAAlB,CAA+BC,CAA/B,EAAiD,CAAjD,CADA,CAEHC,EAAGJ,IAAKC,CAAAA,KAAL,EAAY5M,CAAMgN,CAAAA,WAAlB,CAAgCF,CAAhC,EAAkD,CAAlD,CAFA,CAGHxI,MAAOwI,CAHJ,CAIHvI,OAAQuI,CAJL,CAKHG,gBAAiB,IAAK7L,CAAAA,iBALnB,CAMH8L,iBAAkB,IAAK9L,CAAAA,iBANpB,EAUHO,cAAc,GAClBqC,qBAAA,CAAsB,EAAA,GAGlB,GAAK,IAAK1B,CAAAA,QAAV,CAAA,CACA,iBAAA,eAAA,gBAAA,gBAAA,iBAAA,eAAA;aAAA,6BAAA,cAAA,MAAA,MAcA,QAAQ6K,CAAR,EACI,KAAK,MAAL,CACI,IAAAC,EAAmBP,CACnB,KAAAQ,EAAoBL,CACpB,MACJ,MAAK,MAAL,CACII,CAAA,CAAmBE,CACnBD,EAAA,CAAoBE,CACpB,MACJ,SACI,CAAuB,OAAnB,GAAAJ,CAAA,CACEK,CADF,CACqBC,CADrB,CAEED,CAFF,CAEqBC,CAFzB,GAQIJ,CACA,CADoBE,CACpB,CAAAH,CAAA,CAAmBC,CAAnB,CAAuCG,CAT3C,GAWIJ,CACA,CADmBE,CACnB,CAAAD,CAAA,CAAoBD,CAApB,CAAuCI,CAZ3C,CAcA,CAAuB,YAAvB,GAAIL,CAAJ,GAEIC,CACA,CADmBT,IAAKe,CAAAA,GAAL,CAASN,CAAT,CAA2BP,CAA3B,CACnB,CAAAQ,CAAA,CAAoBV,IAAKe,CAAAA,GAAL,CAASL,CAAT,CAA4BL,CAA5B,CAHxB,CAxBR,CAkCA,OAAaW,8BAA+C7H,CAAAA,KAAKd,EAAQe,KACrE,MAAM6H,EAAcC,UAAA,CAAW7I,CAAX,CACpB,OAAOA,EAAO8I,CAAAA,QAAP,CAAgB,GAAhB,CAAA,EACC/H,CAAD,CAAuCwH,CAAvC,CAAuDF,CAAvD,CAAKC,CAAL,CAAoBF,CADpB,EAC4EQ,CAD5E,CAC0F,GAD1F,CAEDA,oFAMV,kDAGApL,EAAa8B,CAAAA,KAAb;AAAqB,GAAGyJ,CAAH,CAAiBlB,CAAjB,CAA8BO,CAA9B,IACrB5K,EAAa+B,CAAAA,MAAb,CAAsB,GAAGyJ,CAAH,CAAkBhB,CAAlB,CAAgCK,CAAhC,IACtB7K,EAAayL,CAAAA,GAAb,CAAmB,GAAGC,CAAH,CAAcP,CAAd,CAAuBQ,CAAvB,CAAiCnB,CAAjC,CAA+CK,CAA/C,6CAEnB7K,EAAa4L,CAAAA,IAAb,CAAoB,GAAGC,CAAH,EACbC,CAAA,CAAkBhB,CAAlB,CAAiCiB,CAAjC,CAA0CnB,CAA1C,CAA6DmB,CADhD,GAEbD,CAAA,CAAkBzB,CAAlB,CAA+B2B,CAA/B,CAAyCT,CAAzC,CAAuDS,CAF1C,EAEqD3B,CAFrD,CAEkEO,CAFlE,IAIpB5K,EAAaU,CAAAA,SAAb,CAAyBlD,CAAMkE,CAAAA,KAAMhB,CAAAA,SAtErC,EAHJ,EA6EWoH,qBAAc,CACzBmE,CADyB,CAEzBxF,CAFyB,EAIzB,GAAI,CAACA,CAAL,CAAiB,MAAOwF,EACxB,aAAA,SAAA,8BAGMxF,CAAW3E,CAAAA,MAAQ2E,CAAWgE,CAAAA,gBAC9B,iCAEAhE,CAAW1E,CAAAA,OAAS0E,CAAWiE,CAAAA,iBAC/B,CACN,KAAK,KAAL,KAAA,CACIwB,CAAMhC,CAAAA,CACN,CADUgC,CAAMhC,CAAAA,CAChB,CADoBiC,CACpB,CADmCC,CACnC,CAAAF,CAAM3B,CAAAA,CAAN,CAAU2B,CAAM3B,CAAAA,CAAhB,CAAoB8B,CAApB,CAAmCC,CAEvC,OAAOL,GAGHhC,UAAU,GACV,CAAC,IAAKzF,CAAAA,OAAV,EAAqB,IAAK1G,CAAAA,MAAOyO,CAAAA,MAAjC,EAA2C,IAAKzO,CAAAA,MAAO0O,CAAAA,KAAvD,EASA;YAFM,IAAK1O,CAAAA,MAAO2O,CAAAA,yBAA0BzN,CAAAA,IAAtC,CAA2C,IAAKlB,CAAAA,MAAhD,EACA0D,qBACN,EAAa,OAAA,GACT,GAAI,EAA0B,CAA1B,EAAA,IAAK1D,CAAAA,MAAO4O,CAAAA,UAAZ,CAAJ,CAAA,CASA,wCAAA,8BAEIC,EAAJ,CAAwBC,CAAxB,EACI,MAAM,IAAI/G,OAAJ,CAAaC,CAAD,EAAaC,UAAA,CAAWD,CAAX,CAAoB8G,CAApB,CAA8CD,CAA9C,CAAzB,CAGV,KAAKE,CAAAA,kBAAL,CAA0BC,IAAKC,CAAAA,GAAL,EAG1B,IAAI,CACA,IAAAC,EAAS,MAAM1P,CAAU4I,CAAAA,SAAV,CAAoB,IAAKpI,CAAAA,MAAzB,CAAiC,CAC5C2I,WAAY,IAAKlF,CAAAA,WAD2B,CAE5C8E,SAAU,IAAKjE,CAAAA,gBAF6B,CAG5CkE,OAAQ,IAAKvI,CAAAA,OAH+B,CAAjC,CADf,CAMF,MAAOiK,CAAP,CAAc,CACZ,GAAI,CAAC,IAAKxD,CAAAA,OAAV,CAAmB,MACnB,KAAKlG,CAAAA,cAAL,CAAoB0J,CAApB,CAFY,CAKFS,CAAVnL,CAAUmL,CAAAA,uBAAd,EAA2C,MAAM,IAAKrG,CAAAA,gBAAtD;AAAkF6E,MAAlF,GAEI,IAAK7E,CAAAA,gBAFT,CAE4B9E,CAAU+E,CAAAA,cAAV,EAF5B,CAKI2K,EAAJ,EACQ,IAAK9O,CAAAA,SAAT,CACI,IAAKA,CAAAA,SAAL,CAAe8O,CAAf,CADJ,CAEW,IAAK3O,CAAAA,eAFhB,EAGI,IAAKA,CAAAA,eAAL,CAAqB2O,CAAOrF,CAAAA,IAA5B,CAGJ,CAAI,IAAKtG,CAAAA,qBAAT,GACIuG,YAAA,CAAa,IAAKqF,CAAAA,mCAAlB,CAWA,CAVA,IAAKA,CAAAA,mCAUL,CAV2CC,IAAAA,EAU3C,CATA,IAAK7L,CAAAA,qBAAsB8L,CAAAA,YAA3B,CACI,SADJ,CAEI,GAAG,IAAK5L,CAAAA,WAAY2I,CAAAA,CAApB,EAAyB,CAAzB,GAFJ,CAGU,GAAG,IAAK3I,CAAAA,WAAYgJ,CAAAA,CAApB,EAAyB,CAAzB,GAHV,CAIU,GAAG,IAAKhJ,CAAAA,WAAYO,CAAAA,KAApB,EAA6B,IAAKhE,CAAAA,MAAOuM,CAAAA,UAAzC,GAJV,CAKU,GAAG,IAAK9I,CAAAA,WAAYQ,CAAAA,MAApB,EAA8B,IAAKjE,CAAAA,MAAO0M,CAAAA,WAA1C,EALV,CASA,6CADQ2C,CAAAA,YAAR,CAAqB,QAArB;AAA+BH,CAAOnF,CAAAA,YAAavE,CAAAA,GAApB,CAAwB,CAAC,CAAC,EAAA4G,CAAD,CAAI,EAAAK,CAAJ,CAAD,CAAA,EAAY,GAAGL,CAAH,IAAQK,CAAR,EAApC,CAAiD6C,CAAAA,IAAjD,CAAsD,GAAtD,CAA/B,CACA,CAAA,IAAK/L,CAAAA,qBAAsBK,CAAAA,KAAMxB,CAAAA,OAAjC,CAA2C,EAZ/C,CAPJ,EAqBW,IAAKmB,CAAAA,qBArBhB,EAqByC,CAAC,IAAK4L,CAAAA,mCArB/C,GAuBI,IAAKA,CAAAA,mCAvBT,CAuB+ClH,UAAA,CACvC,EAAA,EAAM,IAAK1E,CAAAA,qBAAuBK,CAAAA,KAAMxB,CAAAA,OAAxC,CAAkD,MADX,CAEvC,GAFuC,CAvB/C,CAlCA,CAKI,IAAK+J,CAAAA,UAAL,GANR,EAoEI3L,cAAc,CAAC0J,CAAD,EAEdA,CAAJ,GAAc1K,CAAUyK,CAAAA,gBAAxB,EACA5J,OAAQkP,CAAAA,GAAR,CAAYrF,CAAZ,EAGUjE,sBAAgB,GAC1B,GAAI,CAACpB,SAAUC,CAAAA,YAAf,CAA6B,KAAM,mBAAN,CAE7B,yDACM;AACA,UAFN,KAIId,MAAO,CAAEoJ,IAAK,IAAP,GACR,CACCpJ,MAAO,CAAEoJ,IAAK,GAAP,CADR,EAEA,GAPH,SAQ0DoC,oBAAiCA,EAAY,CACnG,CAACC,CAAD,EAAkB,CAAEC,MAAO,IAAK7O,CAAAA,gBAAd,CADiF,GAIvG,KAAK,KAAL,SAAmD,KAAnD,CACI,GAAI,CACA,iDAAyDnB,MAAOiQ,EAAapK,MAAO,CAAA,GAApF,4BAKQoK,CAAYlI,CAAAA,UAAZ,CACE,IAAK5G,CAAAA,gBADP,CAE6B,aAA1B,GAAA,IAAKA,CAAAA,gBAAL,CACG,MADH,CAEG,cAGd,OAAO,CAAEiF,OAAAA,CAAF,CAAU2B,WAAAA,CAAV,CAbP,CAcF,MAAOvE,CAAP,CAAU,EAGhB,KAAM,mBAAN,EAGU6D,yBAAmB,GAG7B,uCAEA,EAAe6I,CAAAA,CAAf,EAA6B,IAAKlJ,CAAAA,OAAlC,EACA,MAAM,IAAKU,CAAAA,KAAL,GAGKxB,uBAAgB,CAACE,CAAD,EAC3B,IAAK,KAAL,iBAAA,CACI+J,CAAM3I,CAAAA,IAAN,EACA;AAAApB,CAAOgK,CAAAA,WAAP,CAAmBD,CAAnB,EAIAnI,eAAe,CAACD,CAAD,EAGnB,IAAKzH,CAAAA,MAAO4D,CAAAA,KAAMhB,CAAAA,SAAlB,CAA8B,SAA9B,iBAAA,EAAwD,IAGpDmN,cAAc,CAACC,CAAD,EAElB,MAAA,EAAA,sBAAA,EAEO,wBAAyBvF,CAAAA,IAAzB,CAA8BwF,CAAW5K,CAAAA,KAAzC,CAAA,CACD,aADC,CAED,kBAAmBoF,CAAAA,IAAnB,CAAwBwF,CAAW5K,CAAAA,KAAnC,CAAA,CACI,MADJ,CAEI,IANV,CAAwB,KASb4D,oBAAa,CACxBJ,CADwB,CAGxBF,CAHwB,CAIxBH,CAJwB,CAKxBC,CAAA,CAAwB,CAAA,CALA,EAOxBD,CAAA,CAASA,CAAT,EAAmBtI,QAASC,CAAAA,aAAT,CAAuB,QAAvB,CACnB,mBAAA,eAAA,cAGMwI,CAAW3E,CAAAA,MACV6E,CAA2B0D,CAAAA,YAAc1D,CAAM7E,CAAAA,KAJtD,eAMM2E,CAAW1E,CAAAA,OACV4E,CAA2B6D,CAAAA,aAAe7D,CAAM5E,CAAAA,MAElDwE,EAAL,IAYI,sBAVME,CAAWgE,CAAAA,gBACXuD,CASN,EAAA;AAPMvH,CAAWiE,CAAAA,iBACXuD,CAMN,CAHI3H,CAAOxE,CAAAA,KAGX,GAHqBoM,CAGrB,GAFI5H,CAAOxE,CAAAA,KAEX,CAFmBoM,CAEnB,EAAI5H,CAAOvE,CAAAA,MAAX,GAAsBoM,CAAtB,GACI7H,CAAOvE,CAAAA,MADX,CACoBoM,CADpB,CAZJ,sBAiBsC,CAAEC,MAAO,CAAA,CAAT,EACtCC,EAAQC,CAAAA,qBAAR,CAAgC,CAAA,CAChCD,EAAQE,CAAAA,SAAR,CACI5H,CADJ,CAEI6H,CAFJ,CAEiBC,CAFjB,CAE8BT,CAF9B,CAE+CC,CAF/C,CAGI,CAHJ,CAGO,CAHP,CAGU3H,CAAOxE,CAAAA,KAHjB,CAGwBwE,CAAOvE,CAAAA,MAH/B,CAKA,OAAO,CAACuE,CAAD,CAAS+H,CAAT,EAGUvH,uBAAU,CAC3BX,CAD2B,EAK3B,GAAIA,CAAJ,WAAsCuI,MAAtC,CAEI,MADA,OAAMpR,CAAUqR,CAAAA,eAAV,CAA0BxI,CAA1B,CACCA,CAAAA,CACJ,IAAIA,CAAJ,WAAsCyI,iBAAtC,EACAzI,CADA,WACkC0I,kBADlC,EAEA1I,CAFA,WAEkC2I,gBAFlC,EAGA,iBAHA,EAGqB3M,OAHrB,EAG+BgE,CAH/B,WAGiE4I,gBAHjE,EAIA,aAJA,EAIiB5M,OAJjB,EAI2BgE,CAJ3B,WAI6D6I,YAJ7D,CAKH,MAAO7I,EACJ,IAAIA,CAAJ;AAAsC8I,IAAtC,EAA8C9I,CAA9C,WAAgF+I,KAAhF,EACA/I,CADA,WACkCgJ,IADlC,EAC2E,QAD3E,GACyC,MAAOhJ,EADhD,CACqF,CACxF,eAEIQ,EAAMyI,CAAAA,GAAN,CADAjJ,CAAJ,WAAsC8I,KAAtC,EAA8C9I,CAA9C,WAAgF+I,KAAhF,CACgBC,GAAIE,CAAAA,eAAJ,CAAoBlJ,CAApB,CADhB,CAGgBA,CAAuBmJ,CAAAA,QAAvB,EAEhB,IAAI,CAEA,MADA,OAAMhS,CAAUqR,CAAAA,eAAV,CAA0BhI,CAA1B,CACCA,CAAAA,CAFP,CAAJ,OAGU,CACN,CAAIR,CAAJ,WAAsC8I,KAAtC,EAA8C9I,CAA9C,WAAgF+I,KAAhF,GACIC,GAAII,CAAAA,eAAJ,CAAoB5I,CAAMyI,CAAAA,GAA1B,CAFE,CAV8E,CADrF,IAiBH,MAAM,yBAAN,EAIaT,4BAAe,CAAChI,CAAD,EAC5BA,CAAM6I,CAAAA,QAAV,EAA6C,CAA7C,GAAsB7I,CAAM8I,CAAAA,YAA5B,EACA,MAAM,IAAI5J,OAAJ,CAAkB,CAACC,CAAD,CAAUuB,CAAV,CAAA,GACpB,UACIV,CAAM5B,CAAAA,mBAAN,CAA0B,MAA1B,CAAkC2K,CAAlC,CACA/I,EAAM5B,CAAAA,mBAAN,CAA0B,OAA1B,CAAmC2K,CAAnC,CACIhI,EAAJ,WAAqBiI,WAArB;AACItI,CAAA,CAAO,kBAAP,CADJ,CAGIvB,CAAA,GAGRa,EAAMzE,CAAAA,gBAAN,CAAuB,MAAvB,CAA+BwN,CAA/B,CACA/I,EAAMzE,CAAAA,gBAAN,CAAuB,OAAvB,CAAgCwN,CAAhC,EAXE,EAeWzK,+BAAkB,CACnC2K,CADmC,CAEnCC,CAFmC,CAGnClI,CAHmC,CAInCmI,CAJmC,EAMnC,MAAOxS,EAAU6J,CAAAA,sBAAV,CAAiC,MAAMyI,CAAvC,CAAkEC,CAAlE,CAAwElI,CAAxE,CAA8EmI,CAA9E,EAII3I,6BAAsB,CACjCd,CADiC,CAEjCwJ,CAFiC,CAGjClI,CAHiC,CAIjCmI,CAJiC,EAMjC,GAAI,EAAEzJ,CAAF,WAAsBY,OAAtB,CAAJ,CAAmC,MAAO,CAAC,CAC3C,2BACAZ,EAAS0J,CAAAA,WAAT,CAAqB,CACjBvM,GAAAA,CADiB,CAEjBqM,KAAAA,CAFiB,CAGjBlI,KAAAA,CAHiB,CAArB,CAIGmI,CAJH,CAKA,OAAOtM,GAxiCf,CACoBlG,qBAAA,CAAsB,GACtBA,mBAAA,CAAmB,kBACpBA,0BAAA,CAA0B,CAAA,CAC1BA,mBAAA,CAAmB;"}