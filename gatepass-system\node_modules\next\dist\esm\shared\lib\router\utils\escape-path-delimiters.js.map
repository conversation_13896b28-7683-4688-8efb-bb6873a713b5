{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/escape-path-delimiters.ts"], "sourcesContent": ["// escape delimiters used by path-to-regexp\nexport default function escapePathDelimiters(\n  segment: string,\n  escapeEncoded?: boolean\n): string {\n  return segment.replace(\n    new RegExp(`([/#?]${escapeEncoded ? '|%(2f|23|3f|5c)' : ''})`, 'gi'),\n    (char: string) => encodeURIComponent(char)\n  )\n}\n"], "names": ["escapePathDelimiters", "segment", "escapeEncoded", "replace", "RegExp", "char", "encodeURIComponent"], "mappings": "AAAA,2CAA2C;AAC3C,eAAe,SAASA,qBACtBC,OAAe,EACfC,aAAuB;IAEvB,OAAOD,QAAQE,OAAO,CACpB,IAAIC,OAAO,AAAC,WAAQF,CAAAA,gBAAgB,oBAAoB,EAAC,IAAE,KAAI,OAC/D,CAACG,OAAiBC,mBAAmBD;AAEzC", "ignoreList": [0]}