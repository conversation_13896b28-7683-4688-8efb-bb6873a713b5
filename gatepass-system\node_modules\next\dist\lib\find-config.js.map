{"version": 3, "sources": ["../../src/lib/find-config.ts"], "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\nimport { readFile } from 'fs/promises'\nimport JSON5 from 'next/dist/compiled/json5'\nimport { pathToFileURL } from 'url'\n\ntype RecursivePartial<T> = {\n  [P in keyof T]?: RecursivePartial<T[P]>\n}\n\nexport function findConfigPath(\n  dir: string,\n  key: string\n): Promise<string | undefined> {\n  // If we didn't find the configuration in `package.json`, we should look for\n  // known filenames.\n  return findUp(\n    [\n      `.${key}rc.json`,\n      `${key}.config.json`,\n      `.${key}rc.js`,\n      `${key}.config.js`,\n      `${key}.config.mjs`,\n      `${key}.config.cjs`,\n    ],\n    {\n      cwd: dir,\n    }\n  )\n}\n\n// We'll allow configuration to be typed, but we force everything provided to\n// become optional. We do not perform any schema validation. We should maybe\n// force all the types to be `unknown` as well.\nexport async function findConfig<T>(\n  directory: string,\n  key: string,\n  _returnFile?: boolean\n): Promise<RecursivePartial<T> | null> {\n  // `package.json` configuration always wins. Let's check that first.\n  const packageJsonPath = await findUp('package.json', { cwd: directory })\n  let isESM = false\n\n  if (packageJsonPath) {\n    try {\n      const packageJsonStr = await readFile(packageJsonPath, 'utf8')\n      const packageJson = JSON.parse(packageJsonStr) as {\n        [key: string]: string\n      }\n\n      if (typeof packageJson !== 'object') {\n        throw new Error() // Stop processing and continue\n      }\n\n      if (packageJson.type === 'module') {\n        isESM = true\n      }\n\n      if (packageJson[key] != null && typeof packageJson[key] === 'object') {\n        return packageJson[key]\n      }\n    } catch {\n      // Ignore error and continue\n    }\n  }\n\n  const filePath = await findConfigPath(directory, key)\n\n  const esmImport = (path: string) => {\n    // Skip mapping to absolute url with pathToFileURL on windows if it's jest\n    // https://github.com/nodejs/node/issues/31710#issuecomment-587345749\n    if (process.platform === 'win32' && !process.env.JEST_WORKER_ID) {\n      // on windows import(\"C:\\\\path\\\\to\\\\file\") is not valid, so we need to\n      // use file:// URLs\n      return import(pathToFileURL(path).toString())\n    } else {\n      return import(path)\n    }\n  }\n\n  if (filePath) {\n    if (filePath.endsWith('.js')) {\n      if (isESM) {\n        return (await esmImport(filePath)).default\n      } else {\n        return require(filePath)\n      }\n    } else if (filePath.endsWith('.mjs')) {\n      return (await esmImport(filePath)).default\n    } else if (filePath.endsWith('.cjs')) {\n      return require(filePath)\n    }\n\n    // We load JSON contents with JSON5 to allow users to comment in their\n    // configuration file. This pattern was popularized by TypeScript.\n    const fileContents = await readFile(filePath, 'utf8')\n    return JSON5.parse(fileContents)\n  }\n\n  return null\n}\n"], "names": ["findConfig", "findConfigPath", "dir", "key", "findUp", "cwd", "directory", "_returnFile", "packageJsonPath", "isESM", "packageJsonStr", "readFile", "packageJson", "JSON", "parse", "Error", "type", "filePath", "esmImport", "path", "process", "platform", "env", "JEST_WORKER_ID", "pathToFileURL", "toString", "endsWith", "default", "require", "fileContents", "JSON5"], "mappings": ";;;;;;;;;;;;;;;IAiCsBA,UAAU;eAAVA;;IAxBNC,cAAc;eAAdA;;;+DATG;0BACM;8DACP;qBACY;;;;;;AAMvB,SAASA,eACdC,GAAW,EACXC,GAAW;IAEX,4EAA4E;IAC5E,mBAAmB;IACnB,OAAOC,IAAAA,eAAM,EACX;QACE,CAAC,CAAC,EAAED,IAAI,OAAO,CAAC;QAChB,GAAGA,IAAI,YAAY,CAAC;QACpB,CAAC,CAAC,EAAEA,IAAI,KAAK,CAAC;QACd,GAAGA,IAAI,UAAU,CAAC;QAClB,GAAGA,IAAI,WAAW,CAAC;QACnB,GAAGA,IAAI,WAAW,CAAC;KACpB,EACD;QACEE,KAAKH;IACP;AAEJ;AAKO,eAAeF,WACpBM,SAAiB,EACjBH,GAAW,EACXI,WAAqB;IAErB,oEAAoE;IACpE,MAAMC,kBAAkB,MAAMJ,IAAAA,eAAM,EAAC,gBAAgB;QAAEC,KAAKC;IAAU;IACtE,IAAIG,QAAQ;IAEZ,IAAID,iBAAiB;QACnB,IAAI;YACF,MAAME,iBAAiB,MAAMC,IAAAA,kBAAQ,EAACH,iBAAiB;YACvD,MAAMI,cAAcC,KAAKC,KAAK,CAACJ;YAI/B,IAAI,OAAOE,gBAAgB,UAAU;gBACnC,MAAM,IAAIG,QAAQ,+BAA+B;;YACnD;YAEA,IAAIH,YAAYI,IAAI,KAAK,UAAU;gBACjCP,QAAQ;YACV;YAEA,IAAIG,WAAW,CAACT,IAAI,IAAI,QAAQ,OAAOS,WAAW,CAACT,IAAI,KAAK,UAAU;gBACpE,OAAOS,WAAW,CAACT,IAAI;YACzB;QACF,EAAE,OAAM;QACN,4BAA4B;QAC9B;IACF;IAEA,MAAMc,WAAW,MAAMhB,eAAeK,WAAWH;IAEjD,MAAMe,YAAY,CAACC;QACjB,0EAA0E;QAC1E,qEAAqE;QACrE,IAAIC,QAAQC,QAAQ,KAAK,WAAW,CAACD,QAAQE,GAAG,CAACC,cAAc,EAAE;YAC/D,sEAAsE;YACtE,mBAAmB;YACnB,OAAO,MAAM,CAACC,IAAAA,kBAAa,EAACL,MAAMM,QAAQ;QAC5C,OAAO;YACL,OAAO,MAAM,CAACN;QAChB;IACF;IAEA,IAAIF,UAAU;QACZ,IAAIA,SAASS,QAAQ,CAAC,QAAQ;YAC5B,IAAIjB,OAAO;gBACT,OAAO,AAAC,CAAA,MAAMS,UAAUD,SAAQ,EAAGU,OAAO;YAC5C,OAAO;gBACL,OAAOC,QAAQX;YACjB;QACF,OAAO,IAAIA,SAASS,QAAQ,CAAC,SAAS;YACpC,OAAO,AAAC,CAAA,MAAMR,UAAUD,SAAQ,EAAGU,OAAO;QAC5C,OAAO,IAAIV,SAASS,QAAQ,CAAC,SAAS;YACpC,OAAOE,QAAQX;QACjB;QAEA,sEAAsE;QACtE,kEAAkE;QAClE,MAAMY,eAAe,MAAMlB,IAAAA,kBAAQ,EAACM,UAAU;QAC9C,OAAOa,cAAK,CAAChB,KAAK,CAACe;IACrB;IAEA,OAAO;AACT", "ignoreList": [0]}