import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';
import QRCode from 'qrcode';

const prisma = new PrismaClient();

function verifyToken(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('No token provided');
  }

  const token = authHeader.substring(7);
  return jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const decoded = verifyToken(request);
    const resolvedParams = await params;
    const gatepassId = resolvedParams.id;

    // Get gatepass details
    const gatepass = await prisma.gatepass.findUnique({
      where: { id: gatepassId },
      include: {
        student: {
          include: {
            college: true,
            department: true,
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!gatepass) {
      return NextResponse.json(
        { error: 'Gatepass not found' },
        { status: 404 }
      );
    }

    // Update printed timestamp
    await prisma.gatepass.update({
      where: { id: gatepassId },
      data: { printedAt: new Date() },
    });

    // Generate QR code
    const qrCodeDataURL = await QRCode.toDataURL(gatepassId, {
      width: 300,
      margin: 2,
    });

    // Generate simple HTML for printing (QR code + student name only)
    const html = generateSimpleHTML(gatepass, qrCodeDataURL);

    // Return HTML response for printing
    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
        'Content-Disposition': `inline; filename="simple-gatepass-${gatepassId}.html"`,
      },
    });
  } catch (error) {
    console.error('Error generating simple print:', error);
    if (error instanceof Error && error.message === 'No token provided') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function generateSimpleHTML(gatepass: any, qrCodeDataURL: string): string {
  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Simple Gatepass</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
        }
        .qr-container {
            margin: 20px 0;
        }
        .qr-code {
            width: 250px;
            height: 250px;
            margin: 0 auto;
        }
        .student-name {
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
            color: #333;
        }
        .college-name {
            font-size: 16px;
            color: #666;
            margin: 10px 0;
        }
        .gatepass-id {
            font-size: 12px;
            color: #999;
            margin: 10px 0;
            word-break: break-all;
        }
        .print-btn {
            background-color: #3B82F6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
        }
        .print-btn:hover {
            background-color: #2563EB;
        }
    </style>
</head>
<body>
    <div class="no-print">
        <button class="print-btn" onclick="window.print()">Print Simple Gatepass</button>
    </div>
    
    <div class="qr-container">
        <img src="${qrCodeDataURL}" alt="QR Code" class="qr-code" />
    </div>
    
    <div class="student-name">${gatepass.student.name}</div>
    
    <div class="college-name">${gatepass.student.college.name}</div>
    
    <div class="gatepass-id">ID: ${gatepass.id}</div>

    <script>
        // Auto-trigger print dialog after a short delay
        window.addEventListener('load', function() {
            setTimeout(() => {
                window.print();
            }, 1000);
        });
    </script>
</body>
</html>
  `;
}
