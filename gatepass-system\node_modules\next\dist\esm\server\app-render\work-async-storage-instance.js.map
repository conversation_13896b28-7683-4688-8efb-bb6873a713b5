{"version": 3, "sources": ["../../../src/server/app-render/work-async-storage-instance.ts"], "sourcesContent": ["import type { WorkAsyncStorage } from './work-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const workAsyncStorageInstance: WorkAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["createAsyncLocalStorage", "workAsyncStorageInstance"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,wBAAuB;AAE/D,OAAO,MAAMC,2BACXD,0BAAyB", "ignoreList": [0]}