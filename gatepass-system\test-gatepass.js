const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testGatepass() {
  try {
    console.log('Checking for gatepass: e711233f-2459-4139-9a7c-432d8f6c9d48');
    
    const gatepass = await prisma.gatepass.findUnique({
      where: { id: 'e711233f-2459-4139-9a7c-432d8f6c9d48' },
      include: {
        student: {
          include: {
            college: true,
            department: true,
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    if (gatepass) {
      console.log('✅ Gatepass found!');
      console.log('Student:', gatepass.student.name);
      console.log('College:', gatepass.student.college.name);
      console.log('Status:', gatepass.status);
      console.log('Created by:', gatepass.user.name);
    } else {
      console.log('❌ Gatepass not found');
      
      // Let's check what gatepasses exist
      const allGatepasses = await prisma.gatepass.findMany({
        take: 5,
        include: {
          student: true,
        },
      });
      
      console.log('\nExisting gatepasses:');
      allGatepasses.forEach(gp => {
        console.log(`- ${gp.id} (${gp.student.name})`);
      });
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testGatepass();
