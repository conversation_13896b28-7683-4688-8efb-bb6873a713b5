{"version": 3, "sources": ["../../../src/build/next-config-ts/transpile-config.ts"], "sourcesContent": ["import type { Options as SWCOptions } from '@swc/core'\nimport type { CompilerOptions } from 'typescript'\n\nimport { resolve } from 'node:path'\nimport { readFile } from 'node:fs/promises'\nimport { deregisterHook, registerHook, requireFromString } from './require-hook'\nimport { warn } from '../output/log'\nimport { installDependencies } from '../../lib/install-dependencies'\n\nfunction resolveSWCOptions(\n  cwd: string,\n  compilerOptions: CompilerOptions\n): SWCOptions {\n  const resolvedBaseUrl = resolve(cwd, compilerOptions.baseUrl ?? '.')\n  return {\n    jsc: {\n      target: 'es5',\n      parser: {\n        syntax: 'typescript',\n      },\n      paths: compilerOptions.paths,\n      baseUrl: resolvedBaseUrl,\n    },\n    module: {\n      type: 'commonjs',\n    },\n    isModule: 'unknown',\n  } satisfies SWCOptions\n}\n\n// Ported from next/src/lib/verify-typescript-setup.ts\n// Although this overlaps with the later `verifyTypeScriptSetup`,\n// it is acceptable since the time difference in the worst case is trivial,\n// as we are only preparing to install the dependencies once more.\nasync function verifyTypeScriptSetup(cwd: string, configFileName: string) {\n  try {\n    // Quick module check.\n    require.resolve('typescript', { paths: [cwd] })\n  } catch (error) {\n    if (\n      error &&\n      typeof error === 'object' &&\n      'code' in error &&\n      error.code === 'MODULE_NOT_FOUND'\n    ) {\n      warn(\n        `Installing TypeScript as it was not found while loading \"${configFileName}\".`\n      )\n\n      await installDependencies(cwd, [{ pkg: 'typescript' }], true).catch(\n        (err) => {\n          if (err && typeof err === 'object' && 'command' in err) {\n            console.error(\n              `Failed to install TypeScript, please install it manually to continue:\\n` +\n                (err as any).command +\n                '\\n'\n            )\n          }\n          throw err\n        }\n      )\n    }\n  }\n}\n\nasync function getTsConfig(cwd: string): Promise<CompilerOptions> {\n  const ts: typeof import('typescript') = require(\n    require.resolve('typescript', { paths: [cwd] })\n  )\n\n  // NOTE: This doesn't fully cover the edge case for setting\n  // \"typescript.tsconfigPath\" in next config which is currently\n  // a restriction.\n  const tsConfigPath = ts.findConfigFile(\n    cwd,\n    ts.sys.fileExists,\n    'tsconfig.json'\n  )\n\n  if (!tsConfigPath) {\n    // It is ok to not return ts.getDefaultCompilerOptions() because\n    // we are only lookfing for paths and baseUrl from tsConfig.\n    return {}\n  }\n\n  const configFile = ts.readConfigFile(tsConfigPath, ts.sys.readFile)\n  const parsedCommandLine = ts.parseJsonConfigFileContent(\n    configFile.config,\n    ts.sys,\n    cwd\n  )\n\n  return parsedCommandLine.options\n}\n\nexport async function transpileConfig({\n  nextConfigPath,\n  configFileName,\n  cwd,\n}: {\n  nextConfigPath: string\n  configFileName: string\n  cwd: string\n}) {\n  let hasRequire = false\n  try {\n    // Ensure TypeScript is installed to use the API.\n    await verifyTypeScriptSetup(cwd, configFileName)\n\n    const compilerOptions = await getTsConfig(cwd)\n    const swcOptions = resolveSWCOptions(cwd, compilerOptions)\n\n    const nextConfigString = await readFile(nextConfigPath, 'utf8')\n    // lazy require swc since it loads React before even setting NODE_ENV\n    // resulting loading Development React on Production\n    const { transform } = require('../swc') as typeof import('../swc')\n    const { code } = await transform(nextConfigString, swcOptions)\n\n    // register require hook only if require exists\n    if (code.includes('require(')) {\n      registerHook(swcOptions)\n      hasRequire = true\n    }\n\n    // filename & extension don't matter here\n    return requireFromString(code, resolve(cwd, 'next.config.compiled.js'))\n  } catch (error) {\n    throw error\n  } finally {\n    if (hasRequire) {\n      deregisterHook()\n    }\n  }\n}\n"], "names": ["resolve", "readFile", "deregisterHook", "registerHook", "requireFromString", "warn", "installDependencies", "resolveSWCOptions", "cwd", "compilerOptions", "resolvedBaseUrl", "baseUrl", "jsc", "target", "parser", "syntax", "paths", "module", "type", "isModule", "verifyTypeScriptSetup", "configFileName", "require", "error", "code", "pkg", "catch", "err", "console", "command", "getTsConfig", "ts", "tsConfigPath", "findConfigFile", "sys", "fileExists", "configFile", "readConfigFile", "parsedCommandLine", "parseJsonConfigFileContent", "config", "options", "transpileConfig", "nextConfigPath", "hasRequire", "swcOptions", "nextConfigString", "transform", "includes"], "mappings": "AAGA,SAASA,OAAO,QAAQ,YAAW;AACnC,SAASC,QAAQ,QAAQ,mBAAkB;AAC3C,SAASC,cAAc,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,iBAAgB;AAChF,SAASC,IAAI,QAAQ,gBAAe;AACpC,SAASC,mBAAmB,QAAQ,iCAAgC;AAEpE,SAASC,kBACPC,GAAW,EACXC,eAAgC;IAEhC,MAAMC,kBAAkBV,QAAQQ,KAAKC,gBAAgBE,OAAO,IAAI;IAChE,OAAO;QACLC,KAAK;YACHC,QAAQ;YACRC,QAAQ;gBACNC,QAAQ;YACV;YACAC,OAAOP,gBAAgBO,KAAK;YAC5BL,SAASD;QACX;QACAO,QAAQ;YACNC,MAAM;QACR;QACAC,UAAU;IACZ;AACF;AAEA,sDAAsD;AACtD,iEAAiE;AACjE,2EAA2E;AAC3E,kEAAkE;AAClE,eAAeC,sBAAsBZ,GAAW,EAAEa,cAAsB;IACtE,IAAI;QACF,sBAAsB;QACtBC,QAAQtB,OAAO,CAAC,cAAc;YAAEgB,OAAO;gBAACR;aAAI;QAAC;IAC/C,EAAE,OAAOe,OAAO;QACd,IACEA,SACA,OAAOA,UAAU,YACjB,UAAUA,SACVA,MAAMC,IAAI,KAAK,oBACf;YACAnB,KACE,CAAC,yDAAyD,EAAEgB,eAAe,EAAE,CAAC;YAGhF,MAAMf,oBAAoBE,KAAK;gBAAC;oBAAEiB,KAAK;gBAAa;aAAE,EAAE,MAAMC,KAAK,CACjE,CAACC;gBACC,IAAIA,OAAO,OAAOA,QAAQ,YAAY,aAAaA,KAAK;oBACtDC,QAAQL,KAAK,CACX,CAAC,uEAAuE,CAAC,GACvE,AAACI,IAAYE,OAAO,GACpB;gBAEN;gBACA,MAAMF;YACR;QAEJ;IACF;AACF;AAEA,eAAeG,YAAYtB,GAAW;IACpC,MAAMuB,KAAkCT,QACtCA,QAAQtB,OAAO,CAAC,cAAc;QAAEgB,OAAO;YAACR;SAAI;IAAC;IAG/C,2DAA2D;IAC3D,8DAA8D;IAC9D,iBAAiB;IACjB,MAAMwB,eAAeD,GAAGE,cAAc,CACpCzB,KACAuB,GAAGG,GAAG,CAACC,UAAU,EACjB;IAGF,IAAI,CAACH,cAAc;QACjB,gEAAgE;QAChE,4DAA4D;QAC5D,OAAO,CAAC;IACV;IAEA,MAAMI,aAAaL,GAAGM,cAAc,CAACL,cAAcD,GAAGG,GAAG,CAACjC,QAAQ;IAClE,MAAMqC,oBAAoBP,GAAGQ,0BAA0B,CACrDH,WAAWI,MAAM,EACjBT,GAAGG,GAAG,EACN1B;IAGF,OAAO8B,kBAAkBG,OAAO;AAClC;AAEA,OAAO,eAAeC,gBAAgB,EACpCC,cAAc,EACdtB,cAAc,EACdb,GAAG,EAKJ;IACC,IAAIoC,aAAa;IACjB,IAAI;QACF,iDAAiD;QACjD,MAAMxB,sBAAsBZ,KAAKa;QAEjC,MAAMZ,kBAAkB,MAAMqB,YAAYtB;QAC1C,MAAMqC,aAAatC,kBAAkBC,KAAKC;QAE1C,MAAMqC,mBAAmB,MAAM7C,SAAS0C,gBAAgB;QACxD,qEAAqE;QACrE,oDAAoD;QACpD,MAAM,EAAEI,SAAS,EAAE,GAAGzB,QAAQ;QAC9B,MAAM,EAAEE,IAAI,EAAE,GAAG,MAAMuB,UAAUD,kBAAkBD;QAEnD,+CAA+C;QAC/C,IAAIrB,KAAKwB,QAAQ,CAAC,aAAa;YAC7B7C,aAAa0C;YACbD,aAAa;QACf;QAEA,yCAAyC;QACzC,OAAOxC,kBAAkBoB,MAAMxB,QAAQQ,KAAK;IAC9C,EAAE,OAAOe,OAAO;QACd,MAAMA;IACR,SAAU;QACR,IAAIqB,YAAY;YACd1C;QACF;IACF;AACF", "ignoreList": [0]}