{"version": 3, "sources": ["../../../src/server/lib/to-route.ts"], "sourcesContent": ["/**\n * This transforms a URL pathname into a route. It removes any trailing slashes\n * and the `/index` suffix.\n *\n * @param pathname - The URL path that needs to be optimized.\n * @returns - The route\n *\n * @example\n * // returns '/example'\n * toRoute('/example/index/');\n *\n * @example\n * // returns '/example'\n * toRoute('/example/');\n *\n * @example\n * // returns '/'\n * toRoute('/index/');\n *\n * @example\n * // returns '/'\n * toRoute('/');\n */\nexport function toRoute(pathname: string): string {\n  return pathname.replace(/(?:\\/index)?\\/?$/, '') || '/'\n}\n"], "names": ["toRoute", "pathname", "replace"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,OAAO,SAASA,QAAQC,QAAgB;IACtC,OAAOA,SAASC,OAAO,CAAC,oBAAoB,OAAO;AACrD", "ignoreList": [0]}